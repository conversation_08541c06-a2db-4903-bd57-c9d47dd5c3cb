import {ThemeConfig} from "antd"

export const themeConfig: ThemeConfig = {
  components: {
    Layout: {
      headerBg: "#fff",
      headerColor: "#fff",
      siderBg: "#fff",
      bodyBg: "#fff",
      footerPadding: 0,
    },
    Divider: {
      margin: 0,
    },
    Dropdown: {},
    Carousel: {},
    Typography: {
      colorPrimary: "#fff"
    },
    Input: {
      activeBorderColor: "#165DFF",
      hoverBorderColor: "#165DFF",
      lineWidth: 1,
    },
    Alert: {
      colorWarning: "#faad14",
      colorWarningActive: "#faad14",
      colorInfo:"#165DFF",
      colorInfoActive:"#165DFF",
    },
    Card: {
      paddingLG: 16
    },
  },
  token: {
    colorPrimary: '#165DFF',
    borderRadius: 2,
    // paddingSM: "6px",
    // padding: "6px",
  },
  hashed: false,
};