import { DashboardOutlined, DatabaseOutlined, SettingOutlined, UserOutlined } from "@ant-design/icons";
import { ProSettings } from "@ant-design/pro-layout";
import { ICON_URL } from "@/config/data";

const routes = [
  {
    name: "控制台",
    path: "/admin/workspace",
    key: "dashboard",
    icon: <DashboardOutlined />
  },
  {
    name: "接口",
    path: "/admin/interface",
    icon: 'icon-API',
    children: [
      {
        name: "接口分类",
        path: "/admin/api_category",
        icon: 'icon-fenlei'
      },
      {
        name: "接口管理",
        path: "/admin/api",
        icon: 'icon-API',
      },
    ]
  },
  {
    name: "财务",
    path: "/admin/financial",
    icon: 'icon-zidian',
    redirect: "/admin/order",
    children: [
      {
        name: "订单管理",
        path: "/admin/order",
        icon: 'icon-dingdan',
      },
      {
        name: "发票管理",
        path: "/admin/invoice",
        icon: "icon-fapiao"
      },
      {
        name: "兑换码管理",
        path: "/admin/redeem",
        icon: "icon-baobei"
      }
      ,{
        name:"提现管理",
        path:"/admin/affiliate",
        icon:"icon-yaoqingfanli"
      }
    ]
  },
  {
    name: "会员",
    path: "/admin/member",
    icon: <UserOutlined />,
    children: [
      {
        name: "会员管理",
        path: "/admin/user",
        icon: <UserOutlined />,
      },
      {
        name: "会员套餐",
        path: "/admin/plan",
        icon: 'icon-dingdan',
      },
    ]
  },
  {
    name: "站务",
    path: "/admin/site",
    icon: "icon-menhu",
    children: [{
      name: "首页轮播",
      path: "/admin/banner",
      icon: "icon-lunbotu"
    }, {
      name: "页面管理",
      path: "/admin/page",
      icon: "icon-lunbotu"
    },
    {
      name: "菜单管理",
      path: "/admin/menu",
      icon: "icon-lunbotu"
    },
    {
      name: "消息管理",
      path: "/admin/message",
      icon: "icon-lunbotu"
    },
    {
      name: "工单管理",
      path: "/admin/ticket",
      icon: "icon-lunbotu"
    }
    ]
  },
  {
    name: "数据",
    path: "/admin/data",
    icon: "icon-shuju",
    children: [
      {
        name: "请求日志",
        path: "/admin/data/api_request_log",
        icon: "icon-API"
      }
    ]
  },
  {
    name: "系统",
    path: "/admin/system",
    icon: "icon-xitong",
    children: [
      {
        name: "字典管理",
        path: "/admin/dict",
        icon: 'icon-zidian',
      },
      {
        name: "第三方登录配置",
        path: "/admin/socialite",
        icon: 'icon-zidian',
      },
      {
        name: "短信配置",
        path: "/admin/setting/sms",
        icon: 'icon-duanxin',
      },
      {
        name: "设置中心",
        path: "/admin/setting",
        icon: <SettingOutlined />,
      },
    ]
  }
]
const settings: Partial<ProSettings> = {
  fixSiderbar: true,
  layout: 'mix',
  siderMenuType: "sub",
  // splitMenus:true,
  fixedHeader: true,
  iconfontUrl: ICON_URL,
}

export { routes, settings }