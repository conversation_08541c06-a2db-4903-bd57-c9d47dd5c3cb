import { ProSettings } from "@ant-design/pro-layout";
import { ICON_URL } from "@/config/data";

const routes = [
  {
    name: "控制台",
    path: "/dashboard/workspace",
    key: "dashboard",
    icon: 'icon-dashboard',
  },
  {
    name: "数据中心",
    path: "/dashboard/data",
    icon: 'icon-shuju1',
    children: [
      {
        name: "token管理",
        path: "/dashboard/data/token"
      },
      {
        name: "我的接口",
        path: "/dashboard/data/api"
      },
      {
        name: "接口调用日志",
        path: "/dashboard/data/api_log"
      }
    ]
  },
  {
    name: "安全中心",
    path: "/dashboard/secure",
    icon: "icon-anquan",
    children: [
      {
        name: "安全设置",
        path: "/dashboard/secure/index",
      },
      {
        name: "登录日志",
        path: "/dashboard/secure/log",
        icon: "icon-rizhi"
      }
    ]
  },
  {
    name: "账号中心",
    path: "/dashboard/account",
    icon: "icon-geren",
    children: [
      {
        name: "基本信息",
        path: "/dashboard/account/index",
      },
      {
        name: "第三方账号",
        path: "/dashboard/account/third",
      },
      {
        name: "消息通知",
        path: "/dashboard/account/message",
      },
      {
        name: "消息详情",
        path: "/dashboard/account/message/:id",
        hideInMenu: true,
      }
    ]
  },
  {
    name: "工单管理",
    path: "/dashboard/ticket",
    icon: "icon-gongdan",
    children: [
      {
        name: "创建工单",
        path: "/dashboard/ticket/create",
        hideInMenu: true,
      },
      {
        name: "工单详情",
        path: "/dashboard/ticket/:id",
        hideInMenu: true,
      }
    ]
  },
  {
    name: "费用管理",
    path: "/dashboard/cost",
    icon: "icon-caiwuguanli",
    children: [
      {
        name: "订单管理",
        path: "/dashboard/cost/order",
      },
      {
        name: "发票管理",
        path: "/dashboard/cost/invoice",
      },
      {
        name: "发票管理",
        path: "/dashboard/cost/invoice/:id",
        hideInMenu: true,
      },
      {
        name: "发票信息管理",
        path: "/dashboard/cost/invoice/tax",
        hideInMenu: true,
      },
      {
        name: "申请发票",
        path: "/dashboard/cost/invoice/apply",
        hideInMenu: true,
      }
    ]
  },
  {
    name: "礼品兑换",
    path: "gifts",
    key: "gifts",
    icon: "icon-baobei"
  },
  {
    name: "邀请返利🔥",
    path: "/dashboard/affiliate/index",
    icon: "icon-yaoqingfanli",
  },
]
const settings: Partial<ProSettings> = {
  fixSiderbar: true,
  layout: 'mix',
  siderMenuType: "sub",
  // splitMenus:true,
  fixedHeader: true,
  iconfontUrl: ICON_URL,
}

export { routes, settings }