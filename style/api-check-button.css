.api-button {

}

.api-button.arco-btn {
    background-color: #ffffff;
    border-color: #d9d9d9;
    padding: 8px 15px;
    border-radius: 4px;
    font-size: 14px;
    outline: none;
    position: relative;
    display: inline-block;
    font-weight: 400;
    white-space: nowrap;
    text-align: center;
    background-image: none;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    user-select: none;
    touch-action: manipulation;
    line-height: 1.5714285714285714;
    color: rgba(0, 0, 0, 0.88);
}

.api-button:hover {
    border-color: var(--primary-6);
    background: transparent;
}

.api-button-check {
    border: 1px solid rgb(var(--arcoblue-6)) !important;
    color: rgb(var(--arcoblue-6)) !important;
    position: relative !important;
    font-weight: 700 !important;
}

.api-button-check:before {
    content: "" !important;
    position: absolute !important;
    right: 0 !important;
    bottom: 0 !important;
    border-color: transparent rgb(var(--arcoblue-6)) rgb(var(--arcoblue-6)) transparent !important;
    border-style: solid !important;
    border-width: 9px !important;
}

.api-button-check:after {
    content: "" !important;
    width: 2px !important;
    height: 5px !important;
    position: absolute !important;
    right: 3px !important;
    bottom: 5px !important;
    border-color: transparent #fff #fff transparent !important;
    border-style: solid !important;
    border-width: 1px !important;
    transform: rotate(45deg) !important;
}

.price-tag.saved-tag {
    font-size: 12px;
    padding: 0 6px;
    background: linear-gradient(148.8deg, rgb(var(--red-6)) 10%, rgb(var(--red-5)));
}

.price-tag {
    position: absolute;
    top: -8px;
    right: 0;
    color: #fff;
    text-align: center;
    height: 17px;
    opacity: 1;
    border-radius: 8px 0 8px 0;
}