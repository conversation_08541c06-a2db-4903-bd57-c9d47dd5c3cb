/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
        port: '',
        pathname: '**',
      },
    ]
  },
  redirects: function () {
    return [
      {
        source: '/dashboard',
        destination: '/dashboard/workspace',
        permanent: true,
      },
      {
        source: '/admin',
        destination: '/admin/workspace',
        permanent: true,
      },
      {
        source: '/api/view/:id',
        destination: '/api/:id/api_document',
        permanent: true
      }
    ]
  },
  reactStrictMode: false,
  output: "standalone",
  eslint: {
    ignoreDuringBuilds: true
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  experimental:{
    missingSuspenseWithCSRBailout: false,
  }
};

export default nextConfig;
