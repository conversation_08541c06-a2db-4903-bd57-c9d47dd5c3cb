import axios, {AxiosResponse} from "axios";
import {signOut} from 'next-auth/react';
import Cookies from "js-cookie";
import {env} from "@/env";
import {notification} from 'antd';
import {AccessTokenKey} from "@/config/data";

const axiosInstance = axios.create({
  timeout: 30000,
  baseURL: env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:9501'
})
let hasShownUnauthorizedNotification = false;
let hasShownNoPermissionNotification = false;
axiosInstance.interceptors.request.use(async (config) => {
  //请求拦截器
  config.headers["Authorization"] = "Bearer " + Cookies.get(AccessTokenKey)
  return config
})

interface responseTypes<T> {
  code: number,
  message: string,
  data: T,
  success: boolean,
}

axiosInstance.interceptors.response.use((response: AxiosResponse<responseTypes<any>>) => {
  const {data} = response
  if (!data.success) {
    return Promise.reject(new Error(data.message || 'Unknown error'));
  }

  // 返回实际数据
  return data.data;
}, async (error) => {
  if (error.response && error.response.status === 401) {
    if(!hasShownUnauthorizedNotification){
      hasShownUnauthorizedNotification = true
      // 处理未授权的情况，比如重定向到登录页面
      // window.location.href = '/api/auth/signin';
      Cookies.remove(AccessTokenKey)
      notification.warning({
        message: "未登录",
        description:"未登录"
      })
      await signOut({
        callbackUrl: "/",
        redirect: true,
      })
      return;
    }
  }
  if(error.response && error.response.status === 403){
    if(!hasShownNoPermissionNotification){
      hasShownNoPermissionNotification = true
      notification.warning({
        message: "无权限",
        description:"无权限"
      })
      window.location.href = '/'
    }

  }
  // 返回错误信息
  return Promise.reject(error);
})
async function apiRequest<R>(url: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE' = "GET", data?: any,params?:any): Promise<R> {
  const response = await axiosInstance({
    url:url,
    method:method,
    data:data,
    params:params,
  });
  return response as R; // 确保返回类型是 R
}
export {apiRequest};

export default axiosInstance