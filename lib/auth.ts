import { type DefaultSession, getServerSession, type NextAuthOptions, } from "next-auth";
import Credentials from "next-auth/providers/credentials";
// @ts-ignore
import request from "@/lib/request";
import { cookies } from 'next/headers'
import { AccessTokenKey, AffCode<PERSON><PERSON> } from "@/config/data";
import { getUserProfile } from "@/app/actions";

/**
 * Module augmentation for `next-auth` types. Allows us to add custom properties to the `session`
 * object and keep type safety.
 *
 * @see https://next-auth.js.org/getting-started/typescript#module-augmentation
 */
declare module "next-auth" {
  interface Session extends DefaultSession {
    user: {
      id: string;
      accessToken: string,
      info: any,
    } & DefaultSession["user"];
  }

  // interface User {
  //   // ...other properties
  //   // role: UserRole;
  // }
}
export const cookiePrefix = "alapi_"

/**
 * Options for NextAuth.js used to configure adapters, providers, callbacks, etc.
 *
 * @see https://next-auth.js.org/configuration/options
 */
export const authOptions: NextAuthOptions = {
  callbacks: {

    jwt: async ({ token, user, trigger }) => {
      const userData = await getUserProfile()
      if (userData == null) {
        // throw new Error("未登录")
        return null;
      }
      if (user) {
        //@ts-ignore
        token.nickname = user.nickname
        //第一次登录，user 会保存所有信息
        token.name = user.name
        //@ts-ignore
        token.id = user.id
        // @ts-expect-error
        token.accessToken = user?.accessToken
        token.sub = user.id
        // @ts-expect-error
        token.info = user?.info
        //@ts-ignore
        if (user?.is_admin) {
          //@ts-ignore
          token.is_admin = true
        }
      }
      token.name = userData.username || token.name
      token.info = userData
      return token;
    },

    session: ({ session, token }) => {
      return {
        ...session,
        user: {
          email: token?.email,
          name: token?.name,
          info: token?.info,
          id: token?.id,
          accessToken: token?.accessToken,
          image: "",
          is_admin: token?.is_admin
        }
      }
    },
  },
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  providers: [
    /**
     * ...add more providers here.
     *
     * Most other providers require a bit more work than the Discord provider. For example, the
     * GitHub provider requires you to add the `refresh_token_expires_in` field to the Account
     * model. Refer to the NextAuth.js docs for the provider you want to use. Example:
     *
     * @see https://next-auth.js.org/providers/github
     */
    Credentials({
      id: "credentials",
      type: "credentials",
      name: "credentials",
      // You can specify which fields should be submitted, by adding keys to the `credentials` object.
      // e.g. domain, username, password, 2FA token, etc.
      credentials: {
        type: {},
        username: {},
        password: {},
      },
      authorize: async (credentials, req) => {
        try {
          // @ts-ignore
          const ip = req.headers['x-forwarded-for'] || req.connection.remoteAddress
          const clientIP = ip.split(",")[0] ?? ''
          const affCode = cookies().get(AffCodeKey)?.value
          // console.log(req)
          const data = await request.post("/frontend/auth/login", {invite_code:affCode, ip: clientIP, ...credentials })
          //@ts-ignore
          const { userinfo, token, is_admin } = data
          //@ts-ignore
          cookies().set(AccessTokenKey, token, { expires: Date.now() + 30 * 24 * 60 * 60 * 1000 })
          return {
            name: userinfo.username,
            id: userinfo.id,
            email: userinfo.email,
            image: "",
            accessToken: token,
            info: userinfo,
            is_admin: is_admin,
            nickname: userinfo.nickname,
          } as any
        } catch (e) {
          //@ts-ignore
          throw new Error(e.message)
        }
      }
    }),
    Credentials({
      id: "third",
      type: "credentials",
      name: "third",
      // You can specify which fields should be submitted, by adding keys to the `credentials` object.
      // e.g. domain, username, password, 2FA token, etc.
      credentials: {},
      authorize: async (credentials, req) => {
        const { userinfo, token, is_admin } = credentials
        //@ts-ignore
        cookies().set(AccessTokenKey, token, { expires: Date.now() + 30 * 24 * 60 * 60 * 1000 })
        return {
          name: userinfo.username,
          id: userinfo.id,
          email: userinfo.email,
          image: "",
          accessToken: token,
          info: userinfo,
          is_admin: is_admin,
          nickname: userinfo.nickname,
        }
      }
    }),
  ],
  pages: {
    signIn: "/auth/login",
    newUser: "/auth/login"
  }
};

/**
 * Wrapper for `getServerSession` so that you don't need to import the `authOptions` in every file.
 *
 * @see https://next-auth.js.org/configuration/nextjs
 */
export const getServerAuthSession = () => getServerSession(authOptions);
