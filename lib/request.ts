"use server"
import axios, {AxiosResponse} from "axios";
import {env} from "@/env";
import Cookies from "js-cookie";
import {AccessTokenKey} from "@/config/data";
import {cookies} from "next/headers";
import {notification} from "antd";
import {signOut, useSession} from "next-auth/react";
import {session} from "next-auth/core/routes";


const request = axios.create({
  timeout: 30000,
  baseURL: env.NEXT_PUBLIC_API_URL
})

request.interceptors.request.use(async (config) => {
  //请求拦截器
  config.headers["Authorization"] = "Bearer " + cookies().get(AccessTokenKey)?.value

  return config
})

interface responseTypes<T> {
  code: number,
  message: string,
  data: T,
  success: boolean,
}

request.interceptors.response.use((response: AxiosResponse<responseTypes<any>>) => {
  const {data} = response
  if (!data.success) {
    return Promise.reject(new Error(data.message || 'Unknown error'));
  }
  // 返回实际数据
  return data.data;
}, async (error) => {
  // 返回错误信息
  // if (error.response && error.response.status === 401) {
  //   return
  // }

  return Promise.reject(error);
})

async function apiRequest<R>(url: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE' = "GET", data?: any): Promise<R> {
  const response = await request({
    url,
    method,
    data,
  });
  return response as R; // 确保返回类型是 R
}

export {apiRequest};
export default request