//客户端API

import axios from "@/lib/axios";

export async function getCaptcha() {
  return axios.get("/frontend/captcha/captcha")
}

export async function sendSms(data: any) {
  return axios.post("/frontend/auth/send_sms", data)
}

export async function forgetPassword(data: any) {
  return axios.post("/frontend/auth/forget_password", data)
}

export async function getThirdProviders(){
  return axios.get("/frontend/auth/oauth_provider")
}
export async function getThirdCallback(provider:string,data:any){
  return axios.post("/frontend/auth/oauth/callback/"+provider,data)
}
export async function userBindThird(data:any){
  return axios.post("/frontend/auth/oauth/bind_third",data)
}

