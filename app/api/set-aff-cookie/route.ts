import { Aff<PERSON><PERSON><PERSON><PERSON> } from "@/config/data";
import { NextRequest, NextResponse } from "next/server";

// 定义常量
const COOKIE_EXPIRES_SECONDS = 60 * 60 * 24 * 30; // 30天过期（秒数）

export async function GET(request: NextRequest) {
  // 获取URL参数
  const searchParams = request.nextUrl.searchParams;
  const code = searchParams.get('code');
  const redirectUrl = searchParams.get('redirect') || '/';

  // 创建响应对象
  const response = NextResponse.redirect(new URL(redirectUrl, request.url));

  // 设置cookie - 通过服务器端设置
  if (code) {
    // 设置cookie
    response.cookies.set({
      name: AffCodeKey,
      value: code,
      httpOnly: false, // 允许JavaScript访问
      path: "/",
      maxAge: COOKIE_EXPIRES_SECONDS, // 30天
      sameSite: "lax"
    });
  }

  return response;
} 