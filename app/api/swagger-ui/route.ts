import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  let url = searchParams.get('url')
  const {NEXT_PUBLIC_API_URL} = process.env

  if (!url) {
    // return NextResponse.json({ error: '需要提供 OpenAPI 规范的 URL' }, { status: 400 })
    url = `${NEXT_PUBLIC_API_URL}/openapi.json`
  }

  // 返回一个嵌入了 Swagger UI 的 HTML 页面
  const html = `
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8">
    <title>ALAPI Swagger UI</title>
    <link rel="stylesheet" type="text/css" href="https://cdn.bootcdn.net/ajax/libs/swagger-ui/5.18.2/swagger-ui.css" />
    <style>
      html { box-sizing: border-box; overflow: -moz-scrollbars-vertical; overflow-y: scroll; }
      *, *:before, *:after { box-sizing: inherit; }
      body { margin: 0; background: #fafafa; }
      .swagger-ui .topbar { display: none; }
    </style>
  </head>
  <body>
    <div id="swagger-ui"></div>
    <script src="https://cdn.bootcdn.net/ajax/libs/swagger-ui/5.18.2/swagger-ui-bundle.js"></script>
    <script>
      window.onload = function() {
        window.ui = SwaggerUIBundle({
          url: "${url}",
          dom_id: '#swagger-ui',
          deepLinking: true,
          presets: [
            SwaggerUIBundle.presets.apis,
            SwaggerUIBundle.SwaggerUIStandalonePreset
          ],
          layout: "BaseLayout",
          supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch', 'options', 'head'],
          onComplete: function() {
            // 可以在这里添加UI加载完成后的回调
          }
        });
      };
    </script>
  </body>
</html>
  `

  return new NextResponse(html, {
    headers: {
      'Content-Type': 'text/html'
    }
  })
} 