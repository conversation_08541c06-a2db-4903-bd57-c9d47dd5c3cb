import {type NextRequest, NextResponse} from 'next/server'
import {getCurrentUser} from "@/lib/session";
// @ts-ignore
import {env} from "@/env.ts";

async function handler(req: NextRequest, res: NextResponse) {
  const {method, headers} = req;
  const pathname = req.nextUrl.pathname
  const targetUrl = env.API_URL + pathname.replace("/api/backend", "") + req.nextUrl.search;
  const user = await getCurrentUser()
  let contentType = "application/json"
  if (headers.get("content-type")) {
    //兼容上传
    // @ts-ignore
    if (headers.get("content-type").indexOf('multipart/form-data') !== -1) {
      // @ts-ignore
      contentType = headers.get("content-type")
    }
  }

  const fetchOptions: RequestInit = {
    headers: {
      "Authorization": "Bearer " + user?.accessToken ?? '',
      "Content-Type": contentType,//需要设置请求头，要不然不会生效
      ...headers,
    },
    method: method,
    body: req.body,
    redirect: "manual",
    //@ts-ignore
    duplex: "half",
  }

  try {
    const response = await fetch(targetUrl, fetchOptions)

    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
    })

  } catch (e) {
    // @ts-ignore
    return new Response(e, {
      status: 400,
    })
  }
}

// @ts-ignore
export {handler as GET, handler as POST, handler as DELETE, handler as PUT};