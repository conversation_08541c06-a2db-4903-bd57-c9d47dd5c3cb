"use client"
import {
  <PERSON><PERSON>, <PERSON>fig<PERSON>rovider,
  InputNumber,
  message,
  Modal,
  notification,
  QRCode,
  Select,
  Spin,
  Tabs,
  TabsProps,
  Typography
} from "antd";
import { CheckCard } from "@ant-design/pro-card";
import { useEffect, useMemo, useRef, useState } from "react";
import { Api, ApiPrice, OrderStatus, PurchaseApi } from "@/app/types";
import axiosInstance, { apiRequest } from "@/lib/axios";
import { useRouter, useSearchParams } from "next/navigation";
import { useSiteConfig } from "@/components/provider/site-provider";
import Link from "next/link";
import { ICONFONT } from "@/components/antd/icon";
import { getApiDetail } from "@/app/actions";
import { ProApi } from "@/config/data";

interface PriceListProps {
  prices: ApiPrice[],
  priceId: number,
  onPriceChange: (price: ApiPrice) => void
}

const PriceList = (props: PriceListProps) => {
  const [id, setId] = useState<number>(props.priceId ?? 0)
  return (<>
    <CheckCard.Group defaultValue={id} value={id} onChange={(e) => {
      setId(Number(e))
    }} style={{ width: "100%" }} size={'small'}>
      {props.prices?.map((price => (
        <div key={price.id}>
          <CheckCard onClick={() => {
            props.onPriceChange(price)
          }} value={price.id} description={<div className={'flex px-3 w-full justify-between'}>
            <div className={'flex items-center'}>
              <p className={'text-xl font-bold'}>{price.num}次</p>
            </div>
            <div className={'flex items-center leading-9'}>
              <div className={'mr-5'}>
                <p className={'text-xs text-[#999]'}>
                  ￥ {price.unit_price} /次
                </p>
              </div>
              <div>
                <p className={'font-medium'}>￥ <span
                  className={'text-2xl'}>{price.is_trail || price.price == 0 ? '免费' : price.price}</span></p>
              </div>
            </div>
          </div>} className={''} title={null} subTitle={null} style={{ width: "100%", padding: 0 }}
            size={'small'}>
          </CheckCard>
        </div>
      )))}
    </CheckCard.Group>
  </>)
}
export default function Page({ params }: { params: { id: number } }) {

  const siteConfig = useSiteConfig()
  const [api, setApi] = useState<Api>()
  const [isModalOpen, setIsModalOpen] = useState(false);

  let searchParams = useSearchParams();
  const [priceLoading, setPriceLoading] = useState(true)
  const [prices, setPrices] = useState<ApiPrice[]>()
  const [price, setPrice] = useState<ApiPrice>()
  const [priceId, setPriceId] = useState<number>(() => {
    return Number(searchParams.get("price_id")) || 0
  })
  const [buyNum, setBuyNum] = useState(1)

  const [payType, setPayType] = useState('wechat_scan')

  const [payResult, setPayResult] = useState<PurchaseApi>()
  const router = useRouter()
  const [payLoading, setPayLoading] = useState(false)


  useEffect(() => {
    getApiDetail(params.id).then((res) => {
      if (res) {
        setApi(res)
        if (res?.type != ProApi) {
          router.push(`/api/${params.id}/api_document`)
        }
      }
    }).catch((e) => {
      message.warning(e.message)
    })

    apiRequest<ApiPrice[]>(`/frontend/api/prices/${params.id}`).then((res) => {
      setPrices(res)
      console.log(prices)
      if (priceId) {
        for (const re of res) {
          if (re.id === priceId) {
            setPrice(re)
          }
        }
      }
    }).finally(() => setPriceLoading(false))
  }, [params])

  const items: TabsProps['items'] = [
    {
      key: 'package',
      label: '次数包',
      children: <div>
        <Spin spinning={priceLoading}>
          <PriceList prices={prices ?? []} priceId={priceId ?? 0} onPriceChange={(price) => {
            setPrice(price)
          }} />
        </Spin>
      </div>,
    }
  ];

  const getPrice = useMemo(() => {
    if (!price) {
      return 0
    }
    return price?.price * buyNum
  }, [price, buyNum])

  const radios = [
    {
      name: "微信支付",
      icon: <ICONFONT style={{
        height: "24px",
        width: "24px",
      }} size={24} type={'icon-weixinzhifu'} />,
      id: 'wechat_scan',
    },
    {
      name: "支付宝支付",
      icon: <ICONFONT sizes={'24px'} type={'icon-zhifubaozhifu'} size={24} />,
      id: "alipay_scan"
    },
  ]
  const timer = useRef()

  const queryOrderStatus = async (trade_no): Promise<boolean> => {
    const orderStatus = await apiRequest<OrderStatus>("/frontend/purchase/order_status", "POST", {
      trade_no: trade_no,
    })
    if (orderStatus.is_pay) {
      //已支付
      if (timer.current) {
        clearInterval(timer.current)
      }
      setIsModalOpen(false)
      message.success("支付成功");
      //跳转接口测试页面
      setTimeout(() => {
        router.push(`/test/${params.id}`)
      }, 1000)
      return true;
    }
    return false;
  }

  const onSubmit = async () => {
    setPayLoading(true)
    if (timer.current) {
      clearInterval(timer.current)
    }
    try {
      const res = await apiRequest<PurchaseApi>("/frontend/purchase/api", "POST", {
        pay_method: payType,
        api_id: params.id,
        price_id: price?.id,
        num: buyNum,
      })
      setPayResult(res)
      if (res.is_payment) {
        if (res.payment_info?.type === 'qr_url') {
          setIsModalOpen(true)
          //定时器查询订单
          //@ts-ignore
          timer.current = setInterval(async () => {
            await queryOrderStatus(res.order.trade_no)
          }, 3000)

        } else {
          window.location.href = res.payment_info?.url || ''
        }
      } else {
        //直接提示支付完成
        message.success("购买成功")
        router.push(`/test/${params.id}`)
      }
    } catch (e) {
      console.log(e.message)
      message.warning(e.message);
    }
    setPayLoading(false)
  }

  return (
    <div className="z-0 w-full max-w-6xl mx-auto">
      <div className="flex md:flex-row flex-col p-8 min-h-[80vh]">
        {/* 左侧区域 */}
        <div className="md:w-1/2 w-full pr-4">
          <div className="text-2xl flex flex-row">
            <div className={'w-[30px] mr-2 h-[30px]'} style={{
              backgroundImage: `url(${api?.img})`,
              backgroundSize: "contain",
              backgroundColor: "#fff",
              backgroundRepeat: "no-repeat"
            }}>
            </div>
            <p className={'font-bold'}>
              {api?.name}
            </p>
          </div>
          <div className={'mt-2'}>
            <Tabs items={items} />
          </div>
        </div>

        {/* 右侧区域 */}
        <div className="md:w-1/2 md:mt-0 mt-5  w-full pl-4 md:border-l">
          <h2 className="text-xl font-bold">订单信息</h2>
          <div className={'flex flex-col'}>
            <div className={'flex items-center my-4'}>
              <div className={'w-[100px]'}>
                购买数量：
              </div>
              <div className={'flex-1'}>
                <InputNumber width={150} controls={true} value={buyNum} onChange={(v) => {
                  if (!v) {
                    v = 0
                  }
                  setBuyNum(Number(v))
                }} min={1} defaultValue={1} />
              </div>
            </div>
            <div className={'flex items-center'}>
              <div className={'w-[100px]'}></div>
              <div>购买后到期时间是 2025年09月01日</div>
            </div>
            <div className={'flex items-center my-4'}>
              <div className={'w-[100px]'}>
                选择优惠券：
              </div>
              <div>
                <Select className={'w-[200px]'} value={'暂无可用优惠券'} size={'middle'} options={[
                  {
                    label: "暂无可用优惠券",
                    value: "暂无可用优惠券",
                    disabled: true,
                  }
                ]}></Select>
              </div>
            </div>

            <div className={'flex items-center my-4'}>
              <div className={'w-[100px]'}>
                应付金额：
              </div>
              <div>
                <span className="font-bold text-[#ff6a00]">￥ <span className={'text-2xl'}> {getPrice}</span> </span>
              </div>
            </div>

            <div className={'flex my-4'}>
              <div className={'w-[100px]'}>
                支付方式：
              </div>
              <div className={'min-w-[200px]'}>
                <ConfigProvider theme={{
                  token: {
                    padding: 6,
                    paddingSM: 6,
                  }
                }}>
                  <CheckCard.Group value={payType} defaultValue={payType} onChange={(e) => setPayType(String(e))}>
                    <CheckCard bodyStyle={{}} value={'wechat_scan'} style={{
                      width: "100px",
                      paddingBlock: 0,
                      paddingInline: 0,
                    }} className={'w-[100px]'} size={'small'} title={<span><ICONFONT
                      type={"icon-weixinzhifu"} /> <span className={'ml-1'}>微信</span> </span>} />
                    <CheckCard value={'alipay_scan'} style={{
                      width: "100px",
                    }} className={'w-[100px]'} size={'small'} title={<span><ICONFONT
                      type={"icon-zhifubaozhifu"} /> <span className={'ml-1'}>支付宝</span> </span>}></CheckCard>
                  </CheckCard.Group>
                </ConfigProvider>
              </div>
            </div>
          </div>
          <div className={'mt-5'}>
            <Button type="primary" className="mt-4 w-full" onClick={onSubmit} size={'large'}
              disabled={payLoading}>确认支付</Button>

            <div className={'mt-5 text-xs text-[#999]'}>
              <p className={'mb-1'}>购买后可进入 <Link target={"_blank"}
                href={'/dashboard/cost/order'}>订单&发票页面</Link> 申请发票</p>
              <p className={'mb-1'}>虚拟商品，一经售出，概不退换</p>
              <p className={'mb-1'}>购买即视为同意《{siteConfig.site_name} <Link target={"_blank"}
                href={siteConfig.service_agreement || ""}>用户服务协议</Link>》
              </p>
            </div>
          </div>
        </div>

        <Modal width={800} footer={false} onCancel={() => setIsModalOpen(false)} title="支付详情" open={isModalOpen}
          destroyOnClose={true} maskClosable={false}>
          <div className={'mt-5 flex-col flex items-center justify-center'}>
            <div className={'qr_code'}>
              <p>请使用{payType === 'alipay_scan' ? '支付宝' : "微信"}扫一扫, 扫描二维码支付</p>
              <div className={'flex mt-2 justify-center'}>
                <QRCode bordered={false} size={200}
                  icon={payType == 'alipay_scan' ? 'https://t.alipayobjects.com/tfscom/T1Z5XfXdxmXXXXXXXX.png' : ''}
                  value={payResult?.payment_info?.url || ''} />
              </div>
            </div>
            <div className={'mt-5 text-xl'}>
              微信扫码，支付 <span className={'font-bold text-[#ff6a00]'}>{getPrice}</span> 元
            </div>

            <div className={'mt-5 gap-x-5 flex'}>
              <Button type={'primary'} onClick={async () => {
                const is_pay = await queryOrderStatus(payResult?.order.trade_no)
                if (!is_pay) {
                  message.warning("未支付")
                }
              }}>已完成支付</Button>`
              <Button type={'default'} onClick={() => {
                if (timer.current) {
                  clearInterval(timer.current)
                  setIsModalOpen(false)
                }
              }}>取消支付</Button>
            </div>
            <div className={'mt-5 text-center'}>
              <p
                className={'text-[#999]'}>交易完成后，页面将会自动刷新。如果长时间没有响应，您可以手动点击[已完成支付]按钮。</p>
            </div>
          </div>
        </Modal>
      </div>
    </div>

  )
}