import React from "react";
import { Divider } from "antd";
import { getPageContent, getSystemConfig } from "@/app/actions";
import { redirect } from "next/navigation";
import type { Metadata } from "next";
import { SiteConfig } from "@/app/types";

export async function generateMetadata({params}: { params: { slug: string } }): Promise<Metadata> {
  const page = await getPageContent(params.slug)
  if (!page) {
    redirect("/")
  }

  const siteConfig = await getSystemConfig() as SiteConfig
  return {
    title: page.title + " - " + siteConfig.site_name,
    keywords: siteConfig.site_keywords,
    description: (page?.description || '') + "," + siteConfig.site_description,
    applicationName: siteConfig.site_name,
    icons: {
      icon: {
        url: "/images/logo/icon_logo.svg"
      }
    }
  }
}

export default async function Page({params}: { params: { slug: string } }) {
  if (!params.slug) {
    console.log('page not slug')
    redirect("/")
  }
  const page = await getPageContent(params.slug)
  if (!page) {
    redirect("/")
  }

  return (
    <div className="min-h-screen bg-white">
      {/* 标题区域 */}
      <div className="relative">
        {/* 顶部装饰背景 */}
        <div className="absolute inset-x-0 top-0 h-64 bg-gradient-to-b from-blue-50 to-white"></div>
        
        {/* 主标题内容 */}
        <div className="relative px-4 sm:px-6 w-full max-w-5xl mx-auto pt-16 pb-8">
          <div className="text-center">
            <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
              {page?.title || ""}
            </h1>
            {page?.description && (
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                {page.description}
              </p>
            )}
            <div className="mt-6 mb-2 flex justify-center space-x-2 text-sm text-gray-500">
              <span>更新时间: {new Date().toLocaleDateString('zh-CN')}</span>
              <span>·</span>
              <span>阅读时间: 约 {Math.ceil((page.content?.length || 0) / 500)} 分钟</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* 内容区域 */}
      <div className="px-4 sm:px-6 w-full max-w-4xl mx-auto py-8">
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          {/* 内容顶部装饰条 */}
          <div className="h-1 w-full bg-gradient-to-r from-blue-500 to-indigo-600"></div>
          
          {/* 主要内容 */}
          <div className="p-6 sm:p-10">
            <article className="prose prose-lg max-w-none prose-headings:font-semibold prose-headings:text-gray-800 prose-p:text-gray-600 prose-a:text-blue-600 prose-a:no-underline hover:prose-a:underline prose-img:rounded-lg">
              <div dangerouslySetInnerHTML={{__html: page.content}}></div>
            </article>
          </div>
        </div>
        
        {/* 底部信息 */}
        <div className="mt-10 text-center text-sm text-gray-500">
          <p>如有任何问题，请联系我们的客服团队</p>
          <Divider className="my-4 opacity-30" />
          <p>© {new Date().getFullYear()} 版权所有</p>
        </div>
      </div>
    </div>
  );
}