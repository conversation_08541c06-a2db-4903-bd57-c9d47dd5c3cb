import {QuickSearch} from "@/components/common/quick-search";
import {ExploreHead} from "@/components/explore/explore-head";
import React, {Suspense} from "react";
import {ExploreService} from "@/components/explore/explore-service";
import { Metadata } from "next";
import { getSystemConfig } from "@/app/actions";



export async function generateMetadata(
): Promise<Metadata> {
 
  let siteConfig = await getSystemConfig();
  
  // 构建更丰富的 meta 数据
  return {
    title: `API接口_免费API接口_API分类大全API文档_API免费测试 - ${siteConfig.site_name}`,
    description: `API分类大全旨在为广大用户提供API接口分类，涵盖生活服务API、金融科技API、企业工商API、等相关的API接口服务。免费API接口可安全、合规地连接上下游，为数据API应用能力赋能。，包含详细接口文档、在线调试功能。${siteConfig.site_name}提供稳定可靠的API服务。`,
    keywords: `API接口,在线API,${siteConfig.site_name},免费API,免费接口,API文档，API分类大全，生活服务API，金融科技API，企业工商API`,
    openGraph: {
      title: `${siteConfig.site_name}`,
      description: `API分类大全旨在为广大用户提供API接口分类，涵盖生活服务API、金融科技API、企业工商API、等相关的API接口服务。免费API接口可安全、合规地连接上下游，为数据API应用能力赋能。，包含详细接口文档、在线调试功能。${siteConfig.site_name}提供稳定可靠的API服务。`,
      type: 'article',
      
      siteName: siteConfig.site_name,
    },

    category: "免费API接口",
    creator: siteConfig.site_name,
  }
}


export default function Page({
                               searchParams
                             }: {
  searchParams: Record<string, string | undefined>;
}) {
  const {search, keyword,category_id} = searchParams

  return <>
    <div className={'z-0 w-full max-w-6xl mx-auto'}>
      <Suspense fallback={<div>Loading...</div>}>
        <div className='bg-white mb-10'>
          <div className={'mt-[1px]'}>
            <ExploreHead/>
          </div>
          <div className='mb-10 lg:ml-0 lg:mr-0 ml-2 mr-2'>
            <QuickSearch keyword={keyword ?? ''}/>
          </div>
          <div className='mb-10 z-0 '>
            <ExploreService category_id={category_id} keywords={keyword ?? ''} search={search === 'Y'}/>
          </div>
        </div>
      </Suspense>
    </div>
  </>
}