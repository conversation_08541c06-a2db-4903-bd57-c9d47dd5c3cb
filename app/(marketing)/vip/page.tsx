"use client"

import { useEffect, useMemo, useRef, useState } from "react";
import { Plan } from "@/app/(admin)/admin/plan/data";
import { getUserProfile } from "@/app/actions";
import { <PERSON><PERSON>, Card, ConfigProvider, Divider, Input, InputNumber, message, Modal, Popover, QRCode, Select, Spin, Tooltip } from "antd";
import { CheckCard } from "@ant-design/pro-card";
import { ICONFONT } from "@/components/antd/icon";
import Link from "next/link";
import { useSiteConfig } from "@/components/provider/site-provider";
import { apiRequest } from "@/lib/axios";
import { Api, OrderStatus, PurchaseApi } from "@/app/types";
import { useRouter, useSearchParams } from "next/navigation";
import { useSession } from "next-auth/react";
import { useAuthModal } from "@/components/provider/auth-modal-context";
import { User } from "@/app/(admin)/admin/user/data";
import { VipApi } from "@/config/data";

export default function Page() {
  const [planLoading, setPlanLoading] = useState(true)
  const session = useSession()
  const [user, setUser] = useState<User>(null)
  const { openModal } = useAuthModal();
  const [plans, setPlans] = useState<Plan[]>()
  const [plan, setPlan] = useState<Plan | null>()
  const [planModalOpen, setPlanModalOpen] = useState(false)
  const [payType, setPayType] = useState('wechat_scan')
  const [isModalOpen, setIsModalOpen] = useState(false);

  const [priceType, setPriceType] = useState('month')
  const [payLoading, setPayLoading] = useState(false)
  const [newPlanAt, setNewPlanAt] = useState("")

  const [vipApiList, setVipApiList] = useState<Api[]>([])

  const [redeemCode, setRedeemCode] = useState("");

  const [discountLoading, setDiscountLoading] = useState(false);
  const [discountInfo, setDiscountInfo] = useState<{
    code_id: number;
    discount_amount: number;
    final_amount: number;
  } | null>(null);


  const siteConfig = useSiteConfig()

  const PriceCard = ({ type, price, perDay }) => {
    const isSelected = priceType === type;
    const baseClasses = "cursor-pointer w-full p-8 space-y-8 text-center rounded-lg transition-all duration-300";
    const selectedClasses = isSelected
      ? "bg-blue-600 text-white"
      : "border border-gray-200 dark:bg-gray-900";

    return (
      <div
        className={`${baseClasses} ${selectedClasses}`}
        onClick={() => setPriceType(type)}
      >
        <p
          className={`font-medium uppercase text-xl ${isSelected ? 'text-gray-200' : 'text-gray-500 dark:text-gray-300'}`}>
          {type === 'month' ? '一个月' : '一年'}
        </p>
        <h2
          className={`text-3xl font-semibold uppercase ${isSelected ? 'text-white' : 'text-gray-800 dark:text-gray-100'}`}>
          ￥{price}
        </h2>
        <p className={`font-medium ${isSelected ? 'text-gray-200' : 'text-gray-500 dark:text-gray-300'}`}>
          约 {perDay}/天
        </p>
      </div>
    );
  };

  const searchParams = useSearchParams()

  const buyPlan = (plan: Plan) => {
    if (session.status != "authenticated") {
      openModal()
      return;
    }
    setPlan(plan)
    setPlanModalOpen(true)
  }

  const verifyDiscountCode = async () => {
    //添加防抖节流 
    if (!redeemCode) {
      message.warning('请输入优惠码');
      return;
    }

    setDiscountLoading(true);
    try {
      const res = await apiRequest("/frontend/purchase/verify_discount_code", "POST", {
        discount_code: redeemCode,
        type: "plan",
        target_id: plan?.id,
        // 价格
        price: getPrice
      });
      setDiscountInfo(res);
      message.success('优惠码验证成功');
    } catch (err) {
      setDiscountInfo(null);
      message.error(err.message || '验证失败');
    } finally {
      setDiscountLoading(false);
    }
  };

  const getVipApiList = () => {
    apiRequest(`/frontend/api/search`).then((res) => {
      //@ts-ignore
      const { list: apis } = res;
      //@ts-ignore
      const vipProApis = apis.filter(api => api.type === VipApi);
      setVipApiList(vipProApis)
    });
  }

  useEffect(() => {
    apiRequest<Plan[]>("/frontend/index/plans").then(res => {
      setPlans(res)
    }).finally(() => {
      setPlanLoading(false)
    })
  }, []);

  useEffect(() => {
    getUserProfile().then(res => {
      setUser(res)
    })
    getVipApiList()
  }, []);

  const getPrice = useMemo(() => {
    if (!plan) {
      return 0
    }
    if (priceType === 'month') {
      return plan.price_month
    } else {
      return plan.price_year
    }
  }, [plan, priceType])


  useEffect(() => {
    if (user) {
      apiRequest("/frontend/purchase/calculateNewPlanEndAt", "POST", {
        plan_id: plan?.id ?? 0,
        month: priceType === 'month' ? 1 : 12,
      }).then(res => {
        setNewPlanAt(res.plan_end_at ?? '')
      })

      if (redeemCode) {
        verifyDiscountCode();
      }
    }

  }, [user, priceType, plan]);


  const [payResult, setPayResult] = useState<PurchaseApi>()
  const router = useRouter()
  const timer = useRef()

  const queryOrderStatus = async (trade_no): Promise<boolean> => {
    const orderStatus = await apiRequest<OrderStatus>("/frontend/purchase/order_status", "POST", {
      trade_no: trade_no,
    })
    if (orderStatus.is_pay) {
      //已支付
      if (timer.current) {
        clearInterval(timer.current)
      }
      setPlanModalOpen(false)
      setIsModalOpen(false)
      message.success("支付成功");
      //跳转接口测试页面
      if (searchParams.has("callback_url")) {
        router.push(searchParams.get("callback_url") || '#')
      } else {
        router.refresh()
      }
      return true;
    }
    return false;
  }
  const onSubmit = async () => {
    setPayLoading(true)
    try {
      const res = await apiRequest<PurchaseApi>("/frontend/purchase/plan", "POST", {
        plan_id: plan?.id,
        pay_method: payType,
        month: priceType === 'month' ? 1 : 12,
        discount_code: redeemCode,
      })
      setPayResult(res)
      if (res.payment_info?.type === 'qr_url') {
        setIsModalOpen(true)
        //定时器查询订单
        //@ts-ignore
        timer.current = setInterval(async () => {
          await queryOrderStatus(res.order.trade_no)
        }, 3000)

      } else {
        window.location.href = res.payment_info?.url || ''
      }
    } catch (e) {
      message.warning(e.message)
    }
    setPayLoading(false)
  }

  return <div className={'z-0 w-full max-w-6xl mx-auto'}>
    <section className="bg-white dark:bg-gray-900">
      <div className="container px-6 py-8 mx-auto">
        <div className="sm:flex sm:items-center sm:justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-800 lg:text-3xl dark:text-gray-100">会员套餐</h2>
            <p className="mt-4 text-gray-500 dark:text-gray-400">升级会员，百款接口免费用</p>
            {user && <p className="mt-4 text-gray-500 dark:text-gray-400">您当前的会员为：<span
              className='text-red-500 font-bold'>{user?.plan.name}</span> <span className={'ml-2'}>到期时间:  <span
                className={'font-bold'}>{user?.is_free_plan ? '-' : user?.plan_end_at}</span> </span></p>}
          </div>

          <div className="overflow-hidden p-0.5 mt-6 border rounded-lg dark:border-gray-700">
            <div className="sm:-mx-0.5 flex">
              <button onClick={() => setPriceType('month')}
                className={'focus:outline-none px-3 w-1/2 sm:w-auto py-1 sm:mx-0.5 ' + (priceType === 'month' ? ' text-white rounded-lg bg-blue-500' : 'text-gray-800  bg-transparent rounded-lg hover:bg-gray-200')}>月付
              </button>
              <button onClick={() => setPriceType('year')}
                className={'focus:outline-none px-3 w-1/2 sm:w-auto py-1 sm:mx-0.5 ' + (priceType === 'year' ? ' text-white rounded-lg bg-blue-500' : 'text-gray-800  bg-transparent rounded-lg hover:bg-gray-200')}>年付
              </button>
            </div>
          </div>
        </div>
        <Spin spinning={planLoading}>
          <div className="grid gap-6 mt-16 -mx-6 sm:gap-8 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {plans && plans.length > 0 && plans.map(plan => {
              return <div key={plan.id}>
                <div
                  className="px-6 py-4 transition-colors duration-300 transform rounded-lg hover:bg-gray-200 dark:hover:bg-gray-800">
                  <p className="text-lg font-medium text-gray-800 dark:text-gray-100">{plan.name}</p>

                  <h4
                    className="mt-2 text-3xl font-semibold text-gray-800 dark:text-gray-100">￥{priceType === 'month' ? plan.price_month : plan.price_year}<span
                      className="text-base font-normal text-gray-600 dark:text-gray-400">/ {priceType === 'month' ? '月' : '年'}</span>
                  </h4>

                  <p className="mt-4 text-gray-500 dark:text-gray-300">{plan.description}</p>

                  <div className="mt-8 space-y-4 md:min-h-[280px]">
                    <div className="flex justify-between">
                      <span className="mx-4 text-gray-700 dark:text-gray-300">免费接口数量</span>
                      <span>{plan.api_limit == -1 ? '不限' : plan.api_limit}</span>
                    </div>
                    <div className={'flex justify-between'}>
                      <span className="mx-4 text-gray-700 dark:text-gray-300">会员接口</span>
                      <span>
                        {plan.is_free_plan ?
                          <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-red-400" viewBox="0 0 20 20"
                            fill="currentColor">
                            <path fillRule="evenodd"
                              d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z"
                              clipRule="evenodd" />
                          </svg> :
                          <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-blue-500" viewBox="0 0 20 20"
                            fill="currentColor">
                            <path fillRule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                              clipRule="evenodd" />
                          </svg>}
                      </span>
                    </div>
                    <div className={'flex justify-between'}>
                      <span className="mx-4 text-gray-700 dark:text-gray-300">计次接口数量</span>
                      <span>
                        不限
                      </span>
                    </div>

                    <div className="flex justify-between">
                      <span className="mx-4 text-gray-700 dark:text-gray-300 flex items-center">
                        请求次数
                        <Tooltip title={'免费接口和付费接口的调用次数限制'}>
                          <svg className="w-4 h-4 text-gray-800 dark:text-white ml-1" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24">
                            <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                              d="M9.529 9.988a2.502 2.502 0 1 1 5 .191A2.441 2.441 0 0 1 12 12.582V14m-.01 3.008H12M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                          </svg>
                        </Tooltip>
                      </span>
                      <span className="flex items-center">
                        {plan.daily_limit === -1 ? '不限' : `${plan.daily_limit}次/天`}
                        <Popover trigger={['click', 'hover']} content={
                          <div className="max-w-[300px]">
                            {plan.plan_limits && plan.plan_limits.length > 0 ? (
                              <div className="space-y-2">
                                <p className="font-medium">以下接口有单独限制：</p>
                                {plan.plan_limits.map(limit => (
                                  <div key={limit.id} className="text-sm">
                                    <p className="font-medium">{limit.api.name}</p>
                                    <p className="text-gray-500">
                                      {limit.daily_limit == -1 ? '不限次数' : `${limit.daily_limit}次/天`}
                                      {limit.request_qps > 0 && `，${limit.request_qps}QPS`}
                                    </p>
                                  </div>
                                ))}
                              </div>
                            ) : (
                              <p>暂无特殊限制接口</p>
                            )}
                          </div>
                        } title={'特殊接口限制'}>
                          <svg className="w-4 h-4 text-gray-800 dark:text-white ml-1" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24">
                            <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                              d="M9.529 9.988a2.502 2.502 0 1 1 5 .191A2.441 2.441 0 0 1 12 12.582V14m-.01 3.008H12M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                          </svg>
                        </Popover>
                      </span>
                    </div>


                    <div className="flex justify-between">
                      <span className="mx-4 text-gray-700 dark:text-gray-300">QPS限制</span>
                      <span>
                        {plan.request_qps}QPS
                      </span>
                    </div>

                    {!plan.is_free_plan && <div className="flex justify-between">
                      <span className="mx-4 text-gray-700 dark:text-gray-300">会员专属接口</span>
                      <span className="cursor-pointer">
                        <Popover trigger={['click', 'hover']} content={
                          <div className="max-w-[300px]">
                            {vipApiList.length > 0 ? vipApiList.map(api => (
                              <div key={api.id}>
                                <Link target="_blank" className="font-medium" href={`/api/${api.id}/introduction`}>{api.name}</Link>
                              </div>
                            )) : <div>暂无专属接口</div>}
                          </div>
                        } title={'会员专属接口'}>
                          查看列表
                        </Popover>
                      </span>
                    </div>}
                  </div>



                  {plan.is_free_plan ? <>
                    {session.status === "authenticated" ? <Button className={'px-4 py-2 mt-10 font-medium rounded-md w-full'} disabled>注册默认开通</Button> : <Button className={'px-4 py-2 mt-10 font-medium rounded-md w-full'} onClick={() => openModal()}>注册领取</Button>}
                  </> : <button
                    onClick={() => buyPlan(plan)}
                    className="w-full px-4 py-2 mt-10 font-medium tracking-wide text-white capitalize transition-colors duration-300 transform bg-blue-500 rounded-md hover:bg-blue-600 focus:outline-none focus:bg-blue-600">
                    立即开通
                  </button>}

                </div>
              </div>
            })}
          </div>
        </Spin>
      </div>
    </section >
    <div className={'mt-10'}>
      <Card bordered={false}>
        <h3 className={'text-center text-xl font-bold'}>会员规则</h3>
        <div className={'mt-5 space-y-2'}>
          <p className={'text-md font-bold'}>会员权益</p>
          <p>不同的会员套餐有不同的接口限制、请求限制等，具体以实际套餐展示为准</p>
          <p>1.免费接口：免费接口所有套餐都可以申请</p>
          <p>2.会员接口：需要升级付费会员套餐才可以申请</p>
          <p>3.免费接口及会员接口调用量：不同会员套餐实际可用调用量不同，会员等级越高调用量越大</p>
          <p>4.免费接口及会员接口QPS: 不同等级会员QPS不同，根据需求选择对应的会员等级进行购买。</p>
        </div>
        <div className={'mt-5 space-y-2'}>
          <p className={'text-md font-bold'}>会员等级</p>
          <p>1.不同的会员套餐价格不同，支持随意升级,会自动计算新会员的到期天数</p>
          <p>2.低等级升级到高等级会员，会员权益随之升级。</p>
          <p className={'mt-5 font-bold text-red-500'}>
            API接口是计算机软件数字化产品，会员服务一经购买，不支持退款。会员权益如未使用，逾期作废。
          </p>
          <p>如需开具发票，请在控制台 - 财务管理 - 发票管理处申请。</p>
        </div>
      </Card>
    </div>
    <Modal width={800} footer={false} onCancel={() => setIsModalOpen(false)} title="支付详情" open={isModalOpen}
      destroyOnClose={true} maskClosable={false}>
      <div className={'mt-5 flex-col flex items-center justify-center'}>
        <div className={'qr_code'}>
          <p>请使用{payType === 'alipay_scan' ? '支付宝' : "微信"}扫一扫, 扫描二维码支付</p>
          <div className={'flex mt-2 justify-center'}>
            <QRCode bordered={false} size={200}
              icon={payType == 'alipay_scan' ? 'https://t.alipayobjects.com/tfscom/T1Z5XfXdxmXXXXXXXX.png' : ''}
              value={payResult?.payment_info?.url ?? ''} />
          </div>
        </div>
        <div className={'mt-5 text-xl'}>
          微信扫码，支付 <span className={'font-bold text-[#ff6a00]'}>{payResult?.order?.price}</span> 元
        </div>

        <div className={'mt-5 gap-x-5 flex'}>
          <Button type={'primary'} onClick={async () => {
            const is_pay = await queryOrderStatus(payResult?.order.trade_no)
            if (!is_pay) {
              message.warning("未支付")
            }
          }}>已完成支付</Button>`
          <Button type={'default'} onClick={() => {
            if (timer.current) {
              clearInterval(timer.current)
              setIsModalOpen(false)
            }
          }}>取消支付</Button>
        </div>
        <div className={'mt-5 text-center'}>
          <p
            className={'text-[#999]'}>交易完成后，页面将会自动刷新。如果长时间没有响应，您可以手动点击[已完成支付]按钮。</p>
        </div>
      </div>
    </Modal>

    <Modal destroyOnClose={true} className={'round'} width={1000} open={planModalOpen} footer={null} onCancel={() => setPlanModalOpen(false)}
      styles={{
        body: {
          paddingInline: 0,
          paddingBlock: 0,
          background: "#f1f8ff",
        },
        content: {
          boxShadow: '0 0 30px #999',
          background: "#f1f8ff",
        },
      }}>
      <div className={''} style={{ backgroundColor: "#f1f8ff" }}>
        <div className={'header'}>
          <p className={'font-bold text-xl text-center'}>开通{plan?.name}</p>
        </div>
        <div className={'body mt-5 w-full  grid grid-cols-1  md:grid-cols-5 xl:grid-cols-5 gap-x-2'}>
          <Card bordered={false} className={'col-span-3'}>
            <div className="bg-white dark:bg-gray-900">
              <div className="container px-4 mx-auto">
                <div className="grid grid-cols-1 gap-8  xl:gap-12 md:grid-cols-2 lg:grid-cols-2">
                  <PriceCard
                    type="month"
                    price={plan?.price_month ?? '未知'}
                    perDay={(plan?.price_month ? (plan?.price_month / 30).toFixed(2) : '未知')}
                  />
                  <PriceCard
                    type="year"
                    price={`${plan?.price_year ? plan?.price_year : '未知'}`}
                    perDay={(plan?.price_year ? (plan?.price_year / 365).toFixed(2) : '未知')}
                  />
                </div>
              </div>
            </div>
            <p className={'text-center mt-5 text-sm text-[#999]'}>注：购买会员后不支持退款，请谨慎购买</p>
            <Divider />
            <div className={'mt-2'}>
              <p className="mt-4 text-gray-500 dark:text-gray-400">您当前的会员为：<span
                className='text-red-500 font-bold'>{user?.plan.name}</span> <span className={'ml-2'}>到期时间:  <span
                  className={'font-bold'}>{user?.plan_end_at}</span> </span></p>
              <p className={'mt-4 text-gray-500 '}>升级后新到期时间为： <span
                className={'font-bold text-red-500'}>{newPlanAt}</span></p>
              <p className="mt-4 text-gray-500">
                升级不同的套餐，到期时间会根据套餐的单价自动折算最新到期时间
              </p>
            </div>
          </Card>
          <Card bordered={false} className={'col-span-2'}>
            <div>
              <div className={'flex flex-col'}>
                <div className={'flex items-center my-4'}>
                  <div className={'w-[100px]'}>
                    输入优惠码：
                  </div>
                  <div className={'flex gap-2'}>
                    <Input
                      placeholder={'请输入优惠码'}
                      value={redeemCode}
                      onChange={(e) => {
                        setRedeemCode(e.target.value)
                        setDiscountInfo(null)
                      }}
                      allowClear={true}
                    />
                    <Button
                      type="primary"
                      onClick={verifyDiscountCode}
                      loading={discountLoading}
                    >
                      验证
                    </Button>
                  </div>
                </div>

                <div className={'flex my-4'}>
                  <div className={'w-[100px]'}>
                    支付方式：
                  </div>
                  <div className={'flex-wrap flex flex-row items-center flex-1'}>
                    <ConfigProvider theme={{
                      token: {
                        padding: 6,
                        paddingSM: 6,
                      }
                    }}>
                      <CheckCard.Group value={payType} defaultValue={payType} onChange={(e) => setPayType(String(e))}>
                        <CheckCard bodyStyle={{}} value={'wechat_scan'} style={{
                          width: "100px",
                          paddingBlock: 0,
                          paddingInline: 0,
                        }} className={'w-[100px]'} size={'small'} title={<span><ICONFONT
                          type={"icon-weixinzhifu"} /> <span className={'ml-1'}>微信</span> </span>} />
                        <CheckCard value={'alipay_scan'} style={{
                          width: "100px",
                        }} className={'w-[100px]'} size={'small'} title={<span><ICONFONT
                          type={"icon-zhifubaozhifu"} /> <span className={'ml-1'}>支付宝</span> </span>}></CheckCard>
                      </CheckCard.Group>
                    </ConfigProvider>
                  </div>
                </div>
                {/* 在应付金额区域增加对优惠码的处理 */}
                <div className={'flex items-center my-4'}>
                  <div className={'w-[100px]'}>
                    应付金额：
                  </div>
                  <div>
                    {discountInfo ? (
                      <>
                        <span className="font-bold text-[#ff6a00]">
                          ￥ <span className={'text-2xl'}>{discountInfo.final_amount}</span>
                        </span>
                        <span className="ml-2 line-through text-gray-400">￥{getPrice}</span>
                        <span className="ml-2 text-green-500">已优惠￥{discountInfo.discount_amount}</span>
                      </>
                    ) : (
                      <span className="font-bold text-[#ff6a00]">
                        ￥ <span className={'text-2xl'}>{getPrice}</span>
                      </span>
                    )}
                  </div>
                </div>
              </div>
              <div className={'mt-5'}>
                <Button type="primary" className="mt-4 w-full" size={'large'} onClick={onSubmit}
                  disabled={payLoading}>确认支付</Button>

                <div className={'mt-5 text-xs text-[#999]'}>
                  <p className={'mb-1'}>购买后可进入 <Link href={'/dashboard/cost/order'}>订单&发票页面</Link> 申请发票</p>
                  <p className={'mb-1'}>虚拟商品，一经售出，概不退换</p>
                  <p className={'mb-1'}>购买即视为同意 <Link href={siteConfig.service_agreement || ""}>《{siteConfig.site_name} 用户服务协议</Link> 》</p>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </Modal>
  </div >
}