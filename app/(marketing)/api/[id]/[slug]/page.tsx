import {getApiDetail, getSystemCodes, getSystemConfig} from "@/app/actions";
import type {Metadata} from "next";
import Introduction from "@/components/api/api-introduce";
import ApiDocument from "@/components/api/api-document";
import {redirect} from "next/navigation";
import Question from "@/components/api/api-question";
import OpenAPI from "@/components/api/api-openapi";

interface Props {
  params: {
    id: number;
    slug: string;
  }
}

const pages = ["introduction", "api_document"]

export async function generateMetadata(
  {params}: Props,
): Promise<Metadata> {
  const id = params.id
  const api = await getApiDetail(id)
  if (!api) {
    redirect('/')
  }
  let siteConfig = await getSystemConfig();
  
  // 构建更丰富的 meta 数据
  const titleMap = new Map([
    ['introduction', '接口介绍'],
    ['api_document', '接口列表'],
    ['openapi', 'OpenAPI'],
    ['question', '常见问题']
  ]);
  
  const sub_title = titleMap.get(params.slug) || '常见问题';
  return {
    title: `${api.name}-${sub_title}-API文档-API免费测试 - ${siteConfig.site_name}`,
    description: `${api.description}。免费${api.name}API接口，包含详细接口文档、在线调试功能。${siteConfig.site_name}提供稳定可靠的API服务。`,
    keywords: `${api.name},API接口,${api.category?.name},在线API,${siteConfig.site_name},免费${api.name}API,免费${api.name}接口,${api.name}API文档`,
    openGraph: {
      title: `${api.name} - ${siteConfig.site_name}`,
      description: api.description,
      type: 'article',
      images: [
        {
          url: api.img,
          width: 1200,
          height: 630,
          alt: api.name,
        }
      ],
      siteName: siteConfig.site_name,
    },
    twitter: {
      card: 'summary_large_image',
      title: `${api.name} - ${siteConfig.site_name}`,
      description: api.description,
      images: [api.img],
    },
    category: api.category?.name,
    creator: siteConfig.site_name,

  }
}


export default async function Page({params}: Props) {

  const api = await getApiDetail(params.id)
  if (!api) {
    redirect('/')
  }

  const systemCodes = await getSystemCodes()

  return <div>
    {params.slug === 'introduction' && <div><Introduction introduction={api?.introduce ?? ''}/></div>}
    {params.slug === 'api_document' &&
      <div><ApiDocument systemCodes={systemCodes} id={api.id} nodes={api.nodes}/></div>}
    {params.slug === 'question' && <div><Question/></div>}
    {params.slug === 'openapi' && <div><OpenAPI id={api.id}/></div>}
  </div>
}