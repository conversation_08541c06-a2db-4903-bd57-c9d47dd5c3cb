import ApiDetailHead from "@/components/api/api-detail-head";
import React from "react";
import ApiDetailRight from "@/components/api/api-detail-right";
import ApiDetailTab from "@/components/api/api-detail-tab";
import {getApiDetail} from "@/app/actions";
import {redirect} from "next/navigation";
import {getCurrentUser} from "@/lib/session";

export default async function RootLayout({
                                           children,
                                           params,
                                         }: {
  children: React.ReactNode;
  params: { id: number },
}) {

  const api = await getApiDetail(params.id)
  if (!api) {
    redirect("/")
  }
  const user = await getCurrentUser();

  return <>
    <div className={'z-0 w-full max-w-6xl mx-auto'}>
      <div className='mt-5'>
        <div className='max-w-6xl m-auto flex flex-col'>
          <div className='flex w-full'>
            <div className='pt-5 api-detail-left flex-1 min-w-0'>
              <ApiDetailHead api={api}/>
              <div className="hidden lg:block ">
                <div
                  className=" pt-4 pb-5 px-[145px]  flex justify-between  text-xs border-dashed border-t text-[#666]">
                  <div><span className="mr-[10px]">·</span>服务保障</div>
                  <div><span className="mr-[10px]">·</span>未使用部分七天无理由退款</div>
                  <div><span className="mr-[10px]">·</span>正规企业商品来源</div>
                  <div><span className="mr-[10px]">·</span>交易流程全程监控</div>
                </div>
              </div>
              {/* api tab */}
              <div className={'w-full overflow-x-hidden'}>
                <ApiDetailTab api={api} id={params.id}/>
              </div>
              {/*api detail*/}
              <div className={'w-full  overflow-x-hidden'}>
                {children}
              </div>
            </div>
            <div className='lg:block hidden w-[290px] pt-5 pl-5 api-detail-right border-l'>
              <ApiDetailRight/>
            </div>
          </div>
        </div>
      </div>

    </div>
  </>
}