import { Content, Footer, Header, Layout } from "@/components/antd/layout";
import { MarketingHeader } from "@/components/layout/marketing-header";
import React from "react";
import { MarketingFooter } from "@/components/layout/marketing-footer";
import { getCurrentUser } from "@/lib/session";
import { AuthModalProvider } from "@/components/provider/auth-modal-context";
import AuthLoginModal from "@/components/common/auth-login-modal";
import { getMenuList } from "@/app/actions";
import { convertToMenuItems } from "@/utils/helper";

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {

  const user = await getCurrentUser();
  const menuList = await getMenuList("top")
  const bottomMenuList = await getMenuList("bottom")
  const linkExchanges = await getMenuList("link_exchange")

  return (<>
    <div>
      <AuthModalProvider>
        <Layout className={'bg-white'}>
          <Header className={'bg-white z-50 p-0'} style={{ padding: 0 }}>
            <MarketingHeader user={user} menuList={convertToMenuItems(menuList || [])} />
          </Header>
          <Content className={''}>{children}</Content>
          <Footer className='footer mt-10 z-0'>
            <MarketingFooter linkExchanges={linkExchanges} bottomMenuList={bottomMenuList || []} />
          </Footer>
        </Layout>
        <AuthLoginModal />
      </AuthModalProvider>
    </div>
  </>
  );
}
