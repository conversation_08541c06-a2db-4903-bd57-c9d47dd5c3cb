import React from "react";
import { IndexCarousel } from "@/components/index/index-carousel";
import { QuickSearch } from "@/components/common/quick-search";
import IndexShowCard from "@/components/index/index-show-card";
import { getBanner, getIndexApis, getIndexCategories, getSystemConfig } from "@/app/actions";
import { Metadata } from "next";


export async function generateMetadata(): Promise<Metadata> {
  const siteConfig = await getSystemConfig()
  return {
    title: `API接口_免费API接口_API数据接口_API接口大全_API接口免费测试 - ${siteConfig.site_name}`,
    description: `${siteConfig.site_name}接口大全旨在为广大用户提供API接口分类，涵盖生活服务API、金融科技API、企业工商API、等相关的API接口服务。免费API接口可安全、合规地连接上下游，为数据API应用能力赋能。，包含详细接口文档、在线调试功能。${siteConfig.site_name}提供稳定可靠的API服务。`,
    keywords: `API接口,在线API,${siteConfig.site_name},免费API,免费接口,API文档，API分类大全，生活服务API，金融科技API，企业工商API`,
    category: "免费API接口",
    creator: siteConfig.site_name,
    applicationName: siteConfig.site_name,
    authors: [{ name: siteConfig.site_name }],
    generator: siteConfig.site_name,
    openGraph: {
      title: `${siteConfig.site_name}`,
      description: `API分类大全旨在为广大用户提供API接口分类，涵盖生活服务API、金融科技API、企业工商API、等相关的API接口服务。免费API接口可安全、合规地连接上下游，为数据API应用能力赋能。，包含详细接口文档、在线调试功能。${siteConfig.site_name}提供稳定可靠的API服务。`,
      type: 'article',
      siteName: siteConfig.site_name,
    },
  }
}


export default async function Page() {
  const banners = await getBanner()
  const sideCategories = await getIndexCategories()
  const apis = await getIndexApis()
  return (
    <div className={'z-0 w-full max-w-6xl mx-auto'}>

      <div className='mt-5'>
        <IndexCarousel
          banners={banners}
          categories={sideCategories}
        />
      </div>
      <div className="mt-10 lg:ml-0 lg:mr-0 ml-2 mr-2">
        <QuickSearch />
      </div>
      {apis.map((api) => (
        <div key={"api-index-" + api.id} className='mt-10'>
          <IndexShowCard api={api} />
        </div>
      ))}
    </div>
  );
}
