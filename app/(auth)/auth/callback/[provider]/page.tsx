"use client"

import {useEffect, useState} from "react";
import {getThirdCallback} from "@/lib/api";
import {useSearchParams} from "next/navigation";
import {message} from "antd";

export default function Page({params}:{params:{provider:string}}){
  let searchParams = useSearchParams();
  const [messageSent, setMessageSent] = useState(false);

  useEffect(() => {
    if(messageSent){
      return
    }
    getThirdCallback(params.provider,{
      state:searchParams.get("state"),
      code:searchParams.get("code"),
    }).then((data)=>{
      setTimeout(function (){
        if (!messageSent) {
          window.opener.postMessage(data, '*');
          setMessageSent(true); // 更新状态以防止重复发送
        }
        window.close();
      },1000)
    }).catch(e=>{
      message.error(e.message)
    })
  }, [messageSent,params, searchParams]);

  return <div>
    登录中...
  </div>
}