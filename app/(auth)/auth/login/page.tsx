"use client"
import {
  ProCard,
} from "@ant-design/pro-components";
import {useSiteConfig} from "@/components/provider/site-provider";

import Link from "next/link";
import LoginFormCommon from "@/components/common/login-form";
import {useSession} from "next-auth/react";
import {useRouter} from "next/navigation";

export default function Page() {
  const session = useSession()
  const router = useRouter()

  if (session.status === 'authenticated') {
    router.push("/dashboard")
  }

  return <div style={{
    background: "url(/image/login-bg.png) center no-repeat #F6FAFD",
  }}>
    <div style={{
      position: "absolute",
      top: "20px",
      left: "30px"
    }}>
      <Link href={'/'}><img src={useSiteConfig().site_logo} className={'h-[30px]'}/></Link>
    </div>
    <div className={' min-h-screen flex items-center justify-center'}>
      <div className={'p-6 w-[550px] '}>
        <div className={'text-center'}>
          <ProCard boxShadow className={'mt-6'}>
            <div>
              <h3 className={'text-2xl'}>登录账号</h3>
            </div>
            <LoginFormCommon/>
          </ProCard>
        </div>
      </div>
    </div>
  </div>
}