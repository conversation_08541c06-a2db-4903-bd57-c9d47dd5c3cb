import type { MetadataRoute } from 'next'
import axiosInstance from "@/lib/axios"

// 缓存数据结构
interface CacheData {
  data: any
  timestamp: number
}

// 内存缓存对象
const cache: {
  exploreData?: CacheData
} = {}

// 缓存过期时间(10分钟)
const CACHE_TTL = 10 * 60 * 1000 

async function getExploreData() {
  try {
    // 检查缓存是否存在且未过期
    if (
      cache.exploreData && 
      Date.now() - cache.exploreData.timestamp < CACHE_TTL
    ) {
      return cache.exploreData.data
    }

    // 缓存不存在或已过期,重新请求数据
    const res = await axiosInstance.get('/frontend/api/explore')
    
    // 更新缓存
    cache.exploreData = {
      data: res,
      timestamp: Date.now()
    }

    return res
  } catch (e) {
    // 发生错误时返回空数据
    return {
      categories: [],
      apis: []
    }
  }
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
    // 获取所有分类和API数据
    const { categories, apis } = await getExploreData()

    // 基础URL列表
    const routes = [
        {
            url: 'https://www.alapi.cn',
            lastModified: new Date(),
            changeFrequency: 'daily',
            priority: 1
        },
        {
            url: 'https://www.alapi.cn/explore',
            lastModified: new Date(),
            changeFrequency: 'daily',
            priority: 0.8
        },
        {
            url: 'https://www.alapi.cn/api/swagger-ui',
            lastModified: new Date(),
            changeFrequency: 'daily',
            priority: 0.8
        }
    ]

    // 添加分类页URL
    const categoryUrls = categories.map((category: any) => ({
        url: `https://www.alapi.cn/explore?category_id=${category.id}`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 1
    }))

    // 添加API详情页URL 
    const apiUrls = apis.flatMap((categoryApi: any) =>
        categoryApi.apis.map((api: any) => ({
            url: `https://www.alapi.cn/api/${api.id}/introduction`,
            lastModified: new Date(api.updated_at || new Date()),
            changeFrequency: 'daily',
            priority: 1
        }))
    )

    // 添加API文档页URL 
    const apiDocumentUrls = apis.flatMap((categoryApi: any) =>
        categoryApi.apis.map((api: any) => ({
            url: `https://www.alapi.cn/api/${api.id}/api_document`,
            lastModified: new Date(api.updated_at || new Date()),
            changeFrequency: 'daily',
            priority: 1
        }))
    )

    const apiOpenApiUrls = apis.flatMap((categoryApi: any) =>
      categoryApi.apis.map((api: any) => ({
          url: `https://www.alapi.cn/api/${api.id}/openapi`,
          lastModified: new Date(api.updated_at || new Date()),
          changeFrequency: 'daily',
          priority: 1
      }))
  )

    return [...routes, ...categoryUrls, ...apiUrls, ...apiDocumentUrls, ...apiOpenApiUrls]
}