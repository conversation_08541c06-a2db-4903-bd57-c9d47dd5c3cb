import type {<PERSON>ada<PERSON>} from "next";
import {Inter} from "next/font/google";
import "./globals.css";

const inter = Inter({subsets: ["latin"]});
import {AntdRegistry} from '@ant-design/nextjs-registry';
import {ConfigProvider} from "antd";
import {themeConfig} from "@/config/theme_config";
import {StyleProvider} from "@/components/antd/layout";
import {SiteProvider} from "@/components/provider/site-provider";
import {getSystemConfig} from "@/app/actions";
import {SiteConfig} from "@/app/types";
import NextAuthProvider from "@/components/provider/next-auth-provider";
import dynamic from "next/dynamic";

const DynamicScript = dynamic(() => import('@/components/common/dynamic-script'), {ssr: false})

export async function generateMetadata(): Promise<Metadata> {

  const siteConfig = await getSystemConfig() as SiteConfig
  return {
    title: siteConfig.site_name + ' - ' + siteConfig.site_title,
    keywords: siteConfig.site_keywords,
    description: siteConfig.site_description,
    applicationName:siteConfig.site_name,
    icons:{
      icon:{
        url:"/images/logo/icon_logo.svg"
      }
    }
  }
}

export default async function RootLayout({
                                           children,
                                         }: Readonly<{
  children: React.ReactNode;
}>) {
  const siteConfig = await getSystemConfig() as SiteConfig

  return (
    <html lang="en" className={'light'} suppressHydrationWarning={true}>
    <body className={inter.className}>
    <StyleProvider layer>
      <ConfigProvider theme={themeConfig}>
        <AntdRegistry>
          <NextAuthProvider>
          <SiteProvider siteConfig={siteConfig}>
            {children}
          </SiteProvider>
          </NextAuthProvider>
        </AntdRegistry>
      </ConfigProvider>
    </StyleProvider>
    <DynamicScript siteConfig={siteConfig}/>
    </body>
    </html>
  );
}