import axiosInstance from "@/lib/axios";

// 获取用户推荐设置和余额信息
export async function getAffiliateInfo() {
    return axiosInstance.get("/frontend/dashboard/affiliate/status");
}

// 开启邀请返利功能（生成推荐码）
export async function enableAffiliate() {
    return axiosInstance.post("/frontend/dashboard/affiliate/enable");
}

// 获取推荐用户列表
export async function getReferredUsers(params: { page: number; pageSize: number }) {
    return axiosInstance.get("/frontend/dashboard/affiliate/referred_users", { params });
}

// 获取推荐奖励记录
export async function getReferralRewards(params: { page: number; pageSize: number }) {
    return axiosInstance.get("/frontend/dashboard/affiliate/reward_records", { params });
}

// 获取奖励收入记录
export async function getIncomeRecords(params: { page: number; pageSize: number }) {
    return axiosInstance.get("/frontend/dashboard/affiliate/income_records", { params });
}

// 申请提现
export async function withdrawReferralBalance(data: {
    amount: number;
    withdraw_type: 'alipay' | 'wallet';
    account_name?: string;
    account_no?: string;
}) {
    return axiosInstance.post("/frontend/dashboard/affiliate/withdraw", data);
}

// 获取提现记录
export async function getWithdrawRecords(params: { page: number; pageSize: number }) {
    return axiosInstance.get("/frontend/dashboard/affiliate/withdraw_records", { params });
}

// 切换奖励类型
export async function switchRewardType(rewardType: 'amount' | 'plan') {
    return axiosInstance.post("/frontend/dashboard/affiliate/switch_reward_type", { reward_type: rewardType });
} 

//领取会员奖励
export async function claimPlanReward(reward_id: number) {
    return axiosInstance.post("/frontend/dashboard/affiliate/claim_plan_reward", { reward_id });
}

// 设置是否自动领取会员奖励
export async function setAutoClaimPlan(enable: boolean) {
    return axiosInstance.post("/frontend/dashboard/affiliate/auto_claim_plan", { auto_claim_plan: enable });
}