"use client"

import { Empty } from "antd";
import { forwardRef, useImperativeHandle } from "react";
import { getIncomeRecords } from "../api";
import { PaginatedResponse, IncomeRecord } from "./types";
import { ProTable } from "@ant-design/pro-components";
import type { ActionType, ProColumns } from "@ant-design/pro-components";

interface IncomeRecordsTableProps {
    initialLoaded?: boolean;
    actionRef?: React.MutableRefObject<ActionType | undefined>;
}

export default forwardRef<ActionType, IncomeRecordsTableProps>(function IncomeRecordsTable({ initialLoaded = false, actionRef }, ref) {
    
    // 暴露 actionRef 给外部组件
    useImperativeHandle(ref, () => actionRef?.current as ActionType);

    // 收入记录列定义
    const columns: ProColumns<IncomeRecord>[] = [
        {
            title: "收入金额",
            dataIndex: "amount",
            key: "amount",
            render: (_, record) => `￥${record.amount.toFixed(2)}`,
        },
        {
            title: "收入前余额",
            dataIndex: "before_balance",
            key: "before_balance",
            render: (_, record) => `￥${record.before_balance.toFixed(2)}`,
        },
        {
            title: "收入后余额",
            dataIndex: "after_balance",
            key: "after_balance",
            render: (_, record) => `￥${record.after_balance.toFixed(2)}`,
        },
        {
            title: "备注",
            dataIndex: "remark",
            key: "remark",
        },
        {
            title: "时间",
            dataIndex: "created_at",
            key: "created_at",
        },
    ];

    return (
        <ProTable<IncomeRecord>
            columns={columns}
            rowKey="id"
            actionRef={actionRef}
            search={false}
            request={async (params) => {
                const { current, pageSize } = params;
                try {
                    const res = await getIncomeRecords({ 
                        page: current || 1, 
                        pageSize: pageSize || 10 
                    }) as unknown as PaginatedResponse<IncomeRecord>;
                    
                    return {
                        data: res.data || [],
                        success: true,
                        total: res.total || 0,
                    };
                } catch (e: any) {
                    console.error("获取奖励收入记录失败", e);
                    return {
                        data: [],
                        success: false,
                        total: 0,
                    };
                }
            }}
            pagination={{
                pageSize: 10,
            }}
            locale={{
                emptyText: <Empty description="暂无收入记录" />,
            }}
            scroll={{ x: 400 }}
        />
    );
}) 