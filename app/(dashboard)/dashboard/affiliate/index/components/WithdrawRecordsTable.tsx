"use client"

import { Empty, Tag } from "antd";
import { forwardRef, useImperativeHandle } from "react";
import { getWithdrawRecords } from "../api";
import { PaginatedResponse, WithdrawRecord } from "./types";
import { ProTable } from "@ant-design/pro-components";
import type { ActionType, ProColumns } from "@ant-design/pro-components";

interface WithdrawRecordsTableProps {
    initialLoaded?: boolean;
    actionRef?: React.MutableRefObject<ActionType | undefined>;
}

export default forwardRef<ActionType, WithdrawRecordsTableProps>(function WithdrawRecordsTable({ initialLoaded = false, actionRef }, ref) {
    
    // 暴露 actionRef 给外部组件
    useImperativeHandle(ref, () => actionRef?.current as ActionType);

    // 提现记录列定义
    const columns: ProColumns<WithdrawRecord>[] = [
        {
            title: "申请时间",
            dataIndex: "created_at",
            key: "created_at",
        },
        {
            title: "提现金额",
            dataIndex: "amount",
            key: "amount",
            render: (_, record) => `￥${record.amount.toFixed(2)}`,
        },
        {
            title: "手续费",
            dataIndex: "fee",
            key: "fee",
            render: (_, record) => `￥${record.fee.toFixed(2)}`,
        },
        {
            title: "实际到账",
            dataIndex: "actual_amount",
            key: "actual_amount",
            render: (_, record) => `￥${record.actual_amount.toFixed(2)}`,
        },
        {
            title: "提现方式",
            dataIndex: "withdraw_type",
            key: "withdraw_type",
            render: (_, record) => (
                record.withdraw_type === "alipay" ? "支付宝" : "钱包余额"
            ),
        },
        {
            title: "状态",
            dataIndex: "status",
            key: "status",
            render: (_, record) => {
                if (record.status === "completed") return <Tag color="success">已完成</Tag>;
                if (record.status === "approved") return <Tag color="processing">已审核</Tag>;
                if (record.status === "rejected") return <Tag color="error">已拒绝</Tag>;
                return <Tag color="default">待审核</Tag>;
            },
        },
        {
            title: "拒绝原因",
            dataIndex: "reject_reason",
            key: "reject_reason",
            render: (_, record) => record.reject_reason || '-',
        },
    ];

    return (
        <ProTable<WithdrawRecord>
            columns={columns}
            rowKey="id"
            actionRef={actionRef}
            search={false}
            request={async (params) => {
                const { current, pageSize } = params;
                try {
                    const res = await getWithdrawRecords({ 
                        page: current || 1, 
                        pageSize: pageSize || 10 
                    }) as unknown as PaginatedResponse<WithdrawRecord>;
                    
                    return {
                        data: res.data || [],
                        success: true,
                        total: res.total || 0,
                    };
                } catch (e: any) {
                    console.error("获取提现记录失败", e);
                    return {
                        data: [],
                        success: false,
                        total: 0,
                    };
                }
            }}
            pagination={{
                pageSize: 10,
            }}
            locale={{
                emptyText: <Empty description="暂无提现记录" />,
            }}
            scroll={{ x: 400 }}
        />
    );
}) 