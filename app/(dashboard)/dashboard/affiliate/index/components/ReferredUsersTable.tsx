"use client"

import { Empty } from "antd";
import { useEffect, forwardRef, useImperativeHandle } from "react";
import { getReferredUsers } from "../api";
import { PaginatedResponse, ReferredUser } from "./types";
import { ProTable } from "@ant-design/pro-components";
import type { ActionType, ProColumns } from "@ant-design/pro-components";

interface ReferredUsersTableProps {
    initialLoaded?: boolean;
    actionRef?: React.MutableRefObject<ActionType | undefined>;
}

export default forwardRef<ActionType, ReferredUsersTableProps>(function ReferredUsersTable({ initialLoaded = false, actionRef }, ref) {

    // 暴露 actionRef 给外部组件
    useImperativeHandle(ref, () => actionRef?.current as ActionType);

    // 推荐用户列表列定义
    const columns: ProColumns<ReferredUser>[] = [
        {
            title: "用户名",
            dataIndex: ["ref_user", "nickname"],
            key: "username",
        },
        {
            title: "手机号",
            dataIndex: ["ref_user", "phone"],
            key: "phone",
        },
        {
            title: "注册时间",
            dataIndex: "created_at",
            key: "created_at",
        },
        {
            title: "状态",
            dataIndex: "status",
            key: "status",
            valueEnum: {
                paid: { text: '已付费', status: 'Success' },
                registered: { text: '已注册', status: 'Default' },
            },
        },
    ];

    return (
        <ProTable<ReferredUser>
            columns={columns}
            rowKey="id"
            actionRef={actionRef}
            search={false}
            request={async (params) => {
                const { current, pageSize } = params;
                try {
                    const res = await getReferredUsers({ 
                        page: current || 1, 
                        pageSize: pageSize || 10 
                    }) as unknown as PaginatedResponse<ReferredUser>;
                    
                    return {
                        data: res.data || [],
                        success: true,
                        total: res.total || 0,
                    };
                } catch (e: any) {
                    console.error("获取推荐用户列表失败", e);
                    return {
                        data: [],
                        success: false,
                        total: 0,
                    };
                }
            }}
            pagination={{
                pageSize: 10,
            }}
            locale={{
                emptyText: <Empty description="暂无推荐用户" />,
            }}
            scroll={{ x: 400 }}
        />
    );
}) 