"use client"

import { Button, Empty, message, Space, Tag } from "antd";
import { forwardRef, useImperativeHandle, useState } from "react";
import { getReferralRewards, claimPlanReward as claimReward } from "../api";
import { PaginatedResponse, ReferralReward } from "./types";
import { ProTable } from "@ant-design/pro-components";
import type { ActionType, ProColumns } from "@ant-design/pro-components";

interface ReferralRewardsTableProps {
    initialLoaded?: boolean;
    actionRef?: React.MutableRefObject<ActionType | undefined>;
}

export default forwardRef<ActionType, ReferralRewardsTableProps>(function ReferralRewardsTable({ initialLoaded = false, actionRef }, ref) {
    const [claimLoading, setClaimLoading] = useState<{[key: number]: boolean}>({});
    
    // 暴露 actionRef 给外部组件
    useImperativeHandle(ref, () => actionRef?.current as ActionType);

    // 领取会员奖励
    const handleClaimPlanReward = async (id: number) => {
        // 防止重复点击
        if (claimLoading[id]) return;
        
        try {
            setClaimLoading(prev => ({...prev, [id]: true}));
            await claimReward(id);
            message.success("会员奖励领取成功");
            
            // 刷新表格数据
            actionRef?.current?.reload();
        }
        catch (e: any) {
            message.error(e.message || "领取失败，请稍后再试");
        } finally {
            setClaimLoading(prev => ({...prev, [id]: false}));
        }
    }

    // 推荐奖励记录列定义
    const columns: ProColumns<ReferralReward>[] = [
        {
            title: "订单信息",
            dataIndex: ["order", "id"],
            key: "order.id",
            render: (_, record) => {
                // 处理价格显示
                const price = typeof record.order.price === 'string'
                    ? parseFloat(record.order.price)
                    : record.order.price;

                return (
                    <div className="bg-gray-50 p-3 rounded-lg">
                        <div className="font-medium text-gray-900 mb-1">{record.order.title}</div>
                        <div className="text-sm text-gray-600">
                            <span className="text-green-600 font-medium">
                                ￥{isNaN(price) ? '0.00' : price.toFixed(2)}
                            </span>
                        </div>
                    </div>
                )
            }
        },
        {
            title: "用户",
            dataIndex: ["ref_user", "nickname"],
            key: "username",
        },
        {
            title: "奖励类型",
            dataIndex: "reward_type",
            key: "reward_type",
            render: (_, record) => (
                record.reward_type === 'amount' ?
                    <Tag color="green">￥{record.amount.toFixed(2)}</Tag> :
                    <Tag color="blue">{record.plan?.name} - {record.plan_days}天</Tag>
            ),
        },
        {
            title: "时间",
            dataIndex: "created_at",
            key: "created_at",
        },
        {
            title: "状态",
            dataIndex: "status",
            key: "status",
            render: (_, record) => {
                if (record.status === "completed") {
                    return <Tag color="success">已发放</Tag>;
                }
                
                if (record.reward_type === 'plan' && record.plan) {
                    return (
                        <Space>
                            <Tag color="processing">待领取</Tag>
                            <Button 
                                type="link" 
                                size="small" 
                                loading={claimLoading[record.id]} 
                                onClick={() => handleClaimPlanReward(record.id)}
                            >
                                领取
                            </Button>
                        </Space>
                    );
                }
                
                return <Tag color="processing">待发放</Tag>;
            },
        },
    ];

    return (
        <ProTable<ReferralReward>
            columns={columns}
            rowKey="id"
            actionRef={actionRef}
            search={false}
            request={async (params) => {
                const { current, pageSize } = params;
                try {
                    const res = await getReferralRewards({ 
                        page: current || 1, 
                        pageSize: pageSize || 10 
                    }) as unknown as PaginatedResponse<ReferralReward>;
                    
                    return {
                        data: res.data || [],
                        success: true,
                        total: res.total || 0,
                    };
                } catch (e: any) {
                    console.error("获取推荐奖励记录失败", e);
                    message.error("获取奖励记录失败");
                    return {
                        data: [],
                        success: false,
                        total: 0,
                    };
                }
            }}
            pagination={{
                pageSize: 10,
            }}
            locale={{
                emptyText: <Empty description="暂无奖励记录" />,
            }}
            scroll={{ x: 400 }}
        />
    );
}) 