// 通用分页响应接口
export interface PaginatedResponse<T> {
    data: T[];
    total: number;
    pageInfo: {
        total: number;
        current: number;
        totalPage: number;
    }
}

// 推荐用户接口
export interface ReferredUser {
    id: number;
    user_id: number;
    referred_user_id: number;
    code: string;
    status: 'registered' | 'paid';
    created_at: string;
    updated_at: string;
    ref_user: {
        nickname: string;
        phone: string;
    };
}

// 推荐奖励记录接口
export interface ReferralReward {
    id: number;
    user_id: number;
    referred_user_id: number;
    order_id: number;
    reward_type: 'amount' | 'plan';
    amount: number;
    plan_days: number;
    plan_id: number;
    status: 'pending' | 'completed';
    created_at: string;
    updated_at: string;
    ref_user: {
        nickname: string;
        phone: string;
    };
    order: {
        title: string;
        price: number;
        id: number;
    }
    plan?: {
        name: string;
        id: number
    }
}

// 收入记录接口
export interface IncomeRecord {
    id: number;
    user_id: number;
    amount: number;
    referral_reward_id: number;
    before_balance: number;
    after_balance: number;
    remark: string | null;
    created_at: string;
    updated_at: string;
}

// 提现记录接口
export interface WithdrawRecord {
    id: number;
    user_id: number;
    amount: number;
    fee: number;
    actual_amount: number;
    withdraw_type: 'alipay' | 'wallet';
    account_name: string | null;
    account_no: string | null;
    status: 'pending' | 'approved' | 'completed' | 'rejected';
    admin_id: number | null;
    reject_reason: string | null;
    proof: string | null;
    remark: string | null;
    created_at: string;
    updated_at: string;
}

// 推荐设置接口
export interface ReferralSetting {
    id: number;
    user_id: number;
    reward_type: 'amount' | 'plan';
    amount_percentage: number;
    plan_days: number;
    plan_id: number;
    enable_switch: boolean;
    has_switched: boolean;
    code: string;
    used_count: number;
    created_at: string;
    updated_at: string;
    auto_claim_plan: boolean;
    plan?:{
        name: string;
        id: number;
    }
}

// 推荐余额接口
export interface ReferralBalance {
    id: number;
    user_id: number;
    balance: number;
    total_reward: number;
    total_withdraw: number;
    frozen_balance: number;
    created_at: string;
    updated_at: string;
}

// 推荐信息接口
export interface AffiliateInfo {
    has_setting: boolean;
    setting: ReferralSetting | null;
    balance: ReferralBalance | null;
    invite_link: string;
} 