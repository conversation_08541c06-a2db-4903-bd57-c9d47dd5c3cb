"use client"

import { useEffect, useState, useRef } from "react";
import { Al<PERSON>, Button, Descriptions, Divider, Form, Input, message, Modal, Radio, Space, Switch, Tabs, Tag, Typography, Tooltip } from "antd";
import { CopyOutlined, DollarOutlined, GiftOutlined, InfoCircleOutlined, LinkOutlined, SwapOutlined, UserOutlined, WalletOutlined, ReloadOutlined } from "@ant-design/icons";
import { ProCard } from "@ant-design/pro-components";
import { enableAffiliate, getAffiliateInfo, switchRewardType, withdrawReferralBalance, getWithdrawRecords, setAutoClaimPlan } from "./api";
import copy from "copy-to-clipboard";
import { ReferredUsersTable, ReferralRewardsTable, IncomeRecordsTable, WithdrawRecordsTable } from "./components";
import { AffiliateInfo } from "./components/types";
import type { ActionType } from "@ant-design/pro-components";

export default function Page() {
    // 区分不同区块的加载状态
    const [enableLoading, setEnableLoading] = useState(false); // 启用按钮的加载状态
    const [infoLoading, setInfoLoading] = useState(false); // 基本信息的加载状态
    const [overviewLoading, setOverviewLoading] = useState(false); // 收益概览的加载状态
    
    const [switching, setSwitching] = useState(false);
    const [affiliateInfo, setAffiliateInfo] = useState<AffiliateInfo>({
        has_setting: false,
        setting: null,
        balance: null,
        invite_link: ''
    });
    const [withdrawModalVisible, setWithdrawModalVisible] = useState(false);
    const [switchModalVisible, setSwitchModalVisible] = useState(false);
    const [withdrawForm] = Form.useForm();
    const [newRewardType, setNewRewardType] = useState<'amount' | 'plan'>('amount');

    // 创建表格的 actionRef
    const referredUsersActionRef = useRef<ActionType>();
    const referralRewardsActionRef = useRef<ActionType>();
    const incomeRecordsActionRef = useRef<ActionType>();
    const withdrawRecordsActionRef = useRef<ActionType>();

    // 获取推荐信息
    const fetchAffiliateInfo = async () => {
        setInfoLoading(true);
        setOverviewLoading(true);
        try {
            const res = await getAffiliateInfo();
            setAffiliateInfo(res as unknown as AffiliateInfo);

        } catch (e: any) {
            message.error(e.message || "获取推荐信息失败");
        } finally {
            setInfoLoading(false);
            setOverviewLoading(false);
        }
    };

    // 启用推荐功能
    const handleEnableAffiliate = async () => {
        setEnableLoading(true);
        try {
            await enableAffiliate();
            message.success("邀请推荐功能已开启");
            fetchAffiliateInfo();
        } catch (e: any) {
            message.error(e.message || "开启失败");
        } finally {
            setEnableLoading(false);
        }
    };

    // 申请提现
    const handleWithdraw = async (values: any) => {
        try {
            const res = await withdrawReferralBalance(values);
            message.success("提现申请已提交，请等待审核");
            setWithdrawModalVisible(false);
            withdrawForm.resetFields();
            fetchAffiliateInfo();
            withdrawRecordsActionRef.current?.reload();
        } catch (e: any) {
            message.error(e.message || "提现申请失败");
        }
    };

    // 切换奖励类型
    const handleSwitchRewardType = async () => {
        setSwitching(true);
        try {
            await switchRewardType(newRewardType);
            message.success("奖励类型切换成功");
            setSwitchModalVisible(false);
            fetchAffiliateInfo();
        } catch (e: any) {
            message.error(e.message || "切换奖励类型失败");
        } finally {
            setSwitching(false);
        }
    };

    // 显示切换奖励类型确认框
    const showSwitchRewardTypeModal = () => {
        if (!affiliateInfo.setting) return;
        setNewRewardType(affiliateInfo.setting.reward_type);
        setSwitchModalVisible(true);
    };

    // 复制邀请链接
    const copyInviteLink = () => {
        if (affiliateInfo.invite_link) {
            copy(affiliateInfo.invite_link);
            message.success("邀请链接已复制到剪贴板");
        }
    };

    const toggleAutoClaim = async (checked: boolean) => {
        try {
            await setAutoClaimPlan(checked);
            message.success(`${checked ? '开启' : '关闭'}自动领取会员奖励成功`);
            fetchAffiliateInfo();
        } catch (e: any) {
            message.error(e.message || "设置失败");
        }
    };

    // 复制推荐码
    const copyCode = () => {
        if (affiliateInfo.setting?.code) {
            copy(affiliateInfo.setting.code);
            message.success("推荐码已复制到剪贴板");
        }
    };

    useEffect(() => {
        fetchAffiliateInfo();
    }, []);

    useEffect(() => {
        if (affiliateInfo.has_setting && !affiliateInfo.invite_link && affiliateInfo.setting?.code) {
            const inviteLink = `${window.location.protocol}//${window.location.hostname}/aff/${affiliateInfo.setting.code}`
            setAffiliateInfo({
                ...affiliateInfo,
                invite_link: inviteLink
            })
        }
    }, [affiliateInfo.has_setting, affiliateInfo.setting]);

    // 渲染奖励类型
    const renderRewardType = () => {
        const { setting } = affiliateInfo;
        if (!setting) return null;

        if (setting.reward_type === 'amount') {
            return (
                <Tag color="green">金额奖励 {setting.amount_percentage}%</Tag>
            );
        } else {
            return (
                <div>
                    <Tag color="blue">会员奖励{setting.plan_days}天</Tag>
                    {/* tips */}
                    <Tooltip title={`每成功邀请一位好友购买会员， 您将获得 ${setting.plan?.name} - ${setting.plan_days} 天奖励`}>
                        <InfoCircleOutlined className="ml-2" />
                    </Tooltip>
                </div>

            );
        }
    };

    return (
        <div className="space-y-6">
            {/* 未启用状态 */}
            {!affiliateInfo.has_setting && (
                <ProCard loading={enableLoading}>
                    <div className="text-center py-10">
                        <div className="text-2xl font-bold mb-4">邀请推荐功能</div>
                        <div className="mb-6 text-gray-500">
                            邀请好友注册并购买会员，获得奖励
                        </div>
                        <Button type="primary" size="large" onClick={handleEnableAffiliate} loading={enableLoading}>
                            立即开启邀请推荐
                        </Button>
                    </div>
                </ProCard>
            )}

            {/* 已启用状态 */}
            {affiliateInfo.has_setting && affiliateInfo.setting && (
                <>
                    {/* 状态概览 */}
                    <ProCard loading={infoLoading} split="vertical">
                        <div className="flex flex-col md:flex-row">
                            <ProCard>
                                <div className="mb-4">
                                    <Typography.Title level={4}>邀请推荐</Typography.Title>
                                    <div className="text-gray-500">
                                        邀请好友注册并购买会员，获得{affiliateInfo.setting.reward_type === 'amount' ?
                                            `订单金额的${affiliateInfo.setting.amount_percentage}%作为奖励` :
                                            `${affiliateInfo.setting.plan?.name} - ${affiliateInfo.setting.plan_days}天作为奖励`}
                                    </div>
                                </div>

                                <Descriptions column={1} bordered>
                                    <Descriptions.Item label="推荐码">
                                        <div className="flex items-center">
                                            <Input value={affiliateInfo.setting.code} readOnly className="mr-2" />
                                            <Button className="hidden md:block" icon={<CopyOutlined />} onClick={copyCode}>
                                                复制
                                            </Button>
                                        </div>
                                    </Descriptions.Item>
                                    <Descriptions.Item label="邀请链接">
                                        <div className="flex items-center">
                                            <Input value={affiliateInfo.invite_link} readOnly className="mr-2" />
                                            <Button className="hidden md:block" icon={<CopyOutlined />} onClick={copyInviteLink}>
                                                复制
                                            </Button>
                                        </div>
                                    </Descriptions.Item>
                                    <Descriptions.Item label="已邀请人数">
                                        {affiliateInfo.setting.used_count || 0} 次
                                    </Descriptions.Item>
                                    <Descriptions.Item
                                        label={
                                            <div className="flex items-center">
                                                <span>奖励类型</span>
                                                {affiliateInfo.setting.enable_switch && !affiliateInfo.setting.has_switched && (
                                                    <Button
                                                        type="link"
                                                        className="ml-2"
                                                        size="small"
                                                        icon={<SwapOutlined />}
                                                        onClick={showSwitchRewardTypeModal}
                                                    >
                                                        切换
                                                    </Button>
                                                )}
                                            </div>
                                        }
                                    >
                                        {renderRewardType()}
                                        {!!affiliateInfo.setting.has_switched && <div className="mt-1 text-xs text-gray-500">已切换过奖励类型，无法再次切换</div>}
                                    </Descriptions.Item>

                                    {affiliateInfo.setting?.reward_type === 'plan' && (
                                        <Descriptions.Item label="自动领取会员">
                                            <div className="flex items-center">
                                                <Switch
                                                    checked={affiliateInfo.setting.auto_claim_plan}
                                                    onChange={toggleAutoClaim}
                                                    className="mr-2"
                                                />
                                                <span>{affiliateInfo.setting.auto_claim_plan ? '已开启' : '已关闭'}</span>
                                            </div>
                                            <div className="text-xs text-gray-500 mt-1">
                                                开启后系统将自动为您领取会员奖励
                                            </div>
                                        </Descriptions.Item>
                                    )}
                                </Descriptions>
                            </ProCard>

                            <ProCard title={
                                <div className="flex items-center justify-between">
                                    <span>收益概览</span>
                                    <Button 
                                        type="text" 
                                        icon={<ReloadOutlined />} 
                                        onClick={() => {
                                            setOverviewLoading(true);
                                            fetchAffiliateInfo().then(() => {
                                                if (affiliateInfo.setting?.reward_type === 'amount') {
                                                    incomeRecordsActionRef.current?.reload();
                                                    withdrawRecordsActionRef.current?.reload();
                                                } else {
                                                    referralRewardsActionRef.current?.reload();
                                                }
                                            });
                                        }}
                                        loading={overviewLoading}
                                    />
                                </div>
                            } loading={overviewLoading}>
                                {affiliateInfo.setting.reward_type === 'amount' && affiliateInfo.balance ? (
                                    <>
                                        <div className="text-center p-4">
                                            <div className="text-xl mb-2">可提现余额</div>
                                            <div className="text-3xl font-bold text-green-600 mb-4">
                                                ￥{(affiliateInfo.balance.balance || 0).toFixed(2)}
                                            </div>
                                            <Button
                                                type="primary"
                                                icon={<DollarOutlined />}
                                                onClick={() => setWithdrawModalVisible(true)}
                                                disabled={!affiliateInfo.balance.balance || affiliateInfo.balance.balance <= 0}
                                            >
                                                申请提现
                                            </Button>
                                        </div>
                                        <Divider style={{ margin: "12px 0" }} />
                                        <div className="flex justify-between text-center">
                                            <div>
                                                <div className="text-gray-500">累计奖励</div>
                                                <div className="font-bold">￥{(affiliateInfo.balance.total_reward || 0).toFixed(2)}</div>
                                            </div>
                                            <div>
                                                <div className="text-gray-500">已提现金额</div>
                                                <div className="font-bold">￥{(affiliateInfo.balance.total_withdraw || 0).toFixed(2)}</div>
                                            </div>
                                            <div>
                                                <div className="text-gray-500">冻结金额</div>
                                                <div className="font-bold">￥{(affiliateInfo.balance.frozen_balance || 0).toFixed(2)}</div>
                                            </div>
                                        </div>
                                    </>
                                ) : (
                                    <div className="text-center py-6">
                                        <GiftOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
                                        <div className="mt-4 text-lg">会员奖励模式</div>
                                        <div className="mt-2 text-gray-500">
                                            您选择了会员奖励模式，每成功邀请一位好友购买会员，
                                            您将获得 {affiliateInfo.setting.plan?.name} - {affiliateInfo.setting.plan_days} 天奖励
                                        </div>
                                    </div>
                                )}
                            </ProCard>
                        </div>
                    </ProCard>

                    {/* 详情选项卡 */}
                    <ProCard>
                        <Tabs
                            defaultActiveKey="referredUsers"
                            items={[
                                {
                                    key: "referredUsers",
                                    label: (
                                        <span>
                                            <UserOutlined />
                                            推荐用户
                                        </span>
                                    ),
                                    children: <ReferredUsersTable actionRef={referredUsersActionRef} />,
                                },
                                {
                                    key: "rewards",
                                    label: (
                                        <span>
                                            <GiftOutlined />
                                            奖励记录
                                        </span>
                                    ),
                                    children: <ReferralRewardsTable actionRef={referralRewardsActionRef} />,
                                },
                                {
                                    key: "income",
                                    label: (
                                        <span>
                                            <DollarOutlined />
                                            收入记录
                                        </span>
                                    ),
                                    children: <IncomeRecordsTable actionRef={incomeRecordsActionRef} />,
                                },
                                {
                                    key: "withdraw",
                                    label: (
                                        <span>
                                            <WalletOutlined />
                                            提现记录
                                        </span>
                                    ),
                                    children: <WithdrawRecordsTable actionRef={withdrawRecordsActionRef} />,
                                },
                            ]}
                        />
                    </ProCard>
                </>
            )}

            {/* 提现弹窗 */}
            <Modal
                title="申请提现"
                open={withdrawModalVisible}
                onCancel={() => setWithdrawModalVisible(false)}
                footer={null}
            >
                <Form
                    form={withdrawForm}
                    layout="vertical"
                    onFinish={handleWithdraw}
                    initialValues={{ withdraw_type: 'alipay' }}
                >
                    <div className="mb-4">可提现余额: ￥{(affiliateInfo.balance?.balance || 0).toFixed(2)}</div>
                    <Form.Item
                        name="withdraw_type"
                        label="提现方式"
                        rules={[{ required: true, message: '请选择提现方式' }]}
                    >
                        <Radio.Group>
                            <Radio value="wallet" disabled={true}>钱包余额</Radio>
                            <Radio value="alipay">支付宝</Radio>
                        </Radio.Group>
                    </Form.Item>

                    <Form.Item
                        name="amount"
                        label="提现金额"
                        rules={[
                            { required: true, message: '请输入提现金额' },
                            {
                                validator: (_, value) => {
                                    // 确保转换为数字
                                    const numValue = Number(value);

                                    // 检查是否为有效数字
                                    if (isNaN(numValue)) {
                                        return Promise.reject('请输入有效的数字');
                                    }

                                    // 检查最小值
                                    if (numValue < 1) {
                                        return Promise.reject('提现金额必须至少为1元');
                                    }

                                    // 检查最大值
                                    if (numValue > (affiliateInfo.balance?.balance || 0)) {
                                        return Promise.reject('提现金额不能大于可提现余额');
                                    }

                                    return Promise.resolve();
                                }
                            }
                        ]}
                    >
                        <Input
                            type="number"
                            addonBefore="￥"
                            placeholder="请输入提现金额"
                            min={1}
                            max={affiliateInfo.balance?.balance || 0}
                        />
                    </Form.Item>


                    <Form.Item
                        noStyle
                        shouldUpdate={(prevValues, currentValues) => prevValues.withdraw_type !== currentValues.withdraw_type}
                    >
                        {({ getFieldValue }) =>
                            getFieldValue('withdraw_type') === 'alipay' ? (
                                <>
                                    <Form.Item
                                        name="account_name"
                                        label="支付宝账户姓名"
                                        rules={[
                                            { required: true, message: '请输入支付宝账户姓名' },
                                            {
                                                pattern: /^[\u4e00-\u9fa5]+$/,
                                                message: '姓名必须为中文'
                                            }
                                        ]}
                                    >
                                        <Input placeholder="请输入支付宝账户姓名（中文）" />
                                    </Form.Item>
                                    <Form.Item
                                        name="account_no"
                                        label="支付宝账号"
                                        rules={[{ required: true, message: '请输入支付宝账号' }]}
                                    >
                                        <Input placeholder="请输入支付宝账号" />
                                    </Form.Item>
                                </>
                            ) : null
                        }
                    </Form.Item>

                    <Alert
                        type="info"
                        message="提现说明"
                        description={
                            <ul className="list-disc pl-5 mt-2">
                                <li>提现申请将在1-3个工作日内处理，请确保您的账户信息正确</li>
                                <li>最低提现金额为1元</li>
                                <li>提现到钱包余额无手续费，提现到支付宝可能收取手续费</li>
                            </ul>
                        }
                        className="mb-4"
                    />

                    <div className="flex justify-end">
                        <Button onClick={() => setWithdrawModalVisible(false)} className="mr-2">
                            取消
                        </Button>
                        <Button type="primary" htmlType="submit">
                            确认提现
                        </Button>
                    </div>
                </Form>
            </Modal>

            {/* 切换奖励类型弹窗 */}
            <Modal
                title="切换奖励类型"
                open={switchModalVisible}
                confirmLoading={switching}
                onCancel={() => setSwitchModalVisible(false)}
                onOk={handleSwitchRewardType}
                okText="确认切换"
                cancelText="取消"
            >
                <div className="py-4">
                    <Alert
                        type="warning"
                        message="重要提示"
                        description="奖励类型切换后将无法再次更改，请谨慎操作！"
                        className="mb-4"
                    />

                    <div className="mb-4">当前奖励类型: {renderRewardType()}</div>

                    <div className="mb-4">
                        <Radio.Group
                            value={newRewardType}
                            onChange={(e) => setNewRewardType(e.target.value)}
                        >
                            <Space direction="vertical">
                                <Radio value="amount">
                                    <div>
                                        <div><Tag color="green">金额奖励</Tag></div>
                                        <div className="text-gray-500 mt-1">
                                            获得被邀请人订单金额的{affiliateInfo.setting?.amount_percentage}%作为现金奖励
                                        </div>
                                    </div>
                                </Radio>
                                <Radio value="plan">
                                    <div>
                                        <div><Tag color="blue">会员奖励</Tag></div>
                                        <div className="text-gray-500 mt-1">
                                            每成功邀请一位好友购买会员，获得 {affiliateInfo.setting?.plan?.name} - {affiliateInfo.setting?.plan_days}天会员奖励
                                        </div>
                                    </div>
                                </Radio>
                            </Space>
                        </Radio.Group>
                    </div>
                </div>
            </Modal>
        </div>
    );
} 