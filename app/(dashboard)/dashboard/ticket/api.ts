import axiosInstance from "@/lib/axios";

export async function getTickets(params: any) {
  return axiosInstance.get("frontend/dashboard/ticket/index", {params})
}

export async function getTicketCategory() {
  return axiosInstance.get("frontend/dashboard/ticket/ticket_category")
}

export async function createTicket(data: any) {
  return axiosInstance.post("frontend/dashboard/ticket/create_ticket", data)
}

export async function createTicketReply(data: any) {
  return axiosInstance.post("frontend/dashboard/ticket/create_reply", data)
}

export async function makeTicketSuccess(data: any) {
  return axiosInstance.post("frontend/dashboard/ticket/make_success", data)
}


export async function getTicketDetail(ticket_no: any) {
  return axiosInstance.get("frontend/dashboard/ticket/detail/" + ticket_no,)
}