"use client"

import {<PERSON><PERSON>, <PERSON>} from "antd";
import {ProTable} from "@ant-design/pro-components";
import {ProColumnType} from "@ant-design/pro-table";
import {getLoginLogs} from "@/app/(dashboard)/dashboard/secure/log/api";
import {getTicketCategory, getTickets} from "@/app/(dashboard)/dashboard/ticket/api";
import Link from "next/link";

export default function Page() {
  const columns: ProColumnType[] = [{
    dataIndex: "ticket_no",
    title: "工单编号",
    search: true,
  },
    {
      dataIndex: "title",
      title: "工单标题",
      search: false,
    },
    {
      dataIndex: "category",
      title: "工单分类",
      request: async () => {
        try {
          const {ticket_category} = await getTicketCategory()
          return ticket_category;
        } catch {
          return []
        }
      }
    },
    {
      dataIndex: "status",
      title: "工单状态",
      valueType: "select",
      valueEnum: {
        1: "待处理",
        2: "已接单",
        3: "待回复",
        4: "已完成",
      }
    },
    {
      dataIndex: "created_at",
      title: "创建时间",
      search: false,
    },
    {
      valueType: "option",
      title: "操作",
      render: (_, entity) => {
        return <><Link href={`ticket/${entity.ticket_no}`} size={'small'}>详情</Link></>
      }
    },
  ];
  return <>
    <Card bordered={false} title={'工单管理'}>
      <ProTable pagination={{
        pageSize: 10,
      }} toolbar={{
        filter: false,
        settings: false,
      }} toolBarRender={() => [
        <Button href={'ticket/create'} type={'primary'} key={'add'} onClick={() => {
        }}>创建工单</Button>
      ]} columns={columns} request={async (params, sort, filter) => {
        try {
          return await getTickets(params)
        } catch {
          return {}
        }
      }}
                scroll={{x:700}}
      ></ProTable>
    </Card>
  </>
}