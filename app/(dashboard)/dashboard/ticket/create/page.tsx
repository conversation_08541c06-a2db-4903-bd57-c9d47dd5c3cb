"use client"

import {Card, Col, message, Row, Space, Typography} from "antd";
import {ChevronLeft} from "lucide-react";
import {useRouter} from "next/navigation";
import {ProForm, ProFormSelect, ProFormText, ProFormTextArea, ProFormUploadDragger} from "@ant-design/pro-components";
import {useEffect} from "react";
import {createTicket, getTicketCategory} from "@/app/(dashboard)/dashboard/ticket/api";
import {ProFormUploadButton} from "@ant-design/pro-form";
import {addTaxInfo} from "@/app/(dashboard)/dashboard/cost/invoice/api";

export default function Page() {
  const router = useRouter()
  const [form] = ProForm.useForm();

  return <>
    <Card bordered={false} title={<>
      <div className={'flex gap-x-5'}>
        <div className={''}>
          <Typography.Link onClick={() => {
            router.push("/dashboard/ticket")
          }} className={'flex items-center'}>
            <ChevronLeft size={16} className={'inline-block'}/>
            <span>返回</span></Typography.Link>
        </div>
        <div className={'flex'}>
          创建新工单
        </div>
      </div>
    </>}>
      <div className={'md:w-[500px] w-max-[500px]  p-5'}>
        <ProForm
          labelCol={{span: 8}}
          wrapperCol={{span: 16}}
          layout={'horizontal'}

          form={form}
          autoComplete="off"
          submitter={{
            render: (props, doms) => (
              <Row>
                <Col span={16} offset={8}>
                  <Space>{doms}</Space>
                </Col>
              </Row>
            )
          }}
          onFinish={async (values) => {
            // 在这里可以处理表单提交
            try{
              await createTicket(values)
              message.success('提交成功');
              router.push("/dashboard/ticket")
            }catch(e){
              message.error(e.message||'添加失败')
            }
          }}
        >
          <ProFormText name={'title'} label={'工单标题'} rules={[{required: true},{max:50,}]}/>
          <ProFormSelect name={'category'} label={'工单分类'} rules={[{required: true}]} request={async () => {
            try {
              const {ticket_category} = await getTicketCategory()
              return ticket_category;
            } catch {
              return []
            }
          }}></ProFormSelect>
          <ProFormTextArea label={'问题描述'} name={'content'} rules={[{required: true}]}/>
       {/*   <ProFormUploadButton name={'attachment'} fieldProps={{
            multiple: false,
          }} label={'附件'}/>*/}
        </ProForm>
      </div>

    </Card>
  </>
}