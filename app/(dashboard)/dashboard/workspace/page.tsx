"use client"
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Empty, message, Ta<PERSON>, <PERSON><PERSON><PERSON>, Typography } from "antd";
import { useEffect, useState } from "react";
import clsx from "clsx";
import Link from "next/link";
import { getUserApis } from "@/app/(dashboard)/dashboard/data/api_log/api";
import { getLastLoginLog, getUserProfile } from "@/app/(dashboard)/dashboard/workspace/api";
import { useSiteConfig } from "@/components/provider/site-provider";
import { getCanApplyMoney } from "@/app/(dashboard)/dashboard/cost/invoice/api";
import { getMessageList } from "@/app/(dashboard)/dashboard/account/message/api";
import { useRouter } from "next/navigation";


function getDate() {
  const date = new Date();
  const weekDays = ['日', '一', '二', '三', '四', '五', '六'];

  const formattedDate = `${date.getFullYear()}年${String(date.getMonth() + 1).padStart(2, '0')}月${String(date.getDate()).padStart(2, '0')}日星期${weekDays[date.getDay()]}`;
  return formattedDate
}

export default function Page() {
  const [user, setUser] = useState({})
  const [apis, setApis] = useState([])
  const [lastLoginLog, setLastLoginLog] = useState({
    created_at: "",
    address: "",
    ip: "",
  })
  const router = useRouter()

  const [invoiceMoney, setInvoiceMoney] = useState(0)

  const [globalMessage, setGlobalMessage] = useState([])
  const [personalMessage, setPersonalMessage] = useState([])

  const siteConfig = useSiteConfig();
  const loadUserApis = async () => {
    try {
      const data = await getUserApis()
      //@ts-ignore
      setApis(data)
    } catch {
      setApis([])
    }
  }
  const loadUserProfile = async () => {
    try {
      const data = await getUserProfile()
      setUser(data)
    } catch {
    }
  }
  const loadLastLoginLog = async () => {
    try {
      const data = await getLastLoginLog()
      //@ts-ignore
      setLastLoginLog(data)
    } catch {

    }
  }
  const handleGetCanApplyMoney = () => {
    getCanApplyMoney().then((data) => {
      //@ts-ignore
      setInvoiceMoney(data)
    }).catch(() => {

    })
  }

  const handleLoadMessage = () => {
    getMessageList({ message_type: "global" }).then((res) => {
      //@ts-ignore
      setGlobalMessage(res.data)
    }).catch(() => {

    })
    getMessageList({ message_type: "personal" }).then((res) => {
      //@ts-ignore
      setPersonalMessage(res.data)
    }).catch(() => {

    })
  }

  useEffect(() => {
    loadUserApis()
    loadUserProfile()
    loadLastLoginLog()
    handleGetCanApplyMoney()
    handleLoadMessage()
  }, []);

  return <>
    {/*<div className={'mb-5'}>*/}
    {/*  /!*<Alert banner showIcon type={'warning'} message={'警告消息'}/>*!/*/}
    {/*</div>*/}
    <div className={'mb-5'}>
      <Card>
        <div className='flex md:flex-row flex-col'>
          <div className='flex-1'>
            <p className='text-base	 font-medium mb-1'>今日,</p>
            <p
              className='text-xs	text-gray-500'>{getDate()}，欢迎您回到{siteConfig.site_name}。对新版有想说的，请 <Link href='/dashboard/ticket/create'>点击反馈</Link>
            </p>
          </div>
          {/*<div className='flex lg:mt-0 mt-5'>*/}
          {/*  <div className='flex w-[180px]'>*/}
          {/*    <div className={'size-[48px]'} style={{*/}
          {/*      backgroundImage: 'url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAMAAABg3Am1AAAAXVBMVEUAAADo+P/o+f/o+f/o+P/o+P/o+f/o+f/p+f/q+v/p+v/p+P/q+v/o+v/p+f/p+f/q+//r///o///x///M///o+P8Auv9Pz/85yv/E7/9DzP8zx/8Mvf8gw/+K3/+2jYUEAAAAFXRSTlMA9Pjy69vOyLaPjXVrZldQJBkWEgWofv9FAAABF0lEQVRIx5XW646CMBAF4Cm3crNoYUphcd//MZe4ahxatef7Q0g4Cb3NlALWNHVZZFlR1o2x9MXYaxZ0P9J7l1ZxQLUXips6xVGqmyjiXPFb1ZkCQ84f5AMdnPiLU/A9lBg4wfAy3jwlkD9HPlWcpHrMbseJuvv6qtSA+l/zlpO1tBtVekCNe6BnQL8HNBLQRJaFxTvBLyxYMix45+cX+ysLhhoW3MrC6lhoqD4E5se/rZu7YaGmMh5YNnf92W2HQElFPLC639tzPgQKyuKB7creRwIZHIB/CR40Pq1NEBDmYOEMttKGLLaXLJFGdquGDxB8RNEiAJcZtJDBpRItxmi5RxsK3rLwpoi3Xbyx41cH/HICX3/+AIoHlP/2q5m2AAAAAElFTkSuQmCC)'*/}
          {/*    }}></div>*/}
          {/*    <div className='ml-[20px]'>*/}
          {/*      <div className='font-bold	 text-xl'>0</div>*/}
          {/*      <div className='text-xs'>待续费</div>*/}
          {/*    </div>*/}
          {/*  </div>*/}
          {/*  <div className='flex  w-[180px]'>*/}
          {/*    <div className={'size-[48px]'} style={{*/}
          {/*      backgroundImage: 'url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAMAAABg3Am1AAAAXVBMVEUAAADo+P/o+f/o+f/o+P/o+P/o+f/o+f/p+f/q+v/p+v/p+P/q+v/o+v/p+f/p+f/q+//r///o///x///M///o+P8Auv9Pz/85yv/E7/9DzP8zx/8Mvf8gw/+K3/+2jYUEAAAAFXRSTlMA9Pjy69vOyLaPjXVrZldQJBkWEgWofv9FAAABF0lEQVRIx5XW646CMBAF4Cm3crNoYUphcd//MZe4ahxatef7Q0g4Cb3NlALWNHVZZFlR1o2x9MXYaxZ0P9J7l1ZxQLUXips6xVGqmyjiXPFb1ZkCQ84f5AMdnPiLU/A9lBg4wfAy3jwlkD9HPlWcpHrMbseJuvv6qtSA+l/zlpO1tBtVekCNe6BnQL8HNBLQRJaFxTvBLyxYMix45+cX+ysLhhoW3MrC6lhoqD4E5se/rZu7YaGmMh5YNnf92W2HQElFPLC639tzPgQKyuKB7creRwIZHIB/CR40Pq1NEBDmYOEMttKGLLaXLJFGdquGDxB8RNEiAJcZtJDBpRItxmi5RxsK3rLwpoi3Xbyx41cH/HICX3/+AIoHlP/2q5m2AAAAAElFTkSuQmCC)'*/}
          {/*    }}></div>*/}

          {/*    <div className='ml-[20px]'>*/}
          {/*      <div className='font-bold text-xl'>0</div>*/}
          {/*      <div className='text-xs'>待续费</div>*/}
          {/*    </div>*/}
          {/*  </div>*/}
          {/*</div>*/}
        </div>

      </Card>
    </div>
    <div className={'flex lg:flex-row flex-col'}>
      <div className='left lg:basis-2/3 basis-1'>
        <div className='flex flex-col'>
          <div className={'flex-1 mb-5'}>
            <Card bordered={false}>
              <div className={'flex flex-col'}>
                <div className="flex info">
                  <div className="font-bold text-base min-w-[100px]">
                    Hi,{user?.nickname}
                  </div>
                  <div className="ml-[20px] items-center flex">
                    {/* <Tooltip title={'已实名认证'}>
                      <p className={clsx('icon user-check', {'active': user})} onClick={() => () => {
                        setUser(false)
                      }}>
                      </p>
                    </Tooltip>*/}
                    <Tooltip title={'已绑定手机'}>
                      <p className={clsx('ml-[10px] icon phone', { 'active': user })} onClick={() => () => {
                        setUser(false)
                      }}>
                      </p>
                    </Tooltip>
                    {/* <Tooltip title={'已绑定邮箱'}>
                      <p className={clsx('ml-[10px] icon email', {'active': user})} onClick={() => () => {
                        setUser(false)
                      }}>
                      </p>
                    </Tooltip>
                    <Tooltip title={'已绑定微信'}>
                      <p className={clsx('ml-[10px] icon wechat', {'active': user})} onClick={() => () => {
                        setUser(false)
                      }}>
                      </p>
                    </Tooltip>*/}
                  </div>
                </div>
                <div className='mt-5 text-xs text-gray-500 flex md:flex-row flex-col '>
                  <div className='mr-[16px]'>
                    上次登录信息
                  </div>
                  <div className='flex md:flex-row flex-col'>
                    <div className='mr-[16px] mt-1 md:mt-0'>
                      地区：{lastLoginLog?.address}
                    </div>
                    <div className='mr-[16px] mt-1 md:mt-0'>
                      IP: {lastLoginLog?.ip}
                    </div>
                    <div className='mr-[16px] mt-1 md:mt-0'>
                      时间：{lastLoginLog?.created_at}
                    </div>
                    <div className={'mr-[16px] mt-1 md:mt-0'}>
                      <Link href={`/dashboard/secure/log`} className={'text-blue-500 text-xs'}>登录日志</Link>
                    </div>
                  </div>
                </div>
                <div className='border-b mt-3 line'></div>
              </div>
              <div className='mt-5'>
                <div className='flex flex-col md:flex-row  '>
                  <div className='left md:border-r  md:basis-1/2 basis-1'>
                    <div>
                      <div className={'text-xs text-gray-400'}>
                        余额
                      </div>
                      <div className='flex w-full md:w-[350px]'>
                        <div className={'flex-1 text-xl text-bold'}>
                          ￥0.00
                        </div>
                        <div>
                          <Button size='small' type={'primary'} title={'暂未上线'} onClick={() => {
                            message.info('暂未上线')
                          }}>充值</Button>
                        </div>
                      </div>
                    </div>
                    <div className='mt-5'>
                      <div className={'text-xs text-gray-400'}>
                        发票（可开发票额度）
                      </div>
                      <div className='flex w-full md:w-[350px]'>
                        <div className={'flex-1'}>
                          ￥ {invoiceMoney}
                        </div>
                        <div>
                          <Link href={'/dashboard/cost/invoice'} className='text-sm'>申领发票</Link>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className='right md:basis-1/2 border-t md:border-none md:mt-0 mt-5	basis-1'>
                    <div className={'flex justify-center items-center mt-2 md:mt-0'}>
                      <div>
                        {siteConfig?.kefu_img && <img width={100} height={100}
                          src={siteConfig?.kefu_img}
                          alt={'kf'} />}
                      </div>
                      <div className={'flex items-center'}>
                        <div className={'ml-5'}>
                          <p className={'text-[#ccc] text-sm'}>专属客户经理</p>
                          <p className={'mt-5 text-lg'}>{siteConfig?.kefu || "暂无"}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
          <div className={'flex-1 mb-5'}>
            <Card styles={{
              body: { padding: "16px 16px" }
            }} bordered={false} title={<div className='flex justify-between items-center'>
              <div className='text-base font-bold'>我的接口 {apis && apis.length > 0 ? apis.length : 0}个</div>
            </div>}
              extra={[<><Link href={`/explore`} target={"_blank"}><Button>申请新接口</Button></Link></>,]}>

              {(apis && apis.length <= 0) ? <Empty /> :
                <div className='grid md:grid-cols-5 grid-cols-2 gap-x-10 gap-y-5'>
                  {/* 最多显示10个 */}
                  {apis && apis.length > 0 && apis.slice(0, 10).map((e) => {
                    return <div key={e.id}>
                      <div className='flex justify-center items-center h-[30px]'>
                        <div className={'size-[30px]'} style={{
                          background: `url('${e.img}') no-repeat`,
                          backgroundSize: "cover",
                        }}></div>
                        <div className={'h-[30px] ml-[10px] flex items-center'}>
                          <Link href={`/api/${e.id}/api_document`}
                            className={'text-black hover:text-blue-500'}>{e.name}</Link>
                        </div>
                      </div>
                    </div>
                  })}
                </div>}

            </Card>
          </div>
        </div>
      </div>
      <div className='right md:ml-[16px] md:mt-0 mt-5 ml-0 md:basis-1/3  basis-1'>
        <div className={'flex flex-col'}>
          <div className='flex-1 mb-5'>
            <Card styles={{
              body: { padding: '16px 16px' }
            }} className='' title={'VIP会员'} bordered={false}>
              {user?.is_free_plan && <div className='flex flex-col'>
                <div className={'flex justify-center items-center'}>
                  <div className={'size-[144px]'}>
                    <div className={'size-[72px] mx-auto mr-10'} style={{
                      background: "url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAMAAABiM0N1AAABDlBMVEX86KgAAAD86Kj//5L86aj/6azx02rw0mvx1G765Jj86af856j96Kn/66r/6qnx0mrw02zy1G3z1nDz13bz2Hf02Xvz2Xvz2X302oD124P55pr85qX76an76ab76ab/6aX/66f/8qby5KH/35//6KL/35/44pH435Pw0mrz24dHQTlEPzdeVkjw1HJpXk86NjE3NC/o0YLx13rx1XZhV0o/OzRkWkxcU0dBPDbFsXRaUkVMRj1KRDtYT0RSS0BQST89OTMxLytUTUI0Mi0uLCny2H/BrnJ5blFsYk3r1IPkzoDgyn6PglpnXE5mXUjw02+EeFNxZ0wqKCbNuXermmiYil1WTkHy2YKyoWqIelu/XaRsAAAAKHRSTlNPAE0CQB359ttfSkk2KyP88ebMsbCfmZWLhltSR0U5IhoUExALCGhoIFbiVgAAA2dJREFUWMOU0FcOgzAQRdGJC6EZ0UXvCPa/wnwhZRgD5n5bR88Dn5tmqXzReox5rfCVnO/eXkJr77twyvX79R3EZcdAG+skN4Z40MBNTcC1EGU8eMijFIUmAQaJ6QHiCgxT/A5aBBgnlmtodOFF7ngFDQxexQY9JJFjJEkM0T3mmyg06hynSOIotKwwipPC0UnjGVronavc3lF2XtGLLxjigoxJrZ1kpWSW4AhSgKuz767tm9WAU//QBLjN3i+zN8BNB0Q/VqI5ZFRJPndAAaB+jJNpT8JAEIZHEEIoWO4j4fhgIL3AyKmUwzShiIBIQI7//0ecLmlnGgzwfNzsPpl5ZzYvK1eR8+DjyRUFJb9HuYnfJAUd0UVBRfm2SC5elARYUAkYdcrnWk51YJSCQhTyzT2i3EXEtwUhIaoAI6vcSRYYFUdUC/B9Zo1N1Uum1Bzf8UANRVVgZMjTUv+jpbhkgFFFUYz/U/pfa3w1fyds296qyNr7d/wHx1AUBSLnb+zUdFiter1OxzD6g6OvuRwQ0Qd4BgaNrIFvbKHpCU2/PxiNjC88bXiDAwI1IR41FXTAxmaoEZazxrLGSxQdFBcWN2riQBQUlza+2HmawVkznkycmNrupQIQceBZpz0R3l80zz05mr1locU0hxsVcS+ledrwCESKj34pqvl0aH4jhjkcatqRrUAKiEcoA5Fko9+KaGyV+NE0XR/PaQWSQJRBAiLBRj8TPe1ePRYbXX/rdn9pBRJASBAAIkwJqScMeC8CNkVPaEHNx8uOUgoDEfCLaIlw+H+12EsOgjAQgOGlN8HER8CFGzUKlqhBY4RE738USxPp1J/oYtoLfKGl8zx+M2WZ58/eWY5AOJqQ9lbZWsUzxeDgaLhsL61a9zEzdzW9UhQ37+Cy8fuFdHHKwBjv8PfzQUrp7hljKuHwQTJEpNQs1uWHqR7SYYgwaGW8NZv+TFapauuIOGPQhmmE0ss4Bg7SCBNbKF0PVqm7c+AwsTHVUtrVHRymWiZ/SvOupcPkP16OZEJpTnRYjlggKbk69LdAsmRTosOSzSaCEp3JlE0E2xpIcNDWxG209K1f9GaU7XH2qz3O0B7Hbdj1I0SKoUY/ZukHv3SjqH441o/r+gVC6pWGfsmiX/voF1H61Vj6ZZ1+ffgGDxFIaQkf73YAAAAASUVORK5CYII=') no-repeat"
                    }}>
                    </div>
                    <div className='mt-3'>
                      <p className={'text-center text-base font-base font-semibold'}> 海量数据，随心挑选</p>
                      <p className={'text-center text-sm mt-2 text-gray-400'}>
                        免费接口无限畅享
                      </p>
                    </div>
                  </div>
                  <div className={'size-[144px] ml-10'}>
                    <div className={'size-[72px] mx-auto'} style={{
                      background: "url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAMAAABiM0N1AAAAyVBMVEUAAABv2v+Q4v+Y5f9o2f933f9+3v+47P+D4P+/7v+J4P+t6v+i5/+o6f+e5/+z6//X9f/S8/9i1//H8f/L8v/D8P9j0P9g1/9f1//O8v9b2P9r0v/G7/9e1v9f1/9e0//c9/+B2f+S3/9d1f9jx/9e1f9ZzP/b9v9X2f+M3P+a4f+e4v/V9P9z1P+87P+67P901f9gzv9/1//z/P/1/P+y5//J7v+O3P/T8v/V8v+C1//F7f/N8P/L7//l9/9w0//A6/+h4v/X8v97Ms7WAAAAMXRSTlMAUlteUFRWZ1hpWWRhYl9lcG9Oa21q90xIbhvmfj82InHBrCoJRBRyELOinnTXhobW7nPWtAAABEhJREFUWMOc1NmWmkAQBmBNyAzi1jRbA4Ii4rjNgmTMciYnef+nSlU3TUMQxsl/Y199568qjoO+7II0jjLmeSyL4jTYDf4nm2XMKNUwwyH/oZTFy80HmSD2KAiYLzz8CZgXBx9glhkoSNxBdB58AYdWtryVYZQraBiG8YkHHqAJi7JbqG2EDCrC+FxGaGghFW3fc1LOoILGbDablIEnaGgJKu0/VaRpyHAFjOl0OpaZTkFDCykstekZi1ExFGfAmM/vq8zngHFKDEhZ53iBB3UUA8gI41iW4+ADMEVBKS/ocXQdphIMCrZt+ybEhwdygoL5pNTZx6iYkYUGIauHFYQQ1KxRRRld0pb3QadsYwOyOCSPoQsJX5LDYkVM28ZW0ylKOkrb1r0Y9pEOZxbrMG8kXC+Asngp2Yn9e7uIij4wFtRBJnHzVtwEKCwF4wmJRk0nxT7cgbEs3zztBdOm9ifTx1KyU/PL3FIN7g5zCYc8hXlnwiciJbydRnf1wbShcMawHpMc3bwn7hHGg+mENNRqwy0pHwydETjr/J2siWmBNJmJ4ZYVxEShyXiOcwmnX8Lp5uOJqMTqhXSDL8jxyTG/IUfiO3xNhl6rlEEhAwvdw92f3Vsg95ngmvAbUJUCKCQuBgs6qXv9Pl+LvN0J1sQvh5UCDsW1QmTf6ygo3xPbgcuJSjE6G0+Dk5WFFmqwcz/kLuAbKLekeRu5alkoyRvQtz9vKr9eFQRJcEtYSaxbTqYKyaBTfK3nwiFVSWypmo1peHtRaJ03oEvDKS7nnwjJrMvDwUeJd9upyXwSNqEfdaYoLq9vdShc+Q5AYrYdHv9Ol5PlnVDBoe8AqcjZ9Dv8ANJqRTY5dELFNehQW1Ja7foeoKQLKlqQupvcdgQQ7hqhlzakmDb0KLfN/0syrdz1yF+FV6GiBbW3rWUDJiHHfHCvQUUn5D6YFcQGnry+dR0qeqCVacn7e397MbsVhGEYCoN4seHm/MUrQRgiCP4guE3HhvP9H8pWC0kbPVYK5mpXYUnb5HwHJ1JpfBPh0qw8uDTRbJkINTumZvPj34lElzOLDh6/uZCDtxfyfip5wAuZsyniPpHq1qHStiP2ROCjrdsrjxN6tHCMlG1b1CzaCowRff6m2zTYaNQ2KgoTTdOBwQZGrQgwamn4p5k5N7COwPAH60hkgusILEgQtCATWpByZeMAK5uJiHg23vuJiL3WWpn5oXW4rKFWW0IrDRFa9Eu9YeIr/VZC+kkxGnuJ0akjRokfbHl8QPL4oOqakzxeWBSf6+J+E+yJgYg8CCEyQggJNRyOvKAmIqjhbSJcszDr+MKsncGs6RP+EoFZAiAJ/OYa/EYa/DaK/dSHA37RF4RkKKpZlFBUka1Oo6hPoCiG44zDcQzhGON66oHrwECYOAbC4JOB0F9iByhfhFga4SYLtn0i1/aJgO0TYEQFW2P/MevC7cMHkzvOEEu29ZYAAAAASUVORK5CYII=') no-repeat"
                    }}>
                    </div>
                    <div className='mt-3'>
                      <p className={'text-center text-base font-base font-semibold'}> 海量数据，随心挑选</p>
                      <p className={'text-center text-sm mt-2 text-gray-400'}>
                        免费接口无限畅享
                      </p>
                    </div>
                  </div>
                </div>
                <div className={'text-center mt-5 mb-10'}>
                  <Link href={'/vip'}><Button type={'primary'} className={'w-full'}>立即加入</Button></Link>
                </div>
              </div>}
              {!user?.is_free_plan && <div className={'flex flex-col gap-y-2'}>
                <div className={'flex gap-x-2 items-center'}>
                  <div className={''}>当前套餐：</div>
                  <div className={'text-base font-medium'}>{user?.plan?.name}</div>
                </div>
                <div className={'flex gap-x-2  items-center'}>
                  <div className={''}>免费接口数量：</div>
                  <div
                    className={'text-base font-medium'}> {user?.plan?.api_limit === -1 ? '不限制' : user?.plan?.api_limit}</div>
                </div>
                <div className={'flex gap-x-2  items-center'}>
                  <div className={''} title='每个接口单独计次'>每日接口请求次数：</div>
                  <div
                    className={'text-base font-medium'}> {user?.plan?.daily_limit === -1 ? '不限制' : user?.plan?.daily_limit}</div>
                </div>
                <div className={'flex gap-x-2  items-center'}>
                  <div className={''} title='每秒请求次数'>QPS：</div>
                  <div
                    className={'text-base font-medium'}> {user?.plan?.request_qps === -1 ? '不限制' : user?.plan?.request_qps}</div>
                </div>
                <div className={'flex gap-x-2  items-center'}>
                  <div className={''} title='到期时间'>到期时间：</div>
                  <div className={'text-base font-medium'}>{user?.plan_end_at}</div>
                </div>
                <div className={''}>
                  <Link href={'/vip'}><Button type={'primary'} size={'small'}>立即续费</Button></Link>
                </div>
              </div>}
            </Card>
          </div>

          <div className='flex-1'>
            <Card bordered={false}>
              <Tabs>
                <Tabs.TabPane tab={'公共消息'} key={'1'}>
                  <div>
                    {globalMessage.map((message) => {
                      return <div onClick={() => {
                        router.push(`/dashboard/account/message/${message.id}`)
                      }} key={message.id}
                        className={'flex items-center w-full h-[24px] mb-[4px] cursor-pointer '}>
                        <div className={'flex-1'}>
                          <span
                            className={'text-ellipsis overflow-hidden ml-[4px] hover:text-blue-500'}>{message.title}</span>
                        </div>
                        <div>
                          <span className={'float-right text-xs'}>{message?.created_at}</span>
                        </div>
                      </div>
                    })}
                  </div>
                </Tabs.TabPane>
                <Tabs.TabPane tab={'个人消息'} key={'2'}>
                  <div>
                    {personalMessage.map((message) => {
                      return <div onClick={() => {
                        router.push(`/dashboard/account/message/${message.message_id}`)
                      }} key={message.id}
                        className={'flex items-center w-full h-[24px] mb-[4px] cursor-pointer'}>
                        <div className={'flex-1'}>
                          <span
                            className={'text-ellipsis overflow-hidden ml-[4px] hover:text-blue-500'}>{message.title}</span>
                        </div>
                        <div>
                          <span className={'float-right text-xs'}>{message?.created_at}</span>
                        </div>
                      </div>
                    })}
                  </div>
                </Tabs.TabPane>
              </Tabs>
            </Card>
          </div>
        </div>
      </div>
    </div>

  </>
}