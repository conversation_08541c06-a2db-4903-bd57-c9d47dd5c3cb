import axiosInstance from "@/lib/axios";

export async function getApiLists(params: any) {
  return axiosInstance.get("/frontend/dashboard/api/index", {params})
}

export async function getApiStat(params: any) {
  return axiosInstance("/frontend/dashboard/api/stat", {params})
}

// 获取API预警信息
export async function getApiUsageAlert(api_id: number) {
  return axiosInstance.get("/frontend/dashboard/api/get_usage_alert", {
    params: { api_id }
  })
}

// 更新API预警设置
export async function updateApiUsageAlert(data: {
  api_id: number;
  status: 'Y' | 'N';
  threshold: number;
  hours: number[]
}) {
  return axiosInstance.post("/frontend/dashboard/api/update_usage_alert", data)
}