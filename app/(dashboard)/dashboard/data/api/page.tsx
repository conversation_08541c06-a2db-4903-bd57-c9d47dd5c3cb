"use client"
import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  DatePicker,
  Empty,
  Input,
  message,
  Modal,
  Pagination,
  Space,
  Spin, Table,
  Tabs,
  Tag,
  TimeRangePickerProps,
  Typography
} from "antd";
import { PlusOutlined, SearchOutlined } from "@ant-design/icons";
import { getApiLists, getApiStat, getApiUsageAlert, updateApiUsageAlert } from "@/app/(dashboard)/dashboard/data/api/api";
import Link from "next/link";
import { LineChart, Line } from "@visactor/react-vchart";
import { ModalForm, ProFormDigit, ProFormRadio, ProFormTimePicker, ProFormSlider, ProFormDependency } from "@ant-design/pro-form";

const { RangePicker } = DatePicker;
import dayjs, { Dayjs } from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import { PlanColor } from "@/config/data";
import { Api } from "@/app/(admin)/admin/api/data";
import { ProFormSwitch } from "@ant-design/pro-components";

dayjs.extend(customParseFormat);
const dateFormat = 'YYYY-MM-DD';

const rangePresets: TimeRangePickerProps['presets'] = [
  { label: '本周', value: [dayjs().startOf('week'), dayjs().endOf('week')] },
  { label: '最近7天', value: [dayjs().add(-7, 'd'), dayjs()] },
  { label: '本月', value: [dayjs().startOf('month'), dayjs().endOf('month')] },
  {
    label: '上月',
    value: [dayjs().subtract(1, 'month').startOf('month'), dayjs().subtract(1, 'month').endOf('month')]
  },
];

export default function Page() {
  const [activeKey, setActiveKey] = useState('0');
  const [page, setPage] = useState(1)
  const [total, setTotal] = useState(0)
  const [apis, setApis] = useState([])
  const [loading, setLoading] = useState(false)
  const [search, setSearch] = useState('')
  const [searchValue, setSearchValue] = useState('')

  const [statModalOpen, setStatModalOpen] = useState(false)
  const [statApiId, setStatApiId] = useState(0)
  const [statDate, setStatDate] = useState([dayjs().subtract(7, 'day'), dayjs()])
  const [statData, setStatData] = useState<any[]>([])
  const [statLoading, setStatLoading] = useState(false)
  const [statTitle, setStatTitle] = useState('调用统计')

  const [recordModalOpen, setRecordModalOpen] = useState(false)
  const [recordData, setRecordData] = useState([])

  const [alertModalOpen, setAlertModalOpen] = useState(false);
  const [alertLoading, setAlertLoading] = useState(false);
  const [currentApi, setCurrentApi] = useState<Api>();
  const [alertData, setAlertData] = useState<{
    status: 'Y' | 'N';
    threshold: number;
    start_hour: number;
    end_hour: number;
  }>();

  const handleLoadStatData = async (start_date, end_date) => {
    console.log('handleLoadStatData')
    setStatLoading(true)
    setStatDate([])
    try {
      const res = await getApiStat<any[]>({
        api_id: statApiId,
        start_date: start_date,
        end_date: end_date,
      })
      //@ts-ignore
      setStatData(res)
    } catch (e) {
      console.log(e)
      console.log('err')
    }
    setStatLoading(false)
  }
  const handleSearch = () => {
    setLoading(true)
    getApiLists({ type: activeKey, page: page, search: search }).then((res) => {
      const { data, total } = res
      setApis(data)
      setTotal(Number(total))
    }).finally(() => {
      setLoading(false)
    })
  }

  useEffect(() => {
    if (activeKey) {
      setPage(1)
    }
    handleSearch()
  }, [activeKey]);

  useEffect(() => {
    handleSearch()
  }, [page, search]);

  useEffect(() => {
    if (statModalOpen) {
      handleLoadStatData(statDate[0]?.format(dateFormat) || dayjs().subtract(7, 'day').format(dateFormat), statDate[1]?.format(dateFormat) || dayjs().format(dateFormat))
    }
  }, [statModalOpen]);

  // const loadAlertData = async (apiId: number) => {
  //   setAlertLoading(true);
  //   try {
  //     const res = await getApiUsageAlert(apiId);
  //     return res.data || {
  //       enable: 'N',
  //       threshold: 100,
  //       start_hour: 0,
  //       end_hour: 23
  //     }
  //   } catch (e) {
  //     message.error('获取预警信息失败');
  //   } finally {
  //     setAlertLoading(false);
  //   }
  // }

  return <>
    <div>
      <Card title={'我的接口'} bordered={false} extra={<div>
        <div className={'md:hidden block'}>
          <Link href={'/explore'} target={'_blank'}> <Button icon={<PlusOutlined />}>申请接口</Button></Link>
        </div>
      </div>}>
        <Tabs tabBarExtraContent={{
          right: <>
            <div className={'flex gap-x-10 '}>
              <div className={'md:block hidden'}>
                <Link href={'/explore'} target={'_blank'}> <Button icon={<PlusOutlined />}>申请接口</Button></Link>
              </div>
              <div className={'md:block hidden min-w-[300px]'} onMouseDown={(e) => {
                e.stopPropagation();
                e.preventDefault();
              }} onKeyDown={(e) => {
                if (e.key === "Enter") {
                  //回车
                  setSearch(searchValue)
                }
              }}><Input placeholder={'输入接口名称或者接口ID搜索'} onChange={(e) => setSearchValue(e.target.value)}
                suffix={<><Button size={'small'} type={'text'} onClick={() => {
                  setSearch(searchValue)
                  handleSearch()
                }}
                  icon={<SearchOutlined />}></Button></>} /></div>
            </div>
          </>
        }} activeKey={activeKey} onChange={setActiveKey} items={[
          {
            label: "全部接口",
            key: "0"
          },
          {
            label: "免费接口",
            key: "1"
          },
          {
            label: "会员接口",
            key: "2",
          },
          {
            label: "计次接口",
            key: "3"
          }
        ]}>
        </Tabs>
        <Spin spinning={loading}>
          <div className={''}>
            <div className='grid xl:grid-cols-3 grid-cols-1 gap-5'>
              {apis.length > 0 && apis.map((e) => (
                <div key={e.id} className={'api-item'}>
                  <Card className={'min-h-[170px] md:min-h-[150px]'}>
                    <div className='p-2 flex'>
                      <div className='flex-1 pl-5'>
                        <div className='flex-none items-center mb-[10px]'>
                          <div className={'flex justify-between md:flex-row flex-col'}>
                            <div className={'flex items-center '}>
                              <div className="w-[30px] h-[30px] text-xl bg-cover mr-2 inline-block" style={{
                                backgroundImage: `url(${e.api.img})`,
                                backgroundPosition: "50%"
                              }}>
                              </div>
                              <span className='text-xl'>{e.api.name}</span>
                              <Tag color={'blue'} className='ml-1 text-xs md:block hidden'>ID:{e.api.id}</Tag>
                              <span className="md:block hidden">{e.api.type == 1 ?
                                <Tag color={PlanColor.free}>免费</Tag> : (e.api.type == 2 ? <Tag color={PlanColor.vip}>会员</Tag> :
                                  <Tag color={PlanColor.pro}>计次</Tag>)}</span>
                            </div>
                            <div className={'md:mt-0 mt-2 md:block hidden'}>
                              {e.api.type == 3 &&
                                <Link href={`/purchase/${e.api_id}`} target={"_blank"}><Button ghost
                                  type={'primary'}>再次购买</Button></Link>}
                              {e.api.type != 3 &&
                                <Link href={'/vip'} target={"_blank"}><Button type={'default'}>升级会员</Button></Link>}
                            </div>
                          </div>
                        </div>
                        <div className='w-full mt-1 flex-1 h-[40px]'>
                          {e.api.type == 3 && <div>
                            <p className='text-xs text-gray-500 mt-1'>
                              <span className={'text-gray-500'}>已用次数：</span>
                              <span className={'text-red-500'}>{e.record?.use_num}次</span>
                            </p>
                            <p className='text-xs text-gray-500 mt-1'>
                              <span className={'text-gray-500'}>剩余次数：</span>
                              <span className={'text-red-500'}>{e.record?.can_use_num}次</span>
                            </p>
                          </div>}
                          {(e.api.type == 1 || e.api.type == 2) && <div>
                            <p
                              className={'text-xs text-gray-500 mt-1'}>限每天{e.request.daily_limit}次请求，开通「高级会员」最高不限次数</p>
                            <p className={'text-xs text-gray-500 mt-1'}><span
                              className={'text-gray-500'}>当天剩余次数：</span>
                              <span className={'text-red-500'}>{e.request?.can_use_num == -1 ? '不限' : e.request?.can_use_num}次</span></p>
                          </div>}

                        </div>
                        <div className='md:mt-2 mt-5 border-dotted  border-t'>
                          <div className={'flex items-center mt-3'}>
                            <Space>
                              <Typography.Link onClick={async () => {
                                setStatApiId(e.api.id)
                                setStatModalOpen(true)
                                setStatTitle(e.api.name + " - 接口调用统计")
                              }}>统计</Typography.Link>
                              <Link href={`/test/${e.api.id}`} target={'_blank'}>测试</Link>
                              <Link href={`/api/${e.api.id}/api_document`} target={'_blank'}>文档</Link>
                              {e.api.type == 3 && <Typography.Link onClick={() => {
                                setRecordModalOpen(true)
                                setRecordData(e?.records || [])
                              }}>次数包</Typography.Link>}
                              {e.api.type == 3 && <Typography.Link onClick={() => {
                                setCurrentApi(e.api);
                                setAlertModalOpen(true);
                              }}>预警</Typography.Link>}
                            </Space>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card>
                </div>
              ))}
            </div>
            <div>
              {apis.length <= 0 && <div className={'flex items-center justify-center'}>
                <Empty className={'text-center'}></Empty>
              </div>}
            </div>
          </div>
        </Spin>

        <div className='mt-5 float-right'>
          <Pagination total={total} current={page} showTitle onChange={(page) => {
            setPage(page)
          }} />
        </div>
      </Card>

      <div className={'mt-5'}>
        <Alert closable type={'info'} onClose={() => {
          // setAlertClose(true)
          window.localStorage.setItem("api_alert_close", String(true))
        }} description={<div className={'flex flex-col gap-y-2'}>
          <div>
            1.不同的会员每天免费接口和会员接口可用调用次数不同
          </div>
          <div>
            2.只有成功请求才消耗次数，失败不计入次数统计 (暂时策略，可能会调整)
          </div>
        </div>}></Alert>
      </div>

      <div>
        <Modal width={700} style={{}} className={''} footer={null} title={statTitle}
          open={statModalOpen}
          onCancel={() => setStatModalOpen(false)}>
          <div className={'mb-2 mt-5'}>
            <RangePicker
              defaultValue={statDate}
              // maxDate={dayjs()}
              // minDate={dayjs(dayjs().subtract(1,'month'),dateFormat)}
              presets={rangePresets}
              onChange={(dates: null | (Dayjs | null)[], dateStrings: string[]) => {
                if (dates) {
                  //@ts-ignore
                  setStatDate(dates)
                  handleLoadStatData(dateStrings[0], dateStrings[1])
                }
              }}
            />
          </div>
          <Spin spinning={statLoading}>
            <div>
              <LineChart width={650} height={450} data={{
                id: "stat",
                values: statData,
              }}>
                <Line xField={"date"} yField={"num"} seriesField={'num_field'}></Line>
                <Line xField={"date"} yField={"success_num"} seriesField={'success_num_field'}></Line>
                <Line xField={"date"} yField={"fail_num"} seriesField={'fail_num_field'}></Line>
              </LineChart>
            </div>
          </Spin>
        </Modal>
      </div>

      <div>
        <Modal width={650} title={'接口次数包'} footer={null} open={recordModalOpen}
          onCancel={() => setRecordModalOpen(false)}>
          <Table pagination={false} rowKey={'id'} dataSource={recordData} columns={[
            {
              dataIndex: "id",
              title: "编号",
              width: 100,
            },
            {
              dataIndex: "num",
              title: "总次数",
            },
            {
              dataIndex: "use_num",
              title: "已用次数"
            },
            {
              dataIndex: "expire_time",
              title: "过期时间",
              sorter: function (a, b) {
                return (new Date(a.expire_time)).getTime() > (new Date(b.expire_time)).getTime() ? -1 : 1
              },
            },
            {
              dataIndex: "apply_time",
              title: "创建时间",
            }
          ]}>

          </Table>
        </Modal>
      </div>

      <ModalForm
        title={`${currentApi?.name} - API预警设置`}
        open={alertModalOpen}
        onOpenChange={setAlertModalOpen}
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setAlertModalOpen(false);
            setCurrentApi(undefined);
          },
          confirmLoading: alertLoading
        }}
        width={450}
        request={async () => {
          if (currentApi) {
            const data = await getApiUsageAlert(currentApi!.id)
            console.log(data)
            return data
          }
          return alertData
        }}
        onFinish={async (values) => {
          try {
            await updateApiUsageAlert({
              api_id: currentApi!.id,
              ...values
            });
            message.success('更新成功');
            setAlertModalOpen(false);
            return true;
          } catch (e) {
            message.error(e.message || '更新失败');
            return false;
          }
        }}
      >
        <Spin spinning={alertLoading}>
          <ProFormRadio.Group
            name="status"
            label="是否开启预警"
            rules={[{ required: true }]}
            initialValue={alertData?.status}
            valueEnum={{
              "Y": "开启",
              "N": "关闭"
            }}
          />

          <ProFormDependency name={['status']}>
            {({ status }) => {
              if (status === 'Y') {
                return (
                  <>
                    <ProFormDigit
                      name="threshold"
                      label="预警阈值"
                      min={1}
                      rules={[{ required: true }]}
                      extra="当剩余可用次数小于该值时触发预警"
                    />

                    <ProFormSlider
                      name={'hours'}
                      label="预警时间"
                      range
                      min={0}
                      max={23}
                      marks={{
                        0: '0时',
                        6: '6时',
                        12: '12时',
                        18: '18时',
                        23: '23时'
                      }}
                      rules={[{ required: true }]}
                      help="设置预警时间，当剩余可用次数小于阈值时，在设置的时间段内会触发预警"
                    // ={[0,23]}
                    />
                  </>
                );
              }
              return null;
            }}
          </ProFormDependency>
        </Spin>
      </ModalForm>
    </div>
  </>
}