"use client"
import { <PERSON><PERSON>, <PERSON>, Modal } from "antd";
import {
  ProFormItem,
  ProTable
} from "@ant-design/pro-components";
import { ProColumnType } from "@ant-design/pro-table";
import { RequestLog } from "@/app/(dashboard)/dashboard/data/api_log/data";
import DateTimeRangePicker from "@/components/antd/DateTimePicker";
import { getApiLogs, getUserApis } from "@/app/(dashboard)/dashboard/data/api_log/api";
import { EyeOutlined } from "@ant-design/icons";
import { useEffect, useState } from "react";
import Link from "next/link";
import JsonView from "@uiw/react-json-view";
import { JsonParse } from "@/utils/helper";
import dayjs from "dayjs";

export default function Page() {
  const [apis, setApis] = useState<{ id: number; name: string }[]>([])
  const [apiEnum, setApiEnum] = useState({})
  const [modalOpen, setModalOpen] = useState(false)
  const [current, setCurrent] = useState<RequestLog>()
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  const columns: ProColumnType<RequestLog>[] = [
    {
      valueType: 'option',
      width: 100,
      render: function (dom, entity, index, action, schema) {
        return <Button title={'查看日志'} size={'small'} icon={<EyeOutlined />} onClick={() => {
          setCurrent(entity)
          setModalOpen(true)
        }}></Button>
      },
      align: "center"
    },
    {
      dataIndex: "log_id",
      title: "日志ID",
      width: 200,
      align: "center"
    },
    {
      dataIndex: "api_id",
      title: "接口",
      align: "center",
      valueType: "select",
      valueEnum: apiEnum,
      fieldProps: {
        showSearch: true,
        maxCount: 1,
        mode: "tags"
      },
      search: {
        transform: function (value, namePath, allValues) {
          if (value) {
            allValues[namePath] = value[0]
          }
          return allValues
        }
      },
      render: function (dom, entity, index, action, schema) {
        return <Link href={`/api/${entity.api_id}/api_document`} target={"_blank"}>{dom}</Link>
      }
    },
    {
      dataIndex: "client_ip",
      title: "请求IP",
      search: false,
      align: "center"
    },
    {
      dataIndex: "response_code",
      title: "状态码",
      search: false,
      width: 100,
      align: "center"
    },
    {
      dataIndex: "duration_ms",
      title: "耗时(毫秒)",
      search: false,
      width: 100,
      align: "center"
    },
    {
      dataIndex: "request_timestamp",
      title: "请求时间",
      search: {
        transform: (value, namePath, allValues) => {
          if (value) {
            const start_date = value[0] + " " + value[1]
            const end_date = value[0] + " " + value[2]
            allValues["start_time"] = start_date
            allValues["end_time"] = end_date
          }
          return allValues
        }
      },
      renderFormItem: function (schema, config, form) {
        //@ts-ignore
        return <div className="w-full">
          <ProFormItem
            initialValue={[dayjs().format('YYYY-MM-DD'), "00:00", "23:59"]}
            name={"request_timestamp"}
          >
            <DateTimeRangePicker />
          </ProFormItem>
        </div>
      },
      align: "center",
    },
  ]

  const loadUserApis = async () => {
    const response = await getUserApis()
    if (response && Array.isArray(response)) {
      setApis(response)
      const temp: Record<number, string> = {}
      for (const datum of response) {
        temp[datum.id] = datum.name
      }
      setApiEnum(temp)
    }
  }
  useEffect(() => {
    loadUserApis()
  }, []);

  return <div>
    <Card title={'接口调用日志'} bordered={false}>
      <ProTable<RequestLog> search={{
        span: {
          xs: 24,
          sm: 24,
          md: 12,
          lg: 12,
          xl: 8,
          xxl: 6
        },
        defaultCollapsed: false,
        labelWidth: 'auto',
        optionRender: (searchConfig, formProps, dom) => [
          ...dom.reverse(),
        ],
        layout: isMobile ? 'vertical' : 'horizontal',
        className: 'mb-4'
      }} request={async (params) => {
        return await getApiLogs(params)
      }} debounceTime={100} columns={columns}
        scroll={{ x: 700 }}
        cardProps={{
          bodyStyle: { padding: '0px 8px 8px' }
        }}
      >

      </ProTable>
    </Card>
    <div>
      <Modal width={650} title={'日志详情'} footer={null} open={modalOpen} onCancel={() => setModalOpen(false)}>
        <Card className={'mb-5 mt-5 max-h-[500px] overflow-auto'} title={'请求'} bordered={true}>
          <JsonView enableClipboard={false} displayObjectSize={false} displayDataTypes={false} collapsed={1}
            value={JsonParse(current?.request_payload)}></JsonView>
        </Card>
        <Card className={'max-h-[500px] overflow-auto'} title={'响应'} bordered={true}>
          <JsonView enableClipboard={false} displayObjectSize={false} displayDataTypes={false} collapsed={1}
            value={JsonParse(current?.response_payload)}></JsonView>
        </Card>
      </Modal>
    </div>
  </div>
}