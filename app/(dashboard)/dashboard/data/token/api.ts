import axiosInstance from "@/lib/axios";

export async function getUserTokens(params: any) {
  return axiosInstance.get("/frontend/dashboard/token/index", {params})
}

export async function createUserToken(data: any) {
  return axiosInstance.post("/frontend/dashboard/token/save", data)
}

export async function updateUserToken(id: number, data: any) {
  return axiosInstance.put("/frontend/dashboard/token/update/" + id, data)
}

export async function deleteUserToken(id: number) {
  return axiosInstance.delete("/frontend/dashboard/token/delete/" + id)
}