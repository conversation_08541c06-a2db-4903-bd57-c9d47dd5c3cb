import {useCallback, useEffect, useRef, useState} from "react";
import {
  ActionType,
  ModalForm,
  ProFormDependency,
  ProFormRadio, ProFormSelect, ProFormText,
  ProFormTextArea,
  ProTable
} from "@ant-design/pro-components";
import {UserToken} from "@/app/(dashboard)/dashboard/data/token/data";
import {getUserApis} from "@/app/(dashboard)/dashboard/data/api_log/api";
import {
  createUserToken,
  deleteUserToken,
  getUserTokens,
  updateUserToken
} from "@/app/(dashboard)/dashboard/data/token/api";
import {Button, message, Popconfirm, Tag, Typography} from "antd";
import {ProColumnType} from "@ant-design/pro-table";
import {ENABLE} from "@/config/data";
import {convertArrayToString, convertStringToArray} from "@/app/(dashboard)/dashboard/data/token/page";

export default function TokenTable({onSelect}: { onSelect(token: UserToken): void }) {
  const [createModalOpen, setCreateModalOpen] = useState(false);
  const actionRef = useRef<ActionType>();
  const [current, setCurrent] = useState<UserToken | null>(null);

  const [apiEnum, setApiEnum] = useState({})

  // 重置表单状态
  const resetForm = useCallback(() => {
    if (!createModalOpen) {
      setCurrent(null);
    }
  }, [createModalOpen]);

  useEffect(() => {
    resetForm();
  }, [createModalOpen, resetForm]);


  const loadUserApis = async () => {
    const data = await getUserApis()
    //@ts-ignore
    const temp = {}
    for (const datum of data) {
      temp[datum.id] = datum.name
    }
    setApiEnum(temp)
  }
  useEffect(() => {
    loadUserApis()
  }, []);

  // 处理表单提交
  const handleFormSubmit = async (formData: FormData) => {
    try {
      const processedData = {
        ...formData,
        ip: convertStringToArray(formData.ip || ''),
        referer: convertStringToArray(formData.referer || ''),
      };

      if (current) {
        await updateUserToken(current.id, processedData);
        message.success("更新成功");
      } else {
        await createUserToken(processedData);
        message.success("创建成功");
      }

      setCreateModalOpen(false);
      actionRef.current?.reload();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '操作失败';
      message.error(errorMessage);
    }
  };

  // 处理删除
  const handleDelete = async (id: number) => {
    try {
      await deleteUserToken(id);
      message.success("删除成功");
      actionRef.current?.reload();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '删除失败';
      message.error(errorMessage);
    }
  };

  const columns: ProColumnType<UserToken>[] = [
    {
      dataIndex: "token",
      title: "Token",
      align: "start",
      render: (_, entity) => (
        <Typography.Text className={'cursor-pointer'} onClick={() => {
          onSelect(entity)
        }} copyable={{text: entity.token}}>
          <Tag>{entity.token}</Tag>
        </Typography.Text>
      ),
      width: 300,
    },
    {
      dataIndex: "apis",
      title: "可调接口",
      align: "center",
      render: (_, entity) => entity.allow_all ? '全部' : <div>
        {entity.apis.slice(0, 5).map((id) => {
          return <Tag key={id}>{apiEnum[id]}</Tag>
        })}
        {entity.apis.length > 5 && '...'}
      </div>,
      search: false,
      width: 450,
    },
    {
      dataIndex: "status",
      title: "状态",
      align: "center",
      render: (_, entity) => (
        <Tag color={entity.status === ENABLE ? 'green' : 'red'}>
          {entity.status === ENABLE ? '启用' : '禁用'}
        </Tag>
      ),
      search: false,
    },
    {
      dataIndex: "remark",
      title: "备注",
      search: false,
    },
    {
      dataIndex: "created_at",
      title: "创建时间",
      search: false,
      width: 150,
    },
    {
      dataIndex: "option",
      valueType: "option",
      title: "操作",
      align: "center",
      render: (_, entity) => (
        <div className="space-x-2">
          <Button
            type="link"
            onClick={() => {
              setCurrent(entity);
              setCreateModalOpen(true);
            }}
          >
            更新
          </Button>
          <Popconfirm
            title="确认删除吗？"
            description="删除后不可恢复"
            onConfirm={() => handleDelete(entity.id)}
          >
            <Button size="small" type="text" danger>
              删除
            </Button>
          </Popconfirm>
        </div>
      ),
      search: false,
    }
  ];

  // 获取初始值
  const getInitialValues = async () => {
    if (current) {
      return {
        ...current,
        ip: convertArrayToString(current.ip),
        referer: convertArrayToString(current.referer),
      };
    }
    return {
      remark: "",
      ip: "",
      allow_all: 1,
    };
  };

  return (
    <div className="space-y-5">
      <ProTable<UserToken>
        bordered={false}
        search={false}
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        request={getUserTokens}
        toolbar={{}}
        toolBarRender={() => [
          <Button
            type="primary"
            key="add"
            onClick={() => setCreateModalOpen(true)}
          >
            创建token
          </Button>
        ]}
      />
      <ModalForm<FormData>
        title={current ? '更新token' : '创建token'}
        open={createModalOpen}
        onOpenChange={setCreateModalOpen}
        modalProps={{
          destroyOnClose: true,
        }}
        request={getInitialValues}
        onFinish={handleFormSubmit}
      >
        <div className="space-y-4">
          <ProFormTextArea
            name="ip"
            label="IP白名单"
            placeholder="IP白名单，一行一个"
            help={
              <div>
                <p>IP白名单，一行一个</p>
                <p>如不限制IP请求限制，请不要填写</p>
              </div>
            }
          />

          <ProFormTextArea
            name="referer"
            label="来源白名单"
            placeholder="来源白名单，一行一个"
            help={
              <div>
                <p>来源白名单，一行一个</p>
                <p>如不限制请求来源限制，请不要填写</p>
              </div>
            }
          />

          <ProFormRadio.Group
            name="allow_all"
            label="接口限制"
            initialValue={1}
            options={[
              {label: "全部", value: 1},
              {label: "部分", value: 0}
            ]}
          />

          <ProFormDependency name={["allow_all"]}>
            {(values) =>
              values.allow_all === 0 && (
                <ProFormSelect
                  name="apis"
                  label="允许请求的接口"
                  fieldProps={{
                    mode: "tags",
                    size: "large",
                  }}
                  rules={[
                    {
                      required: true,
                      message: "接口限制选择部分则允许请求的接口不允许为空"
                    }
                  ]}
                  request={async () => {
                    const apis = await getUserApis();
                    return apis.map(api => ({
                      label: api.name,
                      value: api.id,
                    }));
                  }}
                />
              )
            }
          </ProFormDependency>

          {current && <ProFormRadio.Group
            //@ts-ignore
            valueEnum={{
              Y: "启用",
              N: "禁用"
            }}
            name={'status'} label={'状态'}/>}

          <ProFormText name="remark" label="备注"/>
        </div>
      </ModalForm>
    </div>
  );
}