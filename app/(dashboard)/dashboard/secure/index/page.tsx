"use client"
import {<PERSON><PERSON>, <PERSON><PERSON>, Card, Divider, List, message, Tag} from "antd";
import {Lock, LucideMail, Phone} from 'lucide-react'
import {CheckCircleTwoTone, WechatOutlined} from "@ant-design/icons";
import {ModalForm, ProFormText} from "@ant-design/pro-components";
import {useEffect, useState} from "react";
import {getUserProfile} from "@/app/(dashboard)/dashboard/workspace/api";
import {updateUserPassword} from "@/app/(dashboard)/dashboard/secure/index/api";

export default function Page() {
  const [user, setUser] = useState({})

  const loadUserProfile = async () => {
    try {
      const data = await getUserProfile()
      setUser(data)
    } catch {
    }
  }
  useEffect(() => {
    loadUserProfile()
  }, []);

  const [updatePasswordOpen, setUpdatePasswordOpen] = useState(false)
  const data = [
    {
      title: '登录密码',
      icon: <Lock size={32}/>,
      type: "password",
      description: "安全性高的密码可以使帐号更安全。建议您定期更换密码，设置6-20 位 登录密码。",
    },
    {
      title: '安全手机',
      icon: <Phone size={32}/>,
      type: 'phone',
      description: '安全手机可以用于登录帐号，重置密码或其他安全验证。'
    },
    {
      title: '绑定邮箱',
      icon: <LucideMail size={32}/>,
      type: "email",
      description: '邮箱可用于登录账号，重置密码或其他安全验证。',
    },
    {
      title: '绑定微信',
      icon: <WechatOutlined style={{fontSize: '32px',}}/>,
      type: 'wechat',
      description: '您的微信可以用来快捷登录帐号，接收微信公众号发送的各类系统、营销、服务通知。'
    },
  ];
  return <div>
    <Card title={'安全设置'} bordered={false}>
      <div className={'h-[150px] mb-2 items-center flex bg-[#fafffa] rounded border border-[#e6fbe4]'}>
        <div className={'h-[150px] w-[150px] ml-5'} style={{
          background: "url(data:image/png;base64,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) no-repeat 50%"
        }}>
        </div>
        <div>
          <div className={'flex items-center'}>
            <div className={'text-xl font-medium '}>
              安全等级
            </div>
            <div className={'ml-5'}>
              <Tag color={'success'}>中</Tag>
            </div>
          </div>
          <div className={'mt-2 text-[#8c8c8c]'}>
            您与安全达人仅一步之遥
          </div>
        </div>
      </div>

      {data.map((item, index) => (
        <div key={index}>
          <div className={'h-[60px] flex items-center justify-between'}>
            <div className={'flex items-center gap-x-5'}>
              <div className={'flex'}>
                {item.icon}
              </div>
              <div>
                <p className={'font-medium font-bold'}>{item.title}</p>
                <p className={'text-[#8c8c8c] md:block hidden'}>{item.description}</p>
              </div>
            </div>
            <div className={'flex'}>
              <div>
                {(item.type === 'password' || item.type === 'phone') && <CheckCircleTwoTone/>}
              </div>
              <div>
                <Divider type="vertical"/>
              </div>
              <div>
                {item.type === 'password' ?
                  <Button size={'small'} type={'link'} onClick={() => setUpdatePasswordOpen(true)}>修改</Button> :
                  <Button size={'small'} type={'link'} disabled title={'暂不支持'}>修改</Button>}
              </div>
            </div>
          </div>
          {index != data.length - 1 && <Divider/>}
        </div>
      ))}
    </Card>
    <ModalForm title={'更新密码'} onOpenChange={(v) => setUpdatePasswordOpen(v)} open={updatePasswordOpen}
               initialValues={{
                 password: "",
                 password_confirmation: ""
               }} modalProps={{
      destroyOnClose: true,
    }} onFinish={async (data) => {
      console.log(data)
      try {
        await updateUserPassword(data)
        message.success("更新成功")
        setUpdatePasswordOpen(false)
      } catch (e) {
        message.error(e.message || '更新失败')
      }
    }}>
      <ProFormText.Password label={'新密码'} rules={[{required: true, message: "请输入新密码"}]}
                            name={'password'}></ProFormText.Password>
      <ProFormText.Password label={'确认密码'} name={"password_confirmation"}
                            rules={[{required: true}, ({getFieldValue}) => ({
                              validator(_, value) {
                                if (!value || getFieldValue('password') === value) {
                                  return Promise.resolve();
                                }
                                return Promise.reject(new Error('两次密码不一致'));
                              },
                            }),]}></ProFormText.Password>
    </ModalForm>
  </div>
}