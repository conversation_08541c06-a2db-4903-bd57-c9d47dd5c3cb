"use client"

import {Card} from "antd";
import {ProTable} from "@ant-design/pro-components";
import {ProColumnType} from "@ant-design/pro-table";
import {getLoginLogs} from "@/app/(dashboard)/dashboard/secure/log/api";

export default function Page() {
  const columns: ProColumnType[] = [{
    dataIndex: "id",
    title: "编号",
    search: false,
    renderText: function (text, record, index, action) {
      return index + 1
    }
  },
    {
      dataIndex: "login_time",
      title: "登录时间",
      renderText: function (text, record) {
        return <span>{record.login_time}</span>
      },
    },
    {
      dataIndex: "ip",
      title: "登录IP",
      search: false,
    },
    {
      dataIndex: "address",
      title: "登录地址",
      search: false,
    },
  ];
  return <>
    <Card bordered={false} title={'登录日志'}>
      <ProTable pagination={{
        pageSize:10,
      }} search={false} columns={columns} request={async (params, sort, filter) => {
        try {
          return await getLoginLogs(params)
        } catch {
          return {}
        }
      }}
                scroll={{x:700}}
      ></ProTable>
    </Card>
  </>
}