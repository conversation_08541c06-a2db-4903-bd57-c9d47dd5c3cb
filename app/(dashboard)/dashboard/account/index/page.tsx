"use client"

import { <PERSON><PERSON>, Button, Descriptions, message, Tag, Tooltip, Typography } from "antd";
import { useEffect, useState } from "react";
import { getUserProfile } from "@/app/(dashboard)/dashboard/workspace/api";
import { ProCard } from "@ant-design/pro-components";
import { ENABLE } from "@/config/data";
import { QuestionCircleFilled } from "@ant-design/icons";
import Link from "next/link";
import { updateUserNickName, deleteAccount } from "@/app/(dashboard)/dashboard/account/index/api";
import AccountDeleteModal from "@/components/common/account-delete-modal";

export default function Page() {
  const [loading, setLoading] = useState(false)
  const [user, setUser] = useState<any>()
  const [deleteModalOpen, setDeleteModalOpen] = useState(false)

  function handleGetUserProfile() {
    setLoading(true)
    getUserProfile().then((res) => {
      setUser(res)
    }).catch((e) => {
      console.log(e)
    }).finally(() => {
      setLoading(false)
    })
  }

  useEffect(() => {
    handleGetUserProfile()
  }, []);

  function handleDeleteAccount() {
    setDeleteModalOpen(true)
  }

  // 处理账号注销确认
  const handleDeleteConfirm = async (data: { reason: string; code: string }) => {
    try {
      await deleteAccount(data)
      message.success('账号注销成功')
      setDeleteModalOpen(false)
      // 注销成功后可以跳转到登录页或首页
      window.location.href = '/auth/login'
    } catch (e: any) {
      throw e // 让Modal组件处理错误显示
    }
  }

  // 关闭注销Modal
  const handleDeleteCancel = () => {
    setDeleteModalOpen(false)
  }

  return <>
    <div className={'grid grid-cols-2 gap-10'}>
      <div className={'col-span-2 md:col-span-1'}>
        <ProCard loading={loading}>
          <div className={'text-center'}>
            <Avatar size={72}>用户</Avatar>
          </div>
          <div className={'mt-5'}>
            <Descriptions column={1} bordered={true} size={'small'}>
              <Descriptions.Item label={'昵称'}>
                <Typography.Text className={'mt-2'} editable={{
                  onChange: async function (value) {
                    try {
                      await updateUserNickName(value)
                      handleGetUserProfile()
                      message.success('更新成功')
                    } catch (e: any) {
                      message.error(e.message || '更新失败')
                    }
                  }
                }}>{user?.nickname}</Typography.Text>
              </Descriptions.Item>
              <Descriptions.Item label={'用户名'}>
                {user?.username}
              </Descriptions.Item>
              <Descriptions.Item label={'手机号'}>
                {user?.phone}
              </Descriptions.Item>
              <Descriptions.Item label={'邮箱'}>
                {user?.email}
              </Descriptions.Item>
              <Descriptions.Item label={'账号状态'}>
                {user?.status === ENABLE ? <Tag color={'success'}>正常</Tag> : <Tag color={'red'}>异常</Tag>}
              </Descriptions.Item>
              <Descriptions.Item label={'创建时间'}>
                {user?.created_at}
              </Descriptions.Item>
              <Descriptions.Item label={'更新时间'}>
                {user?.updated_at}
              </Descriptions.Item>
              <Descriptions.Item label={'操作'}>
                <div className={'flex gap-x-1'}>
                  <Link href={`/dashboard/secure/index`}><Button type={'primary'} size={'small'}>重置密码</Button></Link>
                  <Button danger size={'small'} onClick={handleDeleteAccount}>账号注销</Button>
                </div>
              </Descriptions.Item>
            </Descriptions>
          </div>
        </ProCard>
      </div>
      <div className={'col-span-2 md:col-span-1'}>
        <ProCard>
          <div>
            <div className={'text-center text-xl'}>会员信息</div>
            <div className={'mt-5'}>
              <Descriptions bordered={true} column={1} size={'small'}>
                <Descriptions.Item label={'当前套餐'}>
                  {user?.plan?.name}
                </Descriptions.Item>
                <Descriptions.Item label={<div>
                  免费接口数量<span className={'ml-2'}> <Tooltip
                    title={'可以申请的免费接口及会员接口的数量，计次接口不限制，可以直接申请'}>
                    <QuestionCircleFilled></QuestionCircleFilled>
                  </Tooltip></span>
                </div>}>
                  {user?.plan?.api_limit === -1 ? '不限制' : user?.plan?.api_limit}

                </Descriptions.Item>
                <Descriptions.Item label={<div>
                  每日可请求次数<span className={'ml-2'}> <Tooltip
                    title={'已申请的免费接口和会员接口每日分别可以请求的次数'}>
                    <QuestionCircleFilled></QuestionCircleFilled>
                  </Tooltip></span>
                </div>}>
                  {user?.plan?.daily_limit === -1 ? '不限制' : user?.plan?.daily_limit}

                </Descriptions.Item>
                <Descriptions.Item label={<div>
                  QPS
                  <span className={'ml-2'}> <Tooltip title={'接口每秒请求频率限制'}>
                    <QuestionCircleFilled></QuestionCircleFilled>
                  </Tooltip></span>
                </div>}>
                  {user?.plan?.request_qps === -1 ? '不限制' : user?.plan?.request_qps}

                </Descriptions.Item>
                <Descriptions.Item label={'扩展功能'}>
                  暂无
                </Descriptions.Item>
                <Descriptions.Item label={'到期时间'}>
                  {user?.is_free_plan ? '免费套餐无时间限制' : user?.plan_end_at}
                </Descriptions.Item>
                <Descriptions.Item label={'操作'}>
                  <div className={'flex'}>
                    <Link href={'/vip'}><Button size={'small'} type={'primary'}>续费</Button></Link>
                  </div>
                </Descriptions.Item>
              </Descriptions>
            </div>
          </div>
        </ProCard>
      </div>
    </div>

    {/* 账号注销Modal */}
    <AccountDeleteModal
      open={deleteModalOpen}
      onCancel={handleDeleteCancel}
      onConfirm={handleDeleteConfirm}
      userPhone={user?.phone}
    />
  </>
}