"use client"
import {<PERSON><PERSON>, <PERSON>, message} from "antd";
import {useEffect, useState} from "react";
import {Message} from "@/app/(dashboard)/dashboard/account/message/data";
import {readMessage} from "@/app/(dashboard)/dashboard/account/message/api";
import {useRouter} from "next/navigation";

export default function Page({params}: { params: { id: number } }) {
  console.log(params)
  const [messageInfo, setMessageInfo] = useState<Message>()
  const router = useRouter()
  useEffect(() => {
    readMessage(params.id).then((res) => {
      setMessageInfo(res)
    }).catch((e) => {
      message.error(e.message || "消息不存在")
      router.push("/dashboard/account/message")
    })

  }, []);
  return <div>
    <Card title={'消息详情'} bordered={false} extra={<>
      <Button type={'link'} onClick={() => {
        router.back()
      }}>返回</Button>
    </>}>
      <div className={'flex flex-col gap-1'}>
        <div className={'text-bold text-xl'}>{messageInfo?.title}</div>
        <div className={'text-xs'}>{messageInfo?.created_at}</div>
        <div className={'mt-5 message-content'}>
          <div className={'prose max-w-none'} dangerouslySetInnerHTML={{__html: messageInfo?.content || ""}}></div>
        </div>
      </div>
    </Card>
  </div>
}