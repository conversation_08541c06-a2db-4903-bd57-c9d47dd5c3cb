"use client"

import { useRef, useState } from "react";
import { ActionType, ProList } from "@ant-design/pro-components";
import { getMessageList } from "@/app/(dashboard)/dashboard/account/message/api";
import { Card } from "antd";
import { useRouter } from "next/navigation";

export default function Message() {
  const action = useRef<ActionType>();

  const [activeKey, setActiveKey] = useState<React.Key | undefined>('personal');

  const router = useRouter()

  return <Card bordered={false} title={'消息中心'}>
    <ProList<any>
      rowKey="id"
      actionRef={action}
      metas={{
        title: {
          dataIndex: "title",
          render: function (dom, entity) {
            return <div onClick={() => {
              if (entity.message_type === 'global') {
                router.push(`message/${entity.id}`)
              } else {
                router.push(`message/${entity.message_id}`)
              }
            }}>{entity.title}</div>
          }
        },
        description: {
          dataIndex: 'created_at',
        },
      }}
      request={async function (params) {
        try {
          return getMessageList(Object.assign(params, { message_type: activeKey }))
        } catch (e) {
          return {}
        }
      }}
      toolbar={{
        menu: {
          activeKey,
          items: [
            {
              key: 'personal',
              label: (
                <span>
                  个人消息
                </span>
              ),
            },
            {
              key: 'global',
              label: (
                <span>公共消息</span>
              ),
            },
          ],
          onChange(key) {
            setActiveKey(key);
            action.current?.reload()
          },
        },
      }}
    />
  </Card>
}