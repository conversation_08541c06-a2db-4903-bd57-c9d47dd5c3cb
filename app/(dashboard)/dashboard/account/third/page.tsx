"use client"


import { Lock, LucideMail, Phone } from "lucide-react";
import { CheckCircleTwoT<PERSON>, WechatOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, <PERSON><PERSON>, Badge, <PERSON>ton, Card, Divider, message, Popconfirm, Progress, Tag } from "antd";
import { useEffect, useState } from "react";
import { bindThird, getThirds, unbindThird } from "@/app/(dashboard)/dashboard/account/third/api";
import { ICONFONT } from "@/components/antd/icon";
import { ProTable } from "@ant-design/pro-components";
import { env } from "@/env";
import { signIn } from "next-auth/react";

export default function Page() {
  const [thirds, setThirds] = useState([])

  const handleLoadThird = () => {
    getThirds().then((data) => {
      //@ts-ignore
      setThirds(data)
    }).catch(e => {
      message.success(e.message)
    })
  }

  const openThirdModal = (provider: string) => {
    let isProcessing = false;

    const openWin = window.open(
      env.NEXT_PUBLIC_API_URL + `/frontend/auth/oauth/redirect/${provider}`,
      `Login ${provider}`,
      'height=500,width=500,top=0,left=0,toolbar=no,menubar=no,scrollbars=no,resizable=no,location=no,status=no'
    );

    const receiveMessage = async function (event) {

      const data = event.data;
      if (data.data && !isProcessing) {
        try {
          isProcessing = true;
          await bindThird(provider, data.data);
          handleLoadThird();
          message.success("绑定成功");
        } catch (e) {
          message.error(e.message || '绑定失败');
        } finally {
          isProcessing = false;
        }
      }
    }

    const controller = new AbortController();
    window.addEventListener('message', receiveMessage, {
      signal: controller.signal
    });

    const checkWindow = setInterval(() => {
      if (openWin.closed) {
        clearInterval(checkWindow);
        controller.abort();
      }
    }, 500);

    return () => {
      controller.abort();
      clearInterval(checkWindow);
      if (!openWin.closed) {
        openWin.close();
      }
    }
  }

  useEffect(() => {
    handleLoadThird();
  }, []);

  return <>
    <Card bordered={false} title={'第三方账号'}>
      <Alert showIcon type={'info'} description={'绑定多个第三方账号，允许你使用任意一个第三方账号登录同一个账号。\n'} />
      <div className={'mt-2'}>
        <ProTable rowKey={'code'} toolBarRender={false} bordered={true} cardBordered={true} search={false}
          pagination={false} columns={[
            {
              width: 150,
              dataIndex: "provider.name",
              title: "第三方平台名称",
              render: function (_, entity) {
                return <div className={'flex gap-x-2 items-center'}>
                  <ICONFONT type={entity.provider.icon} style={{
                    fontSize: '32px'
                  }} />
                  <span>{entity.provider.name}</span>
                </div>
              }
            },
            {
              width: 150,
              dataIndex: "provider.info",
              title: "第三方平台账号",
              render: function (_, entity) {
                return <>
                  {entity.bind && <div className={'flex gap-x-2 items-center'}>
                    {entity.info.avatar && <Avatar src={entity.info.avatar} />}
                    {entity.info.nickname || entity.info.name || '-'}
                  </div>}
                  {!entity.bind && '-'}
                </>
              }
            },
            {
              width: 150,
              dataIndex: "bind",
              title: "状态",
              render: function (_, entity) {
                return <div>
                  {entity.bind ? <div className={'flex gap-x-2 items-center'}>
                    <Badge status="success" />
                    已绑定
                  </div> : <div className={'flex gap-x-2 items-center'}>
                    <Badge status="default" />
                    未绑定
                  </div>}
                </div>
              }
            },
            {
              width: 150,
              title: "操作",
              valueType: "option",
              render: function (_, entity) {
                return <div>
                  {entity.bind ?
                    <Popconfirm
                      description={'解绑后可以重新绑定或者绑定新的账号'}
                      title={'确认解绑吗'}
                      onConfirm={async () => {
                        try {
                          await unbindThird(entity.provider.provider);
                          message.success("解绑成功");
                          handleLoadThird();
                        } catch (e) {
                          message.error(e.message || '解绑失败');
                        }
                      }}
                    >
                      <Button type={'default'} size={'small'}>解绑</Button>
                    </Popconfirm>
                    :
                    <Button
                      type={'primary'}
                      size={'small'}
                      onClick={() => {
                        const cleanup = openThirdModal(entity.provider.provider);
                        // 组件卸载时自动清理
                        return () => cleanup && cleanup();
                      }}
                    >
                      绑定
                    </Button>
                  }
                </div>
              }
            }
          ]} dataSource={thirds}
          scroll={{ x: 700 }}
        ></ProTable>
      </div>
    </Card>
  </>
}