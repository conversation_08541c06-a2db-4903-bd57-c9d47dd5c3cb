import axiosInstance from "@/lib/axios";

export async function getTaxInfo(){
  return axiosInstance.get("/frontend/dashboard/cost/invoice/get_tax_info")
}

export async function addTaxInfo(data:any){
  return axiosInstance.post("/frontend/dashboard/cost/invoice/add_tax_info",data)
}

export async function getInvoiceList(params:any){
  return axiosInstance.get("/frontend/dashboard/cost/invoice/list",{params})
}

export async function getCanApplyOrders(){
  return axiosInstance.get("/frontend/dashboard/cost/invoice/get_can_apply_orders")
}

export async function getCanApplyMoney(){
  return axiosInstance.get("/frontend/dashboard/cost/invoice/get_can_apply_money")
}
export async function applyInvoice(data){
  return axiosInstance.post("/frontend/dashboard/cost/invoice/apply_invoice",data)
}
export async function getInvoiceInfo(id){
  return axiosInstance.get("/frontend/dashboard/cost/invoice/read/"+id)
}