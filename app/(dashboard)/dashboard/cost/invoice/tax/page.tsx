"use client"

import {<PERSON><PERSON>, <PERSON>, <PERSON>, Divider, message, Row, Space, Typography} from "antd";
import Link from "next/link";
import {LeftCircleFilled} from "@ant-design/icons";
import {useRouter} from "next/navigation";
import {ChevronLeft} from "lucide-react";
import {
  ProForm,
  ProFormDependency,
  ProFormRadio,
  ProFormSelect,
  ProFormText
} from "@ant-design/pro-components";
import {addTaxInfo, getTaxInfo} from "@/app/(dashboard)/dashboard/cost/invoice/api";

export default function Page() {
  const router = useRouter()
  const [form] = ProForm.useForm();

  return <>
    <Card bordered={false} title={<>
      <div className={'flex gap-x-5'}>

        <div className={'flex'}>
          开票信息填写
        </div>
      </div>
    </>} extra={<div className={''}>
      <Typography.Link onClick={() => {
        router.push("/dashboard/cost/invoice")
      }} className={'flex items-center'}>
        <span>返回</span></Typography.Link>
    </div>}>
      <div className={'md:w-[500px] w-max-[500px] p-5'}>
        <ProForm
          request={async () => {
            try {
              const data = await getTaxInfo()
              console.log(data)
              return data
            } catch {
              return {}
            }
          }}
          labelCol={{span: 8}}
          wrapperCol={{span: 16}}
          form={form}
          onChange={async (v) => {
            console.log(form.getFieldsValue())
          }}
          className={''}
          layout={'horizontal'}
          onFinish={async (values) => {
            // 在这里可以处理表单提交
            try {
              await addTaxInfo(values)
              message.success('提交成功');
              router.push("/dashboard/cost/invoice")
            } catch (e) {
              message.error(e.message || '添加失败')
            }
          }}
          autoComplete="off"
          submitter={{
            render: (props, doms) => (
              <Row>
                <Col span={16} offset={8}>
                  <Space>{doms}</Space>
                </Col>
              </Row>
            )
          }}
        >
          <ProFormSelect
            name={'tax_type'}
            label={'发票类型'}
            options={[
              {
                label: "增值税普通发票",
                value: 1
              },
              {
                label: "增值税专用发票",
                value: 2
              }
            ]}
            initialValue={1}
            help={''}
          />
          <ProFormDependency name={['tax_type']}>
            {({tax_type}) => {
              if (tax_type == 2) {
                form.setFieldValue("invoice_type", 2)
              }
              return (
                <ProFormRadio.Group
                  label={'抬头类型'}
                  name={'invoice_type'}
                  options={[
                    {label: '个人', value: 1},
                    {label: '企业', value: 2}
                  ]}
                  initialValue={1}
                  fieldProps={{
                    disabled: tax_type == 2
                  }}
                />
              );
            }}
          </ProFormDependency>
          <ProFormText
            name={'tax_title'}
            label={'发票抬头'}
            rules={[
              {required: true}
            ]}
          />

          <ProFormDependency name={['tax_type', 'invoice_type']}>
            {({tax_type, invoice_type}) => {
              console.log(tax_type)
              console.log(invoice_type)
              return (
                <>
                  {invoice_type == 2 && (
                    <ProFormText
                      name={'tax_number'}
                      required
                      label={'税号'}
                      rules={[
                        {required: true}
                      ]}
                    />
                  )}
                  {tax_type == 2 && invoice_type == 2 && (
                    <>
                      <ProFormText
                        name={'tax_bank'}
                        required
                        label={'开户银行'}
                        rules={[
                          {required: true}
                        ]}
                      />
                      <ProFormText
                        name={'tax_bank_number'}
                        required
                        label={'银行卡号'}
                        rules={[
                          {required: true}
                        ]}
                      />
                      <ProFormText
                        name={'tax_address'}
                        required
                        label={'注册地址'}
                        rules={[
                          {required: true}
                        ]}
                      />
                      <ProFormText
                        name={'tax_phone'}
                        required
                        label={'联系电话'}
                        rules={[
                          {required: true}
                        ]}
                      />
                    </>
                  )}
                </>
              );
            }}
          </ProFormDependency>

        </ProForm>
      </div>
    </Card>
  </>
}
