"use client"

import {
  ProCard,
  ProDescriptions,
  ProDescriptionsActionType,
  ProForm,
  ProTable
} from "@ant-design/pro-components";
import {Badge, Button, message, Typography} from "antd";
import {useRouter} from "next/navigation";
import {useRef, useState} from "react";
import {getInvoiceInfo} from "@/app/(dashboard)/dashboard/cost/invoice/api";
import Link from "next/link";

export default function Page({params}: { params: { id: number } }){
  const router = useRouter()
  const [invoice,setInvoice] = useState()
  const descriptionColumns = [
    {
      title: "发票类型",
      dataIndex: "invoice_type",
      // @ts-ignore
      valueType: "select",
      valueEnum:{
        1:"个人",
        2:"企业",
      }
    },
    {
      title: "抬头类型",
      dataIndex: "tax_type",
      valueType: "select",
      valueEnum:{
        1:"增值税普通发票",
        2:"增值税专用发票",
      }
    },
    {
      title: "发票抬头",
      dataIndex: "tax_title",
    }
    ,
    {
      title: "税号",
      dataIndex: "tax_number",
    }
    ,
    {
      title: "开户银行",
      dataIndex: "tax_bank"
    }
    ,
    {
      title: "银行卡号",
      dataIndex: "tax_bank_number"
    }
    ,
    {
      title: "注册地址",
      dataIndex: "tax_address"
    }
    ,
    {
      title: "联系电话",
      dataIndex: "tax_phone"
    }
    ,
    {
      title: "发票金额",
      dataIndex: "money",
    }
    ,
    {
      title: "审核状态",
      dataIndex: "audit",
      //@ts-ignore
      valueEnum:{
        1:"审核中",
        2:"审核通过",
        3:"审核拒绝",
      },
      valueType: 'select',
      render:(_,entity)=>{
        return entity.audit == 1 ?  <Badge status="default" text="审核中" /> :  (entity.audit == 2 ? <Badge status="success" text="审核通过" /> :<Badge status="error" text="审核拒绝" />)
      }
    },
    {
      title: "创建时间",
      dataIndex: "created_at", valueType: 'dateTime',
      hidden: true,
    },
  ];
  const actionRef = useRef<ProDescriptionsActionType>();
  const [orders,setOrders] = useState([])

  const [form] = ProForm.useForm()
  return <div>
    <div className={'mb-5'}>
      <ProCard extra={<>
        <Button type={'link'} onClick={() => {
          actionRef.current?.reload()
        }}>刷新</Button>
        <Button type={'link'} onClick={() => {
          router.back()
        }}>返回</Button>
      </>}>
        <Typography.Title level={5}>发票信息</Typography.Title>
        <div>
          <ProDescriptions actionRef={actionRef}
            //@ts-ignore
                           columns={descriptionColumns}
            //@ts-ignore
                           request={async () => {
                             try {
                               const invoice = await getInvoiceInfo(params.id)
                               //@ts-ignore
                               setInvoice(invoice)
                               //@ts-ignore
                               setOrders(invoice?.orders || [])
                               return {
                                 data: invoice,
                                 success: true,
                               }
                             } catch (e) {
                               //@ts-ignore
                               message.error(e.message)
                               router.back()
                             }

                           }}>

          </ProDescriptions>
        </div>
      </ProCard>
    </div>
    <div className={'mb-5'}>
      <ProCard size={'small'}>
        <Typography.Title level={5}>关联订单列表</Typography.Title>
        <ProTable size={'small'} columns={[

          {
            title: "订单标题",
            dataIndex: "title",
            search: false,
          },

          {
            title: "订单编号",
            dataIndex: "trade_no",
          },

          {
            title: "订单金额",
            dataIndex: "price",
            search: false
          },
          {
            title: "创建时间",
            dataIndex: "created_at",
            valueType: 'dateRange',
            hidden: true,
          },
        ]} search={false} toolBarRender={false} pagination={false} dataSource={orders}>
        </ProTable>
      </ProCard>
    </div>
    {invoice?.audit == 2 && <div className={''}>
      <ProCard size={'small'} title={'发票下载'}>
        <div>
          发票下载地址： <Link href={invoice?.url || '#'}>点我下载</Link>
        </div>
      </ProCard>
    </div>}
  </div>

}