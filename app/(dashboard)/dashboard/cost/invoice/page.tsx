"use client"

import {Pro<PERSON>ard, ProTable} from "@ant-design/pro-components";
import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>} from "antd";
import {useEffect, useMemo, useState} from "react";
import RcResizeObserver from 'rc-resize-observer';
import Link from "next/link";
import {ProColumnType} from "@ant-design/pro-table";
import {getCanApplyMoney, getInvoiceList, getTaxInfo} from "@/app/(dashboard)/dashboard/cost/invoice/api";

export default function Page() {
  const [responsive, setResponsive] = useState(false);
  const [tax,setTax] = useState(null)
  const [money,setMoney] = useState(0)


  const handleGetTaxInfo = ()=>{
    getTaxInfo().then((data)=>{
      //@ts-ignore
      setTax(data)
    }).catch(()=>{

    })
  }
  const handleGetCanApplyMoney = ()=>{
    getCanApplyMoney().then((data)=>{
      //@ts-ignore
      setMoney(data)
    }).catch(()=>{

    })
  }

  useEffect(() => {
    handleGetTaxInfo()
    handleGetCanApplyMoney()
  }, []);
  const columns: ProColumnType[] = [
    {
      dataIndex: "created_at",
      title: "申请时间",
      search: false,
    },
    {
      dataIndex: "tax_type",
      title: "发票类型",
      search: false,
      valueEnum:{
        1:"增值税普通发票",
        2:"增值税专用发票"
      }
    },
    {
      dataIndex: "tax_title",
      title: "发票抬头"
    },
    {
      dataIndex: "money",
      title: "发票金额",
      search: false,
    },
    {
      dataIndex: "audit",
      title: "发票状态",
      search: false,
      valueEnum:{
        1:"审核中",
        2:"审核通过",
        3:"审核拒绝",
      },
      render:function (_,entity){
        return entity.audit == 1 ?  <Badge status="default" text="审核中" /> :  (entity.audit == 2 ? <Badge status="success" text="审核通过" /> :<Badge status="error" text="审核拒绝" />)
      }
    },
    {
      valueType: "option",
      title: "操作",
      render: function (_, entity) {
        return <div className={'flex'}>
          <div>
            <Link href={`invoice/${entity.id}`}>详情</Link>
          </div>
        </div>
      }
    }
  ]

  return <div>
    <RcResizeObserver
      key="resize-observer"
      onResize={(offset) => {
        setResponsive(offset.width < 596);
      }}
    >
      <ProCard className={'h-[120px]'} split={responsive ? 'horizontal' : 'vertical'}>
        <ProCard title={'可开发票金额'}>
          <div className={'flex items-center gap-x-5'}>
            <div className={'text-3xl'}>
              ￥ {money?.toFixed(2)}
            </div>
            <div>
             <Link href={'invoice/apply'}> <Button className={''} type={'primary'}>申请发票</Button></Link>
            </div>
          </div>
        </ProCard>
        <ProCard title={'发票信息'} extra={<Link href={'invoice/tax'} key={'xx'}>管理发票信息</Link>}>
          <p>发票抬头： {tax?.tax_title}</p>
          <p>发票类型： {tax?.tax_type == 1?'增值税普通发票':'增值税专用发票'}</p>
        </ProCard>
      </ProCard>
    </RcResizeObserver>
    <Card className={'mt-10'} bordered={false} title={'开票记录'}>
      <ProTable columns={columns} request={async (params)=>{
        try{
          return  await getInvoiceList(params)
        }catch (e) {
          return {
            data:[]
          }
        }
      }}
                scroll={{x:700}}

      ></ProTable>
    </Card>
  </div>
}