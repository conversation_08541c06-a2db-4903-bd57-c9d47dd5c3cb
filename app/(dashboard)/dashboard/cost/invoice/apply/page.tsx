"use client"

import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, message, Modal, Row, Space, Table, Typography} from "antd";
import {ChevronLeft} from "lucide-react";
import {useRouter} from "next/navigation";
import {ProCard, ProTable} from "@ant-design/pro-components";
import React, {useEffect, useMemo, useState} from "react";
import {applyInvoice, getCanApplyOrders, getTaxInfo} from "@/app/(dashboard)/dashboard/cost/invoice/api";
import Link from "next/link";

export default function Page(){
  const router = useRouter()
  const [tax,setTax] = useState()
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectRows,setSelectRows] = useState<Record<string, any>>([])
  const [open,setOpen] = useState(false)
  const [submitLoading,setSubmitLoading] = useState(false)


  const selectOrderMoney = useMemo(() => {
    let money = 0;
    for (let selectRowsKey of selectRows) {
      money+=Number(selectRowsKey?.price||0)
    }
    return money.toFixed(2)
  }, [selectRows]);

  const handleGetTaxInfo = ()=>{
    getTaxInfo().then((data)=>{
      //@ts-ignore
      setTax(data)
    }).then(()=>{

    })
  }

  useEffect(() => {
    handleGetTaxInfo()
  }, []);


  return <div>
    <Card bordered={false} title={<>
      <div className={'flex gap-x-5'}>
        <div className={''}>
          <Typography.Link onClick={()=>{
            router.push("/dashboard/cost/invoice")
          }} className={'flex items-center'}>
            <ChevronLeft size={16} className={'inline-block'} />
            <span>返回</span></Typography.Link>
        </div>
        <div className={'flex'}>
          发票申请
        </div>
      </div>
    </>}>
      <div>
        <ProTable toolBarRender={false} search={false} tableRender={(_,doms)=>{
          return <div>
            <div className={'md:w-[450px] w-max-[450px]'}>
              <ProCard className={'md:w-[450px] w-max-[450px]'} title={'发票信息'}>
                <div className={'flex items-center justify-between'}>
                  <div>
                    <p>发票抬头： {tax?.tax_title}</p>
                  </div>
                  <div className={'md:block hidden'}>
                    <Link href={'tax'} key={'xx'}>管理发票信息</Link>
                  </div>
                </div>
                <p>发票类型： {tax?.tax_type == 1?'增值税普通发票':'增值税专用发票'}</p>
                <p className={'mt-2 md:hidden block'}>
                  <Link href={'tax'} key={'xx'}>管理发票信息</Link>
                </p>
              </ProCard>
            </div>
            <div className={'mt-5'}>
              {doms}
            </div>
            <div className={'mt-5 flex items-center justify-between'}>
              <div>
                待开票金额： <span className={'text-2xl font-bold text-[#ff6a00]'}>{selectOrderMoney}元</span>
              </div>
              <div>
                <Button className={''} type={'primary'} onClick={()=>{
                  if(!tax){
                    message.error("未设置开票信息，请先设置开票信息再申请")
                    return;
                  }
                  if (selectOrderMoney <=0){
                    message.warning("开票金额不能为0")
                    return;
                  }
                  setOpen(true)
                }}>申请开票</Button>
              </div>
            </div>
          </div>
        }} rowKey={'trade_no'} rowSelection={{
          selectedRowKeys,
          onChange:function (selectedRowKeys, selectedRows, info){
            setSelectedRowKeys(selectedRowKeys)
            setSelectRows(selectedRows)
          },
        }} columns={[
          {
            dataIndex:"trade_no",
            title:"订单编号"
          },
          {
            dataIndex:"title",
            title:"订单内容"
          },
          {
            dataIndex:"created_at",
            title:"订单时间"
          },
          {
            dataIndex:"price",
            title:"订单金额"
          }
        ]} pagination={false} request={async ()=>{
          const list = await getCanApplyOrders()
          return {
            data:list
          }
        }} scroll={{
          y:500,
        }} ></ProTable>
      </div>
      <Modal loading={submitLoading} open={open} onCancel={()=>setOpen(false)} title={'确认开票'} onOk={async ()=>{
        try{
          setSubmitLoading(true)
          await applyInvoice({
            "trade_no":selectedRowKeys,
          })
          setSubmitLoading(false)
          message.success("申请成功")
          setOpen(false)
          router.push("/dashboard/cost/invoice")
        }catch (e) {
          message.error(e.message ||"申请失败")
          setSubmitLoading(false)
        }
      }}>
        <div className={'mt-5 flex flex-col gap-5'}>
          <div>
            <p className={''}>
              发票抬头： <span className={'font-bold'}>{tax?.tax_title}</span>
            </p>
            <p>发票类型： {tax?.tax_type == 1 ? '增值税普通发票' : '增值税专用发票'}</p>
          </div>
          <div>
            本次开票金额：<span className={'text-2xl font-bold text-[#ff6a00]'}>{selectOrderMoney}元</span>
          </div>
        </div>
      </Modal>
    </Card>
  </div>
}