"use client"
import {Card, Space, Tag} from "antd";
import {ProTable} from "@ant-design/pro-components";
import {ProColumnType} from "@ant-design/pro-table";
import {getOrderList} from "@/app/(dashboard)/dashboard/cost/order/api";

const orderStatus = {
  "0": <Tag>未支付</Tag>,
  "1": <Tag color={'success'}>已支付</Tag>,
  "2": <Tag color={'red'}>已取消</Tag>,
  "3": <Tag color={'red'}>已退款</Tag>
}
export default function Page() {
  const columns: ProColumnType[] = [
    {
      title: "订单编号",
      dataIndex: "trade_no",
      copyable: true,
    },
    {
      title: "订单标题",
      dataIndex: "title",
      search: false,
    },
    {
      title: "订单类型",
      dataIndex: "order_type",
      valueType: "radioButton",
      valueEnum: {
        api: "购买接口",
        plan: "购买套餐",
        redeem: "礼品兑换"
      }
    },
    {
      title: "订单金额",
      dataIndex: "price",
      search: false
    },
    {
      title: "支付方式",
      dataIndex: "pay_method",
      search: false,
      valueEnum: {
        wechat_scan: "微信支付",
        alipay_scan: "支付宝支付",
        redeem: "礼品兑换"
      },
      valueType: "radio",
    },
    {
      title: "支付状态",
      dataIndex: "order_status",
      render: function (_, entity) {
        return orderStatus[entity.order_status] ?? '';
      },
      valueType: "select",
      valueEnum: {
        "0": "未支付", "1": "已支付", "2": "已取消", "3": "已退款",
      },
      initialValue: "1",
    },
    {
      title: "创建时间/更新时间",
      dataIndex: "created_at",
      search: false,
      render: function (_, entity) {
        return <div className={'flex flex-col p-0 m-0 text-sm'}>
          <p>{entity.created_at}</p>
          <p>{entity.updated_at}</p>
        </div>
      }
    },
    // {
    //   title: "操作",
    //   valueType: 'option',
    //   render: function (text, record) {
    //     return <Space>
    //     </Space>
    //   },
    //   search: false,
    // }
  ]

  return <>
    <Card bordered={false} title={'订单管理'}>
      <ProTable pagination={{
        pageSize: 10,
      }} columns={columns} request={async (params) => {
        try {
          return await getOrderList(params)
        } catch {
          return {}
        }
      }}
                scroll={{x:700}}
      ></ProTable>
    </Card>
  </>
}