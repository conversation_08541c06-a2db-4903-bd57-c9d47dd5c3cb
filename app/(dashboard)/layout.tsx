import React from "react";
import dynamic from "next/dynamic";
import {getCurrentUser} from "@/lib/session";

const ProLayout = dynamic(() => import('@/components/antd/dashboard_layout'), {ssr: false})

export default async function RootLayout({children}: {
  children: React.ReactNode
}) {
  const user = await getCurrentUser();
  return <>
    <div style={{
      height: "100vh",
      overflow: "auto"
    }}>
      <ProLayout user={user}>
        {children}
      </ProLayout>
    </div>
  </>
}