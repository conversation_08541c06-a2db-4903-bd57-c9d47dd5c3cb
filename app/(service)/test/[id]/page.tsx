"use client"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>, <PERSON>
} from "antd";
import { useEffect, useState } from "react";
import { ExportOutlined } from "@ant-design/icons";
import { Content, Footer, Header, Layout } from "@/components/antd/layout";
import { getApiDetail } from "@/app/actions";
import { Api, ApiNode, SiteConfig } from "@/app/types";
import { useRouter } from "next/navigation";
import ApiTestForm from "@/components/api/api-test-form";
import { useSiteConfig } from "@/components/provider/site-provider";
import { getApiLists } from "@/app/(dashboard)/dashboard/data/api/api";
import { FreeApi, ProApi, VipApi } from "@/config/data";

export default function TestPage({ params }: { params: { id: number } }) {

  const siteConfig = useSiteConfig() as SiteConfig

  const router = useRouter()
  const [api, setApi] = useState<Api>()
  const [nodes, setNodes] = useState<ApiNode[]>([])
  const [menuItems, setMenuItems] = useState([])
  const [active, setActive] = useState(0)
  const [pageLoading, setPageLoading] = useState(false)

  const [apiQuota, setApiQuota] = useState({
    show: false,
    can_use_num: 0,
    daily_limit: 0,
  })
  const handleLoadApiQuota = () => {
    getApiLists({ search: params.id }).then((res) => {
      const { data } = res
      if (data.length > 0) {
        const { request, record, api } = data[0]
        if (api?.type == ProApi) {
          setApiQuota({
            show: true,
            can_use_num: record?.can_use_num || 0,
            daily_limit: record?.total_num || 0,
          })
        } else {
          setApiQuota({
            show: true,
            can_use_num: request?.can_use_num || 0,
            daily_limit: request?.daily_limit || 0,
          })
        }

      }
    })
  }

  useEffect(() => {
    if (params.id) {
      setPageLoading(true)
      getApiDetail(params.id).then((res) => {
        //@ts-ignore
        setApi(res)
        //@ts-ignore
        const tempNodes = res?.nodes as ApiNode[]
        const tempMenu = []
        for (let i = 0; i < tempNodes.length; i++) {
          if (i === 0) {
            setActive(tempNodes[i].id)
          }
          tempMenu.push({
            key: tempNodes[i].id,
            label: <span>{tempNodes[i].name}</span>,
          })
        }
        //@ts-ignore
        setMenuItems(tempMenu)
        setNodes(tempNodes)
      }).finally(() => {
        setPageLoading(false)
      })

      handleLoadApiQuota()
    } else {
      router.push("/")
    }

  }, [params.id]);

  const handleMenuClick = async (e: any) => {
    setActive(e.key)
  }

  return <>
    <div>
      <Spin spinning={pageLoading} fullscreen />
      <Layout className='h-screen flex flex-col'>
        <Header style={{
          padding: 0,
          background: '#fff',
          zIndex: 999,
        }}>
          <Affix>
            <div className='flex min-h-[60px] border-b justify-between items-center px-2 bg-white z-50'>
              <div className='flex cursor-pointer' onClick={() => {
                router.push(`/api/${params.id}/api_document`)
              }}>
                <div className='w-[30px] h-[30px] bg-[100%]' style={{
                  backgroundImage: `url(${api?.img})`,
                  backgroundSize: "contain"
                }}>
                </div>
                <div>
                  <p className='text-xl ml-2 font-medium	text-black'>{api?.name}</p>
                </div>
              </div>
              <div>
                <Button type='default' onClick={() => {
                  router.back()
                }} icon={<ExportOutlined />}>退出测试</Button>
              </div>
            </div>
          </Affix>
        </Header>
        <Content className={'mt-[1px] flex-1 h-min-screen'}>
          {apiQuota.show && <div>
            <Alert message={
              <>
                <div className="text-center api-test-alert">
                  {/* 如果当前是免费接口和会员接口则显示当天剩余可请求次数，并且显示升级会员的按，如果是计次接口则显示可请求次数，并且显示继续购买的按钮 */}
                  <div className="text-center api-test-alert">
                    <div className="flex items-center justify-center gap-2">

                      {(api?.type == FreeApi || api?.type == VipApi) && (
                        <>
                          <span>今日剩余可调用次数: <span className="text-red-500 font-bold font-xl">{apiQuota.can_use_num == -1 ? '不限' : apiQuota.can_use_num}</span> 次</span>
                          {apiQuota.can_use_num != -1 && <Button type="link" size="small" onClick={() => router.push('/vip')}>
                            升级套餐获取更多次数
                          </Button>}
                        </>
                      )}
                      {api?.type == ProApi && (
                        <>
                          <span>剩余可调用次数: {apiQuota.can_use_num} 次</span>
                          <Button type="link" size="small" onClick={() => router.push(`/purchase/${params.id}`)}>
                            购买更多次数
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </>
            } type="info" />
          </div>}
          <div className='flex flex-1 h-full lg:flex-row flex-col'>
            <div className='lg:w-[242px] w-full lg:border-r-2 min-h-max'>
              <div className={'hidden md:block'}>
                <Menu items={menuItems} selectedKeys={[active.toString()]} onClick={handleMenuClick}>
                </Menu>
              </div>
              <div className={'block md:hidden border-b'}>
                <Menu mode={'horizontal'} items={menuItems} selectedKeys={[active.toString()]} onClick={handleMenuClick}></Menu>
              </div>
            </div>
            {nodes.map((node) => (
              <div className="flex-1" key={node.id} style={{ display: node.id == active ? 'block' : 'none' }}>
                <ApiTestForm
                  onRequestAfter={handleLoadApiQuota}
                  token={'token'}
                  siteConfig={siteConfig}
                  node={node}
                />
              </div>
            ))}
          </div>
        </Content>

      </Layout>
    </div>

  </>
}