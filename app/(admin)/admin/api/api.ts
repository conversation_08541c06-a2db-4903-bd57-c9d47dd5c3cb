import axiosInstance from "@/lib/axios";

export function getApiList(params: any) {
  return axiosInstance.get("/admin/api/index", {params})
}

export function getApiNodeList(params: any) {
  return axiosInstance.get("/admin/api_node/index", {params})
}

export function getApiPriceList(params: any) {
  return axiosInstance.get("/admin/api_price/index", {params})
}

export function readApi(id: number) {
  return axiosInstance.get(`/admin/api/read/${id}`)
}

export function readApiNode(id: string | number) {
  return axiosInstance.get(`/admin/api_node/read/${id}`)
}

export function readApiPrice(id: number) {
  return axiosInstance.get(`/admin/api_price/read/${id}`)
}

export function updateApi(id: number, values: any) {
  return axiosInstance.put(`/admin/api/update/${id}`, values)
}

export function updateApiPrice(id: number, values: any) {
  return axiosInstance.put(`/admin/api_price/update/${id}`, values)
}

export function updateApiNode(id: string | number, values: any) {
  return axiosInstance.put(`/admin/api_node/update/${id}`, values)
}

export function saveApi(values: any) {
  return axiosInstance.post(`/admin/api/save`, values)
}

export function saveApiPrice(values: any) {
  return axiosInstance.post(`/admin/api_price/save`, values)
}

export function saveApiNode(values: any) {
  return axiosInstance.post(`/admin/api_node/save`, values)
}

export function updateApiStatus(id: number, status: boolean) {
  return axiosInstance.post("/admin/api/status", {
    id: id,
    status: status ? 'Y' : "N"
  })
}


export function updateApiPriceStatus(id: number, status: boolean) {
  return axiosInstance.post("/admin/api_price/status", {
    id: id,
    status: status ? 'Y' : "N"
  })
}

export function updateApiNodeStatus(id: number, status: boolean) {
  return axiosInstance.post("/admin/api_node/status", {
    id: id,
    status: status ? 'Y' : "N"
  })
}

export async function deleteApi(ids: string) {
  return axiosInstance.delete(`/admin/api/delete`, {data: {ids: ids}})
}

export async function deleteApiPrice(ids: string) {
  return axiosInstance.delete(`/admin/api_price/delete`, {data: {ids: ids}})
}

export async function deleteApiNode(ids: string) {
  return axiosInstance.delete(`/admin/api_node/delete`, {data: {ids: ids}})
}



export function getApiPlanLimitList(params: any) {
  return axiosInstance.get("/admin/api_plan_limit/index", {params})
}

export function readApiPlanLimit(id: number) {
  return axiosInstance.get(`/admin/api_plan_limit/read/${id}`)
}
export function saveApiPlanLimit(values: any) {
  return axiosInstance.post(`/admin/api_plan_limit/save`, values)
}
  
export function updateApiPlanLimit(id: number, values: any) {
  return axiosInstance.put(`/admin/api_plan_limit/update/${id}`, values)
}
export function deleteApiPlanLimit(ids: string) {
  return axiosInstance.delete(`/admin/api_plan_limit/delete`, {data: {ids: ids}})
}