"use client"
import {
  ProCard,
  ProForm, ProFormDependency, ProFormDigit, ProFormGroup,
  ProFormRadio,
  ProFormSegmented,
  ProFormText,
  ProFormTextArea,
  StepsForm
} from "@ant-design/pro-components";
import {getDictData} from "@/app/(admin)/admin/dict/api";
import {ParamForm} from "@/components/common/param_form";
import {AliyunForm, BaiduForm, HttpForm, ReflectForm, TencentForm} from "@/components/common/invoke_form";
import {Button, message, Steps} from "antd";
import {useState} from "react";
import {readApiNode, saveApiNode, updateApiNode} from "@/app/(admin)/admin/api/api";
import {useRouter} from "next/navigation";

const steps = [{
  title: "基础信息",
},
  {
    title: "请求参数"
  },
  {
    title: "响应参数",
  },
  {
    title: "连接器配置"
  }]
export default function Page({params}: { params: { api_id: number; id: string | number } }) {

  const router = useRouter()

  const [form] = ProForm.useForm()
  const [current, setCurrent] = useState(0);
  const next = async () => {
    await form.validateFields()
    setCurrent(current + 1);
  };

  const prev = () => {
    setCurrent(current - 1);
  };

  const handleSubmit = async () => {
    await form.validateFields()
    const values = (form.getFieldsValue(true))
    console.log((JSON.stringify(values)))
    if (params.id === 'create') {
      //create
      try {
        await saveApiNode(JSON.parse(JSON.stringify(values)))
        message.success("创建成功")
        router.back()
      } catch (e) {
        // @ts-ignore
        message.error(e.message)
      }
    } else {
      try {
        await updateApiNode(params.id, JSON.parse(JSON.stringify(values)))
        message.success("更新成功")
        router.back()
      } catch (e) {
        // @ts-ignore
        message.error(e.message)
      }
    }
  }

  return <>
    <ProCard title={'接口节点' + (params.id === 'create' ? '创建' : "编辑")}
             extra={<><Button type={'link'} onClick={() => {
               router.back()
             }}>返回</Button></>}>
      <div className={'mx-auto'}>
        <ProForm
          //@ts-ignore
          request={async () => {
            if (params.id !== 'create') {
              return await readApiNode(params.id)
            }
            return {}
          }} submitter={false} form={form}>
          <ProFormText hidden name={'api_id'} initialValue={params.api_id}></ProFormText>
          <Steps onChange={async (curr) => {
            await form.validateFields()
            setCurrent(curr)
          }} className={'mx-auto max-w-[1024px]'} current={current} items={steps}>
          </Steps>
          <div className={'mt-5'}></div>
          <div className={'mx-auto max-w-[760px]'}>
            {current === 0 && <>
              <div className={''}>
                <ProCard className={''} size={'small'}>
                  <ProFormText name={'name'} label={'节点名称'} rules={[{required: true}]}/>
                  <ProFormText name={'request_url'} rules={[{required: true}]} label={'接口访问地址'}/>
                  <ProFormRadio.Group label={'请求方式'} initialValue={'ANY'}
                    //@ts-ignore
                                      request={() => getDictData('request_method')}
                                      name={'request_method'} rules={[{required: true}]}/>
                  <ProFormTextArea label={'节点响应示例'} name={'response'}
                                   rules={[{required: true}]}></ProFormTextArea>
                  <ProFormDigit label={'节点QPS'} name={'qps'} initialValue={0}
                                extra={'节点QPS设置，不设置则为默认套餐的QPS'}></ProFormDigit>
                  <ProFormRadio.Group label={'是否免费节点'} help={'如果API为付费接口时，单独调用该接口不计算次数收费'}
                                      initialValue={'N'}
                                      request={() => getDictData('status')}
                                      name={'is_free'}
                  ></ProFormRadio.Group>
                  <ProFormTextArea label={'计次接口自定义扣费计算'} name={'usage_func'}/>
                  <ProFormRadio.Group label={'节点状态'} initialValue={'Y'}
                    //@ts-ignore
                                      request={() => getDictData('status')}
                                      name={'status'} rules={[{required: true}]}/>
                </ProCard>
              </div>
            </>}
            {current === 1 && <>
              <ParamForm showRules={true} field={'request_params'}/>

            </>}
            {current === 2 && <>
              <ParamForm field={'response_params'}/>

            </>}
            {current === 3 && <>
              <ProFormRadio.Group name={'invoke'} initialValue={'route'}
                //@ts-ignore
                                  request={() => getDictData("api_invoke_type")}
              ></ProFormRadio.Group>
              <ProFormGroup>
                <ProFormRadio.Group label={'数据缓存'} initialValue={'N'} name={['config', 'cache', 'enable_cache']}
                                    valueEnum={{
                                      "Y": '开启',
                                      "N": "禁用"
                                    }}></ProFormRadio.Group>
                <ProFormDependency name={['config']}>
                  {({config}) => {
                    if (config.cache.enable_cache === 'Y') {
                      return <ProFormDigit name={['config', 'cache', 'cache_ttl']} initialValue={0} label={'缓存时间'}
                                           extra={'缓存时间，单位秒'}></ProFormDigit>
                    }
                  }}
                </ProFormDependency>
              </ProFormGroup>

              <ProFormDependency name={['invoke']}>
                {({invoke}) => {
                  if (invoke === 'reflect') {
                    return <ReflectForm/>
                  } else if (invoke === 'http') {
                    return <HttpForm/>
                  } else if (invoke === 'tencent') {
                    return <TencentForm/>
                  } else if (invoke === 'aliyun') {
                    return <AliyunForm/>
                  } else if (invoke === 'baidu') {
                    return <BaiduForm/>
                  }
                  return null
                }}
              </ProFormDependency>
            </>}
            <div className={'mt-5'}>
              {current < steps.length - 1 && (
                <Button type="primary" onClick={() => next()}>
                  下一步
                </Button>
              )}
              {current === steps.length - 1 && (
                <Button type="primary" onClick={handleSubmit}>
                  提交
                </Button>
              )}
              {current > 0 && (
                <Button style={{margin: '0 8px'}} onClick={() => prev()}>
                  上一步
                </Button>
              )}
            </div>
          </div>
        </ProForm>
      </div>
    </ProCard>
  </>
}