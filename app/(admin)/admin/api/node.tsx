"use client"
// @ts-ignore
import {Api, ApiNode} from "@/app/(admin)/admin/api/data";
import {useEffect, useRef, useState} from "react";
import {ActionType, ProTable} from "@ant-design/pro-components";
import axiosInstance from "@/lib/axios";
import {But<PERSON>, Drawer, message, Popconfirm, Space, Switch} from "antd";
import {ProColumnType} from "@ant-design/pro-table";
import {deleteApiNode, getApiNodeList, updateApiNodeStatus} from "@/app/(admin)/admin/api/api";
import {getDictData, getDictList} from "@/app/(admin)/admin/dict/api";
import Link from "next/link";

export default function ApiNode(props: {
  api: Api | null | undefined
  open: boolean
  setOpen: (v: boolean) => void
}) {
  const [current, setCurrent] = useState<ApiNode | null>()
  const actionRef = useRef<ActionType>()
  useEffect(() => {
    console.log(props)
  }, [props]);

  const handleDelete = async (ids: string) => {
    try {
      await deleteApiNode(ids)
      message.success("删除成功")
      actionRef.current?.reload()
    } catch (e) {
      // @ts-ignore
      message.error(e?.message || '删除失败')
    }
  }

  const columns: ProColumnType<ApiNode>[] = [
    {
      title: "ID",
      dataIndex: "id",
    },
    {
      title: "节点名称",
      dataIndex: "name"
    },
    {
      title: "请求地址",
      dataIndex: "request_url",
    },
    {
      title: "请求方法",
      dataIndex: "request_method",
      //@ts-ignore
      request: () => getDictData("request_method")
    },
    {
      title: "接口连接器",
      dataIndex: "invoke",
      //@ts-ignore
      request: () => getDictData("api_invoke_type")
    },
    {
      title: "状态",
      dataIndex: "status",
      valueType: "radio",
      valueEnum: {
        "Y": "启用",
        "N": "禁用",
      },
      render: function (text, record) {
        return <Switch value={record.status === 'Y'} onChange={async (v) => {
          try {
            await updateApiNodeStatus(record.id, v)
            message.success("更新成功")
            actionRef.current?.reload()
          } catch {
            message.error("更新失败")
          }
        }}/>
      }
    },
    {
      title: "创建时间",
      dataIndex: "created_at",
      valueType: 'dateRange',
      hidden: true,
    },
    {
      title: "更新时间",
      dataIndex: "updated_at",
      search: false,
    },
    {
      title: "操作",
      valueType: 'option',
      render: function (text, record) {
        return <Space>
          <Link href={`/admin/api/${props.api?.id}/node/${record.id}`}>
            <Button type={'primary'} size={'small'}>编辑</Button>
          </Link>
          <Popconfirm title={'确认删除吗？'} description={'删除后不可恢复'} onConfirm={async () => {
            await handleDelete([record.id].join(","))
          }}>
            <Button size={'small'} danger>删除</Button>
          </Popconfirm>
        </Space>
      },
      search: false,
    }
  ]

  return <>
    <Drawer destroyOnClose width={1024} title={`${props.api?.name || ''} - 接口节点`} open={props.open}
            onClose={() => props.setOpen(false)}>
      <ProTable<ApiNode> params={{
        orderBy: "id",
        orderType: "desc",
        api_id: props.api?.id || 0,
      }} actionRef={actionRef} rowKey={'id'} columns={columns} scroll={{x: 750}}
                         request={async (params) => {
                           console.log(params)
                           return await getApiNodeList(params)
                         }}
                         toolBarRender={() => [
                           <Link key={'add'} href={`/admin/api/${props.api?.id}/node/create`}>
                             <Button type={'primary'} size={'small'} key={'add'} onClick={() => {
                             }}>添加节点</Button>
                           </Link>
                         ]}
      >
      </ProTable>
    </Drawer>
  </>
}