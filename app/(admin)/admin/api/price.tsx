"use client"

import {<PERSON><PERSON>, <PERSON>er, InputN<PERSON><PERSON>, message, Popconfirm, Space, Switch} from "antd";
import {useEffect, useRef, useState} from "react";
import {
  ActionType,
  BetaSchemaForm,
  ModalForm,
  ProFormColumnsType,
  ProTable
} from "@ant-design/pro-components";
import {ProColumnType} from "@ant-design/pro-table";
// @ts-ignore
import {Api, ApiPrice} from "@/app/(admin)/admin/api/data";
// @ts-ignore
import {deleteApiPrice, getApiPriceList, readApiPrice, saveApiPrice, updateApiPrice} from "@/app/(admin)/admin/api/api";

export default function ApiPrice(props: {
  current: Api | null | undefined
  open: boolean,
  setOpen: (v: boolean) => void
}) {
  const [current, setCurrent] = useState<ApiPrice | null>()
  const actionRef = useRef<ActionType>()

  const [open, setOpen] = useState(false)

  const handleDelete = async (ids: string) => {
    try {
      await deleteApiPrice(ids)
      message.success("删除成功")
      actionRef.current?.reload()
    } catch (e) {
      // @ts-ignore
      message.error(e?.message || '删除失败')
    }
  }

  const handleCloseOpen = () => {
    setOpen(false)
    setCurrent(null)
  }

  const columns: ProColumnType<ApiPrice>[] = [
    {
      title: "编号",
      dataIndex: "id"
    },
    {
      title: "次数",
      dataIndex: "num"
    },
    {
      title: "价格",
      dataIndex: "price"
    },
    {
      title: "是否体验次数",
      dataIndex: "is_trail",
      valueType: "switch",
      render: (dom, record) => {
        return <>{record.is_trail ? '是' : '否'}</>
      }
    },
    {
      title: "有效天数",
      dataIndex: "daily",
      search: false,
    },
    {
      title: "状态",
      dataIndex: "status",
      valueType: "radio",
      valueEnum: {
        "Y": "启用",
        "N": "禁用",
      },
      render: function (text, record) {
        return <Switch value={record.status === 'Y'} onChange={async (v) => {
          try {
            await updateApiPrice(record.id, v)
            message.success("更新成功")
            actionRef.current?.reload()
          } catch {
            message.error("更新失败")
          }
        }}/>
      }
    },
    {
      title: "更新时间",
      dataIndex: "updated_at",
      search: false,
    },
    {
      title: "操作",
      valueType: 'option',
      render: function (text, record) {
        return <Space>
          <Button type={'primary'} size={'small'} onClick={() => {
            setCurrent(record)
            setTimeout(() => {
              setOpen(true)
            }, 100)
          }}>编辑</Button>
          <Popconfirm title={'确认删除吗？'} description={'删除后不可恢复'} onConfirm={async () => {
            await handleDelete([record.id].join(","))
          }}>
            <Button size={'small'} danger>删除</Button>
          </Popconfirm>
        </Space>
      },
      search: false,
    }
  ]

  const formColumns: ProFormColumnsType<ApiPrice>[] = [
    {
      title: "次数",
      dataIndex: "num"
    },
    {
      title: "价格",
      dataIndex: "price"
    },
    {
      title: "是否体验次数",
      dataIndex: "is_trail",
      valueType: "switch",
    },
    {
      title: "有效天数",
      dataIndex: "daily",
      valueType: "digit",
    },
    {
      title: "状态",
      dataIndex: "status",
      valueType: "radio",
      valueEnum: {
        "Y": "启用",
        "N": "禁用",
      }
    },
  ]

  useEffect(() => {
    if (!open) {
      setCurrent(null)
    }
  }, [open])
  useEffect(() => {
    return () => {
      setCurrent(null)
    }
  }, []);

  return <Drawer title={`${props.current?.name}`} width={1024} onClose={() => props.setOpen(false)} open={props.open}
                 destroyOnClose>
    <ProTable<ApiPrice> search={false} params={{
      api_id: props.current?.id,
      orderBy: "id",
      orderType: "asc"
    }} actionRef={actionRef} rowKey={'id'} columns={columns} scroll={{x: 750}}
                        request={async (params) => {
                          return await getApiPriceList(params)
                        }}
                        toolBarRender={() => [
                          <Button type={'primary'} size={'small'} key={'add'} onClick={() => {
                            setOpen(true)
                          }}>添加定价</Button>
                        ]}
    >
    </ProTable>
    <ModalForm title={'接口定价'} open={open} onOpenChange={setOpen} request={async () => {
      if (current) {
        return await readApiPrice(current.id)
      }
      return {}
    }} onFinish={async (values) => {
      values = {unit_price: 0, ...values}
      if (current) {
        try {
          await updateApiPrice(current.id, values)
          message.success("更新成功")
          actionRef.current?.reload()
          handleCloseOpen()
        } catch (e) {
          //@ts-ignore
          message.error(e.message ?? '更新失败')
        }

      } else {
        try {
          await saveApiPrice({...values, api_id: props.current?.id})
          message.success("创建成功")
          actionRef.current?.reload()
          handleCloseOpen()
        } catch (e) {
          //@ts-ignore
          message.error(e.message ?? '创建失败')
        }
      }
    }} modalProps={{
      destroyOnClose: true
    }}>
      <BetaSchemaForm<ApiPrice>
        title={'接口定价'} columns={formColumns} layoutType={'Embed'}
        //@ts-ignore
      ></BetaSchemaForm>
    </ModalForm>
  </Drawer>
}