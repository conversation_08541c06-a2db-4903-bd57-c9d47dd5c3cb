"use client"

import { <PERSON><PERSON>, <PERSON><PERSON>, InputN<PERSON><PERSON>, message, Popconfirm, Space, Switch } from "antd";
import { useEffect, useRef, useState } from "react";
import {
    ActionType,
    BetaSchemaForm,
    ModalForm,
    ProFormColumnsType,
    ProTable
} from "@ant-design/pro-components";
import { ProColumnType } from "@ant-design/pro-table";
// @ts-ignore
import { Api, ApiPlanLimit } from "@/app/(admin)/admin/api/data";
import { deleteApiPlanLimit, getApiPlanLimitList, readApiPlanLimit, saveApiPlanLimit, updateApiPlanLimit } from "./api";
import { getPlanList } from "../plan/api";
// @ts-ignore

export default function PlanLimit(props: {
    current: Api | null | undefined
    open: boolean,
    setOpen: (v: boolean) => void
}) {
    const [current, setCurrent] = useState<ApiPlanLimit | null>()
    const actionRef = useRef<ActionType>()

    const [open, setOpen] = useState(false)

    const handleDelete = async (ids: string) => {
        try {
            await deleteApiPlanLimit(ids)
            message.success("删除成功")
            actionRef.current?.reload()
        } catch (e) {
            // @ts-ignore
            message.error(e?.message || '删除失败')
        }
    }

    const handleCloseOpen = () => {
        setOpen(false)
        setCurrent(null)
    }

    const columns: ProColumnType<ApiPlanLimit>[] = [
        {
            title: "编号",
            dataIndex: "id"
        },
        {
            title: "套餐",
            dataIndex: "plan_id",
            valueType: "select",
            request: async () => await getPlans()
        },
        {
            title: "请求次数",
            dataIndex: "daily_limit"
        },
        {
            title: "QPS",
            dataIndex: "request_qps",
            search: false,
        },
        {
            title: "更新时间",
            dataIndex: "updated_at",
            search: false,
        },
        {
            title: "操作",
            valueType: 'option',
            render: function (text, record) {
                return <Space>
                    <Button type={'primary'} size={'small'} onClick={() => {
                        setCurrent(record)
                        setTimeout(() => {
                            setOpen(true)
                        }, 100)
                    }}>编辑</Button>
                    <Popconfirm title={'确认删除吗？'} description={'删除后不可恢复'} onConfirm={async () => {
                        await handleDelete([record.id].join(","))
                    }}>
                        <Button size={'small'} danger>删除</Button>
                    </Popconfirm>
                </Space>
            },
            search: false,
        }
    ]

    const formColumns: ProFormColumnsType<ApiPlanLimit>[] = [
        {
            title: "套餐",
            dataIndex: "plan_id",
            valueType: "select",
            request: async () => await getPlans()
        },
        {
            title: "请求次数",
            dataIndex: "daily_limit",
            valueType: "digit",
            fieldProps: {
                min: -1
            }
        },
        {
            title: "QPS",
            dataIndex: "request_qps",
            valueType: "digit",
            fieldProps: {
                min: -1
            }
        }
    ]

    const getPlans = async () => {
        try {
            const response = await getPlanList({});
            // @ts-ignore
            return response.data.map((item: any) => ({
                label: item.name,
                value: item.id
            }));
        } catch (e) {
            message.error('获取套餐列表失败');
            return [];
        }
    }

    useEffect(() => {
        if (!open) {
            setCurrent(null)
        }
    }, [open])
    useEffect(() => {
        return () => {
            setCurrent(null)
        }
    }, []);

    return <Drawer title={'套餐单独限制'} width={1024} onClose={() => props.setOpen(false)} open={props.open}
        destroyOnClose>
        <ProTable<ApiPlanLimit> search={false} params={{
            api_id: props.current?.id,
            orderBy: "id",
            orderType: "asc"
        }} actionRef={actionRef} rowKey={'id'} columns={columns} scroll={{ x: 750 }}
            request={async (params) => {
                return await getApiPlanLimitList(params)
            }}
            toolBarRender={() => [
                <Button type={'primary'} size={'small'} key={'add'} onClick={() => {
                    setOpen(true)
                }}>添加限制</Button>
            ]}
        >
        </ProTable>
        <ModalForm title={'套餐单独限制'} open={open} onOpenChange={setOpen} request={async () => {
            if (current) {
                return await readApiPlanLimit(current.id)
            }
            return {}
        }} onFinish={async (values) => {
            values = { unit_price: 0, ...values }
            if (current) {
                try {
                    await updateApiPlanLimit(current.id, values)
                    message.success("更新成功")
                    actionRef.current?.reload()
                    handleCloseOpen()
                } catch (e) {
                    //@ts-ignore
                    message.error(e.message ?? '更新失败')
                }

            } else {
                try {
                    await saveApiPlanLimit({ ...values, api_id: props.current?.id })
                    message.success("创建成功")
                    actionRef.current?.reload()
                    handleCloseOpen()
                } catch (e) {
                    //@ts-ignore
                    message.error(e.message ?? '创建失败')
                }
            }
        }} modalProps={{
            destroyOnClose: true
        }}>
            <BetaSchemaForm<ApiPlanLimit>
                title={'套餐单独限制'} columns={formColumns} layoutType={'Embed'}
            //@ts-ignore
            ></BetaSchemaForm>
        </ModalForm>
    </Drawer>
}