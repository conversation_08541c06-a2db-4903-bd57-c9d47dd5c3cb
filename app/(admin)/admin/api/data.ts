import { ApiCategory } from "@/app/(admin)/admin/api_category/data";
import { STATUS } from "@/config/data";

export interface Api {
  id: number;
  name: string
  category_id: number;
  category?: ApiCategory;
  img?: string;
  type: number;
  description?: string;
  introduce?: string;
  status: STATUS;
  sort: number;
  click: number;
  tags: string[]
  nodes: ApiNode[];
  prices: ApiPrice[];
}

export interface ApiNode {
  id: number;
  api_id: number;
  name: string;
  request_url: string;
  request_method: string;
  invoke: string;
  config: any;
  is_free: boolean;
  response: string;
  status: STATUS
}

export interface ApiPrice {
  id: number;
  api_id: number;
  price: number;
  num: number;
  is_trail: boolean;
  status: STATUS
}

export interface ApiPlanLimit {
  id: number;
  api_id: number;
  plan_id: number;
  daily_limit: number;
  request_qps: number;
  created_at: string;
  updated_at: string;
}
