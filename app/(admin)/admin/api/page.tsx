"use client"

import { <PERSON><PERSON><PERSON><PERSON> } from "@ant-design/pro-layout";
import {
  ActionType,
  ModalForm, ProFormDigit,
  ProFormItem, ProFormRadio,
  ProFormSelect, ProFormText, ProFormTextArea,
  ProTable
} from "@ant-design/pro-components";
import { ProColumnType } from "@ant-design/pro-table";
import { Badge, Button, Dropdown, message, Popconfirm, Space, Switch } from "antd";
import { useEffect, useRef, useState } from "react";
import { Api } from "@/app/(admin)/admin/api/data";
import { getApiCategorySelect } from "@/app/(admin)/admin/api_category/api";
import { getDictData } from "@/app/(admin)/admin/dict/api";
import { ProFormUploadButton } from "@ant-design/pro-form";
import { deleteApi, getApiList, readApi, saveApi, updateApi, updateApiStatus } from "@/app/(admin)/admin/api/api";
import dynamic from "next/dynamic";
import ApiNode from "@/app/(admin)/admin/api/node";
import ApiPrice from "@/app/(admin)/admin/api/price";
import Cookies from "js-cookie";
import { AccessTokenKey } from "@/config/data";
import { env } from "@/env";
import PlanLimit from "./plan_limit";


const Wangeditor = dynamic(() => import('@/components/common/wangeditor'), { ssr: false })
export default function Page() {
  const [current, setCurrent] = useState<Api | null>()
  const actionRef = useRef<ActionType>()

  const [open, setOpen] = useState(false)

  const handleDelete = async (ids: string) => {
    try {
      await deleteApi(ids)
      message.success("删除成功")
      actionRef.current?.reload()
    } catch (e) {
      // @ts-ignore
      message.error(e?.message || '删除失败')
    }
  }

  const handleCloseOpen = () => {
    setOpen(false)
    setCurrent(null)
  }

  const [apiNodeOpen, setApiNodeOpen] = useState(false)
  const [apiPriceOpen, setApiPriceOpen] = useState(false)
  const [planLimitOpen, setPlanLimitOpen] = useState(false)


  const columns: ProColumnType<Api>[] = [
    {
      title: "ID",
      dataIndex: "id",
    },
    {
      title: "接口分类",
      dataIndex: "category_id",
      valueType: "select",
      // @ts-ignore
      request: getApiCategorySelect
    },
    {
      title: "接口名称",
      dataIndex: "name",
    },

    {
      title: "接口封面",
      dataIndex: "img",
      search: false,
      valueType: "image",
    },
    {
      title: "接口类型",
      dataIndex: "type",
      valueType: "select",
      // @ts-ignore
      request: () => getDictData('api_type')
    },
    {
      title: "接口节点",
      dataIndex: "nodes",
      render: function (text, record) {
        return <>
          <Badge count={record.nodes.length ?? 0}>
            <Button size={'small'} type={'primary'} onClick={() => {
              setCurrent(record)
              setTimeout(() => setApiNodeOpen(true), 100)
            }}>接口节点</Button>
          </Badge>
        </>
      }
    },
    {
      title: "接口定价",
      dataIndex: "prices",
      render: function (text, record) {
        return <>
          <Badge count={record.prices.length ?? 0}>
            <Button size={'small'} type={'primary'} onClick={() => {
              setCurrent(record)
              setTimeout(() => setApiPriceOpen(true), 100)
            }}>接口定价</Button>
          </Badge>
        </>
      }
    },
    {
      title: "状态",
      dataIndex: "status",
      valueType: "radio",
      valueEnum: {
        "Y": "启用",
        "N": "禁用",
      },
      render: function (text, record) {
        return <Switch value={record.status === 'Y'} onChange={async (v) => {
          try {
            await updateApiStatus(record.id, v)
            message.success("更新成功")
            actionRef.current?.reload()
          } catch {
            message.error("更新失败")
          }
        }} />
      }
    },
    {
      title: "创建时间",
      dataIndex: "created_at",
      valueType: 'dateRange',
      hidden: true,
    },
    {
      title: "更新时间",
      dataIndex: "updated_at",
      search: false,
    },
    {
      title: "操作",
      valueType: 'option',
      render: function (text, record) {
        return <Space>
          <Button type={'primary'} size={'small'} onClick={() => {
            setCurrent(record)
            setTimeout(() => {
              setOpen(true)
            }, 100)
          }}>编辑</Button>
          <Popconfirm title={'确认删除吗？'} description={'删除后不可恢复'} onConfirm={async () => {
            await handleDelete([record.id].join(","))
          }}>
            <Button size={'small'} danger>删除</Button>
          </Popconfirm>
          <Dropdown menu={{
            items: [
              {
                label: "套餐限制",
                key: "api_limit",
              }
            ],
            onClick: (e) => {
              if(e.key === 'api_limit'){
                setCurrent(record)
                setTimeout(() => {
                  setPlanLimitOpen(true)
                }, 100)
              }
            }
          }}>
            <Button size={'small'}>更多</Button>
          </Dropdown>
        </Space>
      },
      search: false,
    }
  ]


  useEffect(() => {
    if (!open) {
      setCurrent(null)
    }
  }, [open])


  return <>
    <PageContainer>
      <ProTable<Api> params={{
        orderBy: "id",
        orderType: "desc"
      }} actionRef={actionRef} rowKey={'id'} columns={columns} scroll={{ x: 750 }}
        request={async (params) => {
          return await getApiList(params)
        }}
        toolBarRender={() => [
          <Button type={'primary'} size={'small'} key={'add'} onClick={() => {
            setOpen(true)
          }}>添加接口</Button>
        ]}
      >
      </ProTable>
      <ModalForm title={'接口分类'} open={open} onOpenChange={setOpen} request={async () => {
        if (current) {
          const data = await readApi(current.id)
          // @ts-ignore
          if (data.img) {
            //@ts-ignore
            data.img = [{ url: data.img, status: "done", id: -1 }]
          }
          return data
        }
        return {}
      }} onFinish={async (values) => {
        if (current) {
          try {
            await updateApi(current.id, values)
            message.success("更新成功")
            actionRef.current?.reload()
            handleCloseOpen()
          } catch (e) {
            //@ts-ignore
            message.error(e.message ?? '更新失败')
          }

        } else {
          try {
            await saveApi(values)
            message.success("创建成功")
            actionRef.current?.reload()
            handleCloseOpen()
          } catch (e) {
            //@ts-ignore
            message.error(e.message ?? '创建失败')
          }
        }
      }} modalProps={{
        destroyOnClose: true
      }}>
        <ProFormSelect label={'接口分类'} name={'category_id'}
          //@ts-ignore
          request={getApiCategorySelect}
          rules={[{ required: true, message: "此项必填！" }]} />
        <ProFormText label={'接口名称'} name={'name'} rules={[{ required: true, message: "此项必填！" }]} />
        <ProFormSelect extra={'接口类型为不同的计费模式'} label={'接口类型'} name={'type'}
          //@ts-ignore
          request={() => getDictData('api_type')}
          rules={[{ required: true, message: "此项必填！" }]} />
        <ProFormUploadButton fieldProps={{
          multiple: false,
          headers: {
            "Authorization": "Bearer " + Cookies.get(AccessTokenKey)
          },
          onChange: (info) => {
            if (info.file.status === "done") {
              if (info.file.response.code != 200) {
                //@ts-ignore
                message.error(info.file.response.message)

              }
            }
          }
        }} transform={(value) => {
          console.log(value)
          if (value.length == 0) {
            return null;
          }
          const img = value[0]
          if (img.url) {
            return img.url
          } else if (img.response) {
            return img.response.data.url
          }
          return null
        }} max={1} listType={'picture-card'} label={'接口封面'} name={'img'}
          action={env.NEXT_PUBLIC_API_URL + '/admin/common/upload'} />

        <ProFormTextArea name={'description'} label={'接口描述'} />
        <ProFormItem name={'introduce'} label={'接口介绍'}>
          <Wangeditor />
        </ProFormItem>
        <ProFormDigit name={"click"} label={'点击次数'} />
        <ProFormDigit name={"sort"} label={'排序'} />
        <ProFormSelect mode={'tags'} name={'tags'} label={'标签'} />
        <ProFormRadio.Group
          //@ts-ignore
          request={() => getDictData("status")}
          name={'status'} label={'接口状态'} />

      </ModalForm>

      <ApiNode api={current} open={apiNodeOpen} setOpen={(v) => {
        if (!v) {
          setApiNodeOpen(false)
          setCurrent(null)
        }
      }} />
      <ApiPrice current={current} open={apiPriceOpen} setOpen={(v) => {
        if (!v) {
          setApiPriceOpen(false)
          setCurrent(null)
        }
      }} />

      <PlanLimit current={current} open={planLimitOpen} setOpen={(v)=>{
        if(!v){
          setPlanLimitOpen(false)
          setCurrent(null)
        }
      }}></PlanLimit>
    </PageContainer>
  </>
}