import axiosInstance from "@/lib/axios";

export async function getSettingConfigGroupList() {
  return axiosInstance.get("/admin/setting_config_group/index", {
    params: {pageSize: 1000,}
  })
}

export async function createSettingConfigGroup(values: any) {
  return axiosInstance.post("/admin/setting_config_group/save", values)
}

export async function createSettingConfig(values: any) {
  return axiosInstance.post("/admin/setting_config/save", values)
}

export async function getSettingConfigList(group_code: string) {
  return axiosInstance.get("/admin/setting_config/index", {
    params: {pageSize: 1000, group_code: group_code, orderBy: "sort", orderType: "desc"}
  })
}

export async function updateSettingConfig(values: any) {
  return axiosInstance.post("/admin/setting_config/update", values)
}

export async function updateSettingConfigSort(group_code: string, values: any) {
  return axiosInstance.post(`/admin/setting_config/updateSortByKey`, {group_code:group_code,...values})
}


export async function updateSettingConfigByKeys(group_code: string, values: any) {
  return axiosInstance.post(`/admin/setting_config/updateByKeys/${group_code}`, values)
}