"use client"

import {BetaSchemaForm, ProForm} from "@ant-design/pro-components";
import {ProFormColumnsType} from "@ant-design/pro-form/es/components/SchemaForm/typing";
import {updateSettingConfigByKeys} from "@/app/(admin)/admin/setting/api";
import {message} from "antd";

interface SettingFormProps {
  forms: ProFormColumnsType[],
  group_code: string
}

export function SettingForm(props: SettingFormProps) {
  return <>
    <ProForm onFinish={async (values) => {
      try {
        await updateSettingConfigByKeys(props.group_code, values)
        message.success("更新成功")
      } catch {
        message.error("更新失败")
      }
    }}>
      <BetaSchemaForm columns={props.forms} layoutType={'Embed'}/>
    </ProForm>
  </>
}