"use client"

import {ProCard, ProForm, ProFormGroup, ProFormSelect, ProFormText, ProFormTextArea} from "@ant-design/pro-components";
import {PageContainer} from "@ant-design/pro-layout";
import {message, Typography} from "antd";
import {ProFormSwitch} from "@ant-design/pro-form";
import {useEffect} from "react";
import {getSettingConfigList, updateSettingConfigByKeys} from "@/app/(admin)/admin/setting/api";

export default function Page() {
  const [form] = ProForm.useForm()
  useEffect(() => {

    getSettingConfigList("sms").then(({data}) => {
      const temp: Record<any, any> = {}
      for (const datum of data) {
        temp[datum.key] = datum.value
      }
      console.log(temp)
      form.setFieldsValue(temp)
    })
  }, [])


  return <div>
    <PageContainer title={'短信配置'}>
      <ProForm form={form} onFinish={async (values) => {
        try {
          await updateSettingConfigByKeys("sms", values)
          message.success("更新成功")
        } catch {
          message.error("更新失败")
        }
      }}>
        <div className={'mb-5'}>
          <ProCard title={'基础设置'}>
            <ProFormSelect name={'sms_channel'} width={'md'} label={'短信通道'} valueEnum={{
              aliyun: "阿里云",
            }}></ProFormSelect>
          </ProCard>
        </div>
        <div className={'mb-5'}>
          <ProCard title={'阿里云配置'}>
            <ProFormText.Password width={'md'} label={'AccessKeyId'}
                                  name={'aliyun_sms_access_key_id'}></ProFormText.Password>
            <ProFormText.Password width={'md'} label={'AccessKeySecret'}
                                  name={'aliyun_sms_access_key_secret'}></ProFormText.Password>
            <ProFormText label={'短信签名'} width={'md'} name={'aliyun_sms_sign_name'}></ProFormText>
          </ProCard>
        </div>
        <div className={'mb-5'}>
          <ProCard title={'短信模板'}>
            <ProFormText label={'短信验证码通知模板'} width={'md'} name={'sms_verify_code_template'}></ProFormText>
            <ProFormText label={'开通会员短信通知模板'} width={'md'} name={'sms_open_vip_template'}></ProFormText>
            <ProFormText label={'会员到期通知短信模板'} width={'md'} name={'sms_membership_expiration_template'}></ProFormText>
            <ProFormText label={'接口次数余额不足短信模板'} width={'md'} name={'sms_api_usage_alert_template'}></ProFormText>
          </ProCard>

        </div>
        <div className={'mb-5'}>
          <ProCard title={'验证码设置'}>
            <ProFormText label={'验证码长度'} width={'md'} name={'sms_code_length'}></ProFormText>
            <ProFormText label={'验证码有效期'} width={'md'} name={'sms_code_valid_time'}
                         addonAfter={'分钟'}></ProFormText>
          </ProCard>
        </div>
        <div className={'mb-5'}>
          <ProCard title={'限制设置'}>
            <ProFormSwitch label={'限制是否开启'} name={'sms_limit_enable'} rules={[{required: true,}]}></ProFormSwitch>
            <div className={'flex flex-col'}>
              <Typography.Title level={5}>验证码限制：</Typography.Title>
              <div>
                                  <span>
                  <ProFormGroup labelLayout={'inline'} size={'small'} spaceProps={{
                    size: "small"
                  }}>
                    同一手机号在 <ProFormText name={'sms_limit_hour'} width={'xs'}/>  小时内，获取验证码最多 <ProFormText
                    name={'sms_limit_hour_count'} width={'xs'}/>次
                  </ProFormGroup>
                </span>
              </div>
              <div className={'items-center'}>
                <ProFormGroup labelLayout={'inline'} size={'small'} spaceProps={{
                  size: "small"
                }}>
                  同一手机号在 <ProFormText name={'sms_limit_minute'} width={'xs'}/> 分钟内，获取验证码最多 <ProFormText
                  name={'sms_limit_minute_count'} width={'xs'}/>次
                </ProFormGroup>
              </div>
            </div>

            <div className={'flex flex-col'}>
              <Typography.Title level={5}>单IP限制：</Typography.Title>
              <div>
                <span>
                  <ProFormGroup labelLayout={'inline'} size={'small'} spaceProps={{
                    size: "small"
                  }}>
                    同一IP在 <ProFormText name={'sms_limit_hour_ip'} width={'xs'}/>  小时内，获取验证码最多 <ProFormText
                    name={'sms_limit_hour_count_ip'} width={'xs'}/>次
                  </ProFormGroup>
                </span>
              </div>
              <div className={'items-center'}>
                <ProFormGroup labelLayout={'inline'} size={'small'} spaceProps={{
                  size: "small"
                }}>
                  同一IP在 <ProFormText width={'xs'} name={'sms_limit_minute_ip'}/> 分钟内，获取验证码最多 <ProFormText
                  name={'sms_limit_minute_count_ip'} width={'xs'}/>次
                </ProFormGroup>
              </div>
            </div>
            <div>
              <ProFormTextArea extra={'一行一个'} width={'md'} label={'手机黑名单设置'}
                               name={'sms_black_phone'}></ProFormTextArea>
              <ProFormTextArea extra={'一行一个'} width={'md'} label={'IP黑名单设置'}
                               name={'sms_black_phone_ip'}></ProFormTextArea>
            </div>

          </ProCard>
        </div>
      </ProForm>
    </PageContainer>
  </div>
}