"use client"
import {
  BetaSchemaForm,
  ModalForm,
  ProCard,
  ProForm, ProFormDependency,
  ProFormDigit, ProFormInstance,
  ProFormSelect,
  ProFormText, ProFormTextArea, ProTable
} from "@ant-design/pro-components";
import {Button, message, Modal, Space, Tabs, TabsProps, Tooltip} from "antd";
import {DeleteOutlined, MenuUnfoldOutlined, PlusOutlined, ReloadOutlined, SettingOutlined} from "@ant-design/icons";
import IconButton from "@/components/common/icon-button";
import {useEffect, useRef, useState} from "react";
import {
  createSettingConfig,
  createSettingConfigGroup,
  getSettingConfigGroupList, getSettingConfigList
} from "@/app/(admin)/admin/setting/api";
import {SettingForm} from "@/app/(admin)/admin/setting/setting_form";
import {SettingConfig, SettingConfigGroup} from "@/app/(admin)/admin/setting/data";
import {ProFormColumnsType} from "@ant-design/pro-form/es/components/SchemaForm/typing";
import SettingConfigModal from "./setting_config";
import {getDictData} from "@/app/(admin)/admin/dict/api";

export default function Page() {
  const [loading, setLoading] = useState(false)
  const [groupFormModal, setGroupFormModal] = useState(false)
  const [groups, setGroups] = useState<SettingConfigGroup[]>([])
  const [groupSelect, setGroupSelect] = useState({})
  const [items, setItems] = useState([])
  const settingFormAction = useRef<ProFormInstance>()
  const [activeKey, setActiveKey] = useState("system")

  const loadGroups = async () => {
    setLoading(true)
    const res = await getSettingConfigGroupList()
    const data = res.data as SettingConfigGroup[]
    setGroups(data)
    const tempItems = []
    const tempSelect = {}
    for (const datum of data) {
      if (datum.code === 'sms') {
        continue;
      }
      //获取配置组的值
      const res = await getSettingConfigList(datum.code)
      const configList = res.data as SettingConfig[]
      //封装 forms
      const forms: ProFormColumnsType[] = [];
      for (const config of configList) {
        const form: ProFormColumnsType = {
          title: config.name,
          dataIndex: config.key,
          initialValue: config.value,
          //@ts-ignore
          valueType: config.input_type,
          tooltip: config.key,
          formItemProps: {
            extra: config.remark
          },

        }
        if (config.config_select_data) {
          try {
            form.valueEnum = JSON.parse(config.config_select_data)
          } catch {

          }
        } else {
          if (config.dict_code) {
            //@ts-ignore
            form.request = async () => getDictData(config.dict_code)
          }
        }
        forms.push(form)
      }

      tempItems.push({
        label: datum.name,
        key: datum.code,
        children: <SettingForm group_code={datum.code} forms={forms}></SettingForm>
      })
      //@ts-ignore
      tempSelect[datum.code] = datum.name
    }
    //@ts-ignore
    setItems(tempItems)
    //@ts-ignore
    setGroupSelect(tempSelect)
    setLoading(false)
  }

  const handleDeleteGroup = async () => {
    if (['system', 'user', 'upload', "sms"].includes(activeKey)) {
      message.info("该配置为系统核心配置，不能删除")
      return;
    }
    Modal.confirm({
      title: "确认删除吗？",
      content: "删除后不可找回",
      type: "warning",
      onOk: function () {
        message.success("删除成功")
      }
    })
  }
  const [open, setOpen] = useState(false)

  useEffect(() => {
    void loadGroups()
  }, []);


  return <>
    <div className={'grid grid-cols-1 md:grid-cols-2 gap-5'}>
      <ProCard loading={loading}>
        <Tabs activeKey={activeKey} onChange={(v) => {
          setActiveKey(v)
          settingFormAction.current?.setFieldValue("group_code", v)
        }} items={items} tabBarExtraContent={{
          right: <div>
            <div>
              <Space>
                <Tooltip title={'添加新配置组'}><IconButton size={'small'} onClick={() => setGroupFormModal(true)}
                                                            type={'primary'}
                                                            icon={<PlusOutlined/>}></IconButton></Tooltip>
                <Tooltip title={'删除当前组配置'}>
                  <IconButton size={'small'} onClick={handleDeleteGroup} type={'primary'}
                              icon={<DeleteOutlined/>}></IconButton>
                </Tooltip>
                <Tooltip title={'管理该组配置'}>
                  <IconButton size={'small'} onClick={() => setOpen(true)} type={'primary'}
                              icon={<SettingOutlined/>}></IconButton>
                </Tooltip>
                <Tooltip title={'刷新'}>
                  <IconButton size={'small'} onClick={() => loadGroups()} type={'primary'}
                              icon={<ReloadOutlined/>}></IconButton>
                </Tooltip>
              </Space>
            </div>
          </div>
        }}>
        </Tabs>
      </ProCard>
      <ProCard>
        <ProForm formRef={settingFormAction} size={'middle'} onFinish={async (values) => {
          try {
            await createSettingConfig(values)
            message.success("添加成功")
            settingFormAction.current?.resetFields()
            await loadGroups()
          } catch (e) {
            //@ts-ignore
            message.error(e.message)
          }
        }}>
          <ProFormSelect label={'配置组'} name={'group_code'} valueEnum={groupSelect} rules={[{required: true}]}/>
          <ProFormText label={'配置名称'} name={'name'} rules={[{required: true}]}/>
          <ProFormText label={'配置标识'} name={'key'} rules={[{required: true}]}/>
          <ProFormText label={'配置值'} name={'value'} rules={[{required: false}]}/>
          <ProFormDigit label={'排序'} name={'sort'} initialValue={0} rules={[{required: false}]}/>
          <ProFormSelect label={'输入组件'} initialValue={'text'} name={'input_type'} valueEnum={{
            text: "文本框",
            textarea: "文本域",
            password: "密码框",
            date: "日期",
            dateTime: "日期时间",
            dateRange: "日期区间",
            select: "下拉框",
            treeSelect: "树形下拉框",
            checkbox: "多选框",
            rate: "星级组件",
            radio: "单选框",
            radioButton: "按钮单选框",
            code: "代码框",
            switch: "开关",
          }} rules={[{required: false}]}/>
          <ProFormText label={'配置说明'} name={'remark'}/>
          <ProFormDependency name={['input_type']}>
            {({input_type}) => {
              if (["select", "radio", "radioButton", "checkbox", "treeSelect"].includes(input_type)) {
                return <>
                  <ProFormTextArea label={'选择/默认数据'}
                                   extra={'用于配置下拉、单选、复选的数据，格式例子：{"local":"本地"}，优先于字典配置'}
                                   name={'config_select_data'}/>
                  <ProFormText label={'字典配置'}
                               extra={'使用字典值配置选项框'}
                               name={'dict_code'}/>
                </>
              }
              return null
            }}
          </ProFormDependency>
        </ProForm>

      </ProCard>
      <ModalForm modalProps={{
        destroyOnClose: true,
      }} key={'group'} onFinish={async (values) => {
        try {
          await createSettingConfigGroup(values)
          message.success("添加成功")
          setGroupFormModal(false)
          await loadGroups()
        } catch (e) {
          //@ts-ignore
          message.error(e.message)
        }
      }} open={groupFormModal} onOpenChange={setGroupFormModal} title={'添加配置组'}>
        <BetaSchemaForm columns={[
          {
            title: "组名称",
            dataIndex: "name",
            formItemProps: {
              rules: [{
                required: true,
              }]
            }
          },
          {
            title: "组编码",
            dataIndex: "code",
            formItemProps: {
              rules: [{
                required: true,
              }]
            }
          },
          {
            title: "备注",
            dataIndex: "remark",
            valueType: "textarea"
          }
        ]} layoutType={'Embed'}/>
      </ModalForm>
      <SettingConfigModal open={open} onOpen={setOpen} group_code={activeKey}/>
    </div>
  </>
}
