"use client"

import {<PERSON><PERSON>, <PERSON>er, InputN<PERSON><PERSON>, message, Popconfirm, Space} from "antd";
import {
  ActionType,
  ModalForm, ProForm,
  ProFormDependency,
  ProFormDigit,
  ProFormSelect,
  ProFormText, ProFormTextArea,
  ProTable
} from "@ant-design/pro-components";
import {ProColumnType} from "@ant-design/pro-table";
import {inputTypeValue, SettingConfig} from "@/app/(admin)/admin/setting/data";
import {getSettingConfigList, updateSettingConfig, updateSettingConfigSort} from "@/app/(admin)/admin/setting/api";
import {useRef, useState} from "react";
import {debounce, throttle} from "@/utils/helper";

export default function SettingConfigModal(props: {
  group_code: string,
  open: boolean,
  onOpen: (v: boolean) => void
}) {

  const [open, setOpen] = useState(false)

  const [form] = ProForm.useForm()

  const actionRef = useRef<ActionType>()

  const updateSort = throttle(async (record, sort) => {
    try {
      await updateSettingConfigSort(props.group_code, {
        key: record.key,
        sort: sort,
      })
      message.success("更新成功")
      actionRef.current?.reload()
    } catch (e) {
      // @ts-ignore
      message.error(e.message || '更新失败')
    }
  }, 3000)

  const columns: ProColumnType<SettingConfig>[] = [
    {
      title: "配置名称",
      dataIndex: "name"
    },
    {
      title: "配置标识",
      dataIndex: "key"
    },
    {
      title: "配置值",
      dataIndex: "value",
      search: false,
    },
    {
      title: "排序",
      dataIndex: "sort",
      search: false,
      render: (dom, record) => {
        return <>
          <InputNumber defaultValue={record.sort} onChange={async (v) => {
            await updateSort(record, v)
          }}/>
        </>
      }
    },
    {
      title: "输入组件",
      dataIndex: "input_type",
      valueType: "select",
      valueEnum: inputTypeValue,
      search: false,
    },
    {
      title: "操作",
      valueType: 'option',
      render: function (text, record) {
        return <Space>
          <Button type={'primary'} size={'small'} onClick={() => {
            form.setFieldsValue(record)
            setTimeout(() => {
              setOpen(true)
            }, 100)
          }}>编辑</Button>
        </Space>
      },
      search: false,
    }
  ];
  return <>
    <Drawer size={'large'} destroyOnClose title={''} open={props.open} onClose={() => props.onOpen(false)}>
      <ProTable<SettingConfig> actionRef={actionRef} columns={columns} request={() => {
        return getSettingConfigList(props.group_code)
      }} pagination={false}></ProTable>
    </Drawer>
    <ModalForm form={form} open={open} onFinish={async (values) => {
      try {
        await updateSettingConfig({group_code: props.group_code, ...values})
        message.success("更新成功")
        setOpen(false)
        form.resetFields()
      } catch (e) {
        //@ts-ignore
        message.error(e.message)
      }
    }} onOpenChange={setOpen} modalProps={{

      destroyOnClose: true,
    }}>
      <ProFormText label={'配置名称'} name={'name'} rules={[{required: true}]}/>
      <ProFormText label={'配置标识'} name={'key'} rules={[{required: true}]}/>
      <ProFormText label={'配置值'} name={'value'} rules={[{required: false}]}/>
      <ProFormDigit label={'排序'} name={'sort'} initialValue={0} rules={[{required: false}]}/>
      <ProFormSelect label={'输入组件'} initialValue={'text'} name={'input_type'} valueEnum={inputTypeValue}
                     rules={[{required: false}]}/>
      <ProFormText label={'配置说明'} name={'remark'}/>
      <ProFormDependency name={['input_type']}>
        {({input_type}) => {
          if (["select", "radio", "radioButton", "checkbox", "treeSelect"].includes(input_type)) {
            return <>
              <ProFormTextArea label={'选择/默认数据'}
                               extra={'用于配置下拉、单选、复选的数据，格式例子：{"local":"本地"}，优先于字典配置'}
                               name={'config_select_data'}/>
              <ProFormText label={'字典配置'}
                           extra={'使用字典值配置选项框'}
                           name={'dict_code'}/>
            </>
          }
          return null
        }}
      </ProFormDependency>
    </ModalForm>
  </>
}