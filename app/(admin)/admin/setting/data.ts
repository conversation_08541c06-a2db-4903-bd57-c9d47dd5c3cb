export interface SettingConfigGroup {
  id: number;
  name: string;
  code: string;
  remark: string
}

export interface SettingConfig {
  group_id: number;
  name: string;
  key: string;
  value: any;
  input_type: string;
  config_select_data: any;
  sort: number;
  remark: string;
  dict_code:string
}

export const inputTypeValue = {
  text: "文本框",
  textarea: "文本域",
  password: "密码框",
  date: "日期",
  dateTime: "日期时间",
  dateRange: "日期区间",
  select: "下拉框",
  treeSelect: "树形下拉框",
  checkbox: "多选框",
  rate: "星级组件",
  radio: "单选框",
  radioButton: "按钮单选框",
  code: "代码框",
  switch: "开关",
}