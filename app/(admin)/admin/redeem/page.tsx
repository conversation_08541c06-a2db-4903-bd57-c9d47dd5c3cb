"use client"

import { <PERSON><PERSON><PERSON><PERSON> } from "@ant-design/pro-layout";
import {
  ActionType,
  ModalForm,
  ProFormDigit,
  ProFormRadio,
  ProFormText,
  ProFormTextArea,
  ProTable,
  ProFormSelect,
  ProFormDateTimePicker,
  ProForm,
  ProFormDependency
} from "@ant-design/pro-components";
import { ProColumnType } from "@ant-design/pro-table";
import { Button, message, Popconfirm, Space, Modal } from "antd";
import { useEffect, useRef, useState } from "react";
import {
  createRedeemCode,
  deleteRedeemCode,
  getRedeemCodeList,
  getRedeemRecordList,
  readRedeemCode,
  updateRedeemCode,
} from "./api";
import { RedeemCode, RedeemRecord } from "./data";
import { getApiList } from "@/app/(admin)/admin/api/api";
import { getPlanList } from "@/app/(admin)/admin/plan/api";
import { FreeA<PERSON>, ProApi } from "@/config/data";

export default function Page() {
  const [current, setCurrent] = useState<RedeemCode | null>()
  const actionRef = useRef<ActionType>()
  const [open, setOpen] = useState(false)
  const [targetOptions, setTargetOptions] = useState<{ label: string; value: number; }[]>([])
  const [form] = ProForm.useForm();
  const [apiMap, setApiMap] = useState<Record<number, string>>({});
  const [planMap, setPlanMap] = useState<Record<number, string>>({});
  const [recordModalOpen, setRecordModalOpen] = useState(false);
  const [currentCode, setCurrentCode] = useState<RedeemCode | null>(null);
  const recordActionRef = useRef<ActionType>();

  const handleDelete = async (ids: string) => {
    try {
      await deleteRedeemCode(ids)
      message.success("删除成功")
      actionRef.current?.reload()
    } catch (e) {
      // @ts-ignore
      message.error(e?.message || '删除失败')
    }
  }

  const fetchAllTargets = async () => {
    try {
      // 获取API列表
      const apiRes = await getApiList({ pageSize: 100, current: 1 });
      const apis = apiRes?.data || [];
      const apiMapping = apis.reduce((acc: Record<number, string>, cur: any) => {
        acc[cur.id] = cur.name;
        return acc;
      }, {});
      setApiMap(apiMapping);

      // 获取Plan列表
      const planRes = await getPlanList({ pageSize: 100, current: 1 });
      const plans = planRes?.data || [];
      const planMapping = plans.reduce((acc: Record<number, string>, cur: any) => {
        acc[cur.id] = cur.name;
        return acc;
      }, {});
      setPlanMap(planMapping);
    } catch (e) {
      console.error('Error fetching targets:', e);
      message.error('获取目标数据失败');
    }
  };

  // 组件加载时获取数据
  useEffect(() => {
    fetchAllTargets();
  }, []);

  const recordColumns: ProColumnType<RedeemRecord>[] = [
    {
      title: "用户",
      dataIndex: "user.nickname",
      render: (_, record) => {
        return <>{record?.user?.nickname}</>
      }
    },
    {
      title: "手机号",
      dataIndex: "user.phone",
      render: (_, record) => {
        return <>{record?.user?.phone}</>
      }
    },
    {
      title: "用户ID",
      dataIndex: "user_id",
    },
    {
      title: "兑换码",
      dataIndex: "code",
    },
    {
      title: "类型",
      dataIndex: "type",
      valueEnum: {
        "plan": "VIP会员",
        "api": "API接口"
      }
    },
    {
      title: "目标",
      dataIndex: "target_id",
      render: (_, record) => {
        if (record.type === 'plan') {
          return planMap[record.target_id] || record.target_id;
        } else {
          return apiMap[record.target_id] || record.target_id;
        }
      }
    },
    {
      title: "数值",
      dataIndex: "value",
      render: (_, record) =>
        record.type === 'plan' ? `${record.value}天` : `${record.value}次`
    },
    {
      title: "兑换时间",
      dataIndex: "created_at",
      valueType: 'dateTime',
    }
  ];

  const columns: ProColumnType<RedeemCode>[] = [
    {
      title: "兑换码",
      dataIndex: "code",
    },
    {
      title: "类型",
      dataIndex: "type",
      valueEnum: {
        "plan": "VIP会员",
        "api": "API接口"
      }
    },
    {
      title: "是否为优惠码",
      dataIndex: "is_discount",
      valueEnum: {
        "Y": "是",
        "N": "否"
      }
    },

    {
      title: "目标",
      dataIndex: "target_id",
      render: (_, record) => {
        if (record.type === 'plan') {
          return planMap[record.target_id] || record.target_id;
        } else {
          return apiMap[record.target_id] || record.target_id;
        }
      },
      search: false,
    },
    {
      title: "优惠数据",
      dataIndex: "discount_data",
      render: (_, record) => {
        return <>{record.is_discount === 'Y' ? `${record.discount_type === 'percent' ? `${record.discount_value}%` : `-${record.discount_value}元`} (最低消费${record.min_amount}元)` : ''}</>
      }
    },
    {
      title: "数值",
      dataIndex: "value",
      render: (_, record) =>
        record.type === 'plan' ? `${record.value}天` : `${record.value}次`,
      search: false,
    },
    {
      title: "最大使用次数",
      dataIndex: "max_uses",
      search: false,
    },
    {
      title: "已使用次数",
      dataIndex: "used_count",
      search: false,
    },
    {
      title: "过期时间",
      dataIndex: "expire_time",
      valueType: 'dateTime',
      search: false,
    },
    {
      title: "备注",
      dataIndex: "remark",
      search: false,
    },
    {
      title: "创建时间",
      dataIndex: "created_at",
      valueType: 'dateTime',
      search: false,
    },
    {
      title: "操作",
      valueType: 'option',
      render: (_, record) => (
        <Space>
          <Button type={'link'} size={'small'} onClick={() => {
            setCurrent(record)
            setOpen(true)
          }}>编辑</Button>
          <Button
            type={'link'}
            size={'small'}
            onClick={() => {
              setCurrentCode(record);
              setRecordModalOpen(true);
            }}
          >
            兑换记录
          </Button>
          <Popconfirm
            title={'确认删除吗？'}
            description={'删除后不可恢复'}
            onConfirm={() => handleDelete(record.id.toString())}
          >
            <Button type={'link'} size={'small'} danger>删除</Button>
          </Popconfirm>
        </Space>
      ),
    }
  ]

  const fetchTargetOptions = async (type: 'plan' | 'api') => {
    try {
      if (type === 'plan') {
        const res = await getPlanList({
          pageSize: 100,
          current: 1
        });
        const plans = res?.data || [];
        const options = plans.map((item: any) => ({
          label: `${item.name} (${item.price_month}元/月)`,
          value: item.id
        }));

        const formValues = form.getFieldsValue();
        if (formValues.is_discount === 'Y') {
          options.unshift({
            label: '不限',
            value: 0
          });
        }

        setTargetOptions(options);
      } else if (type === 'api') {
        const res = await getApiList({
          pageSize: 100,
          current: 1,
          type: ProApi
        });
        const apis = res?.data || [];
        const options = apis.map((item: any) => ({
          label: item.name,
          value: item.id
        }));
        const formValues = form.getFieldsValue();
        if (formValues.is_discount === 'Y') {
          options.unshift({
            label: '不限',
            value: 0
          });
        }
        setTargetOptions(options);
      }
      form.setFieldValue('target_id', undefined)
    } catch (e) {
      console.error('Error fetching target options:', e);
      // message.error('获取目标选项失败');
    }
  };

  return (
    <PageContainer>
      <ProTable<RedeemCode>
        params={{
          orderBy: "id",
          orderType: "desc",
        }}
        actionRef={actionRef}
        rowKey={'id'}
        columns={columns}
        scroll={{ x: 1200 }}
        request={async (params) => {
          // 如果映射为空，重新获取数据
          if (Object.keys(apiMap).length === 0 || Object.keys(planMap).length === 0) {
            await fetchAllTargets();
          }
          return getRedeemCodeList(params);
        }}
        toolBarRender={() => [
          <Button type={'primary'} key={'add'} onClick={() => setOpen(true)}>
            添加兑换码
          </Button>
        ]}
      />
      <ModalForm
        form={form}
        title={current ? '编辑兑换码' : '新增兑换码'}
        open={open}
        onOpenChange={(visible) => {
          setOpen(visible);
          if (!visible) {
            setTargetOptions([]);
            setCurrent(null);
          }
        }}
        request={async () => {
          if (current) {
            const data = await readRedeemCode(current.id);
            if (data.type) {
              await fetchTargetOptions(data.type);
            }
            return data;
          }
          return {};
        }}
        onFinish={async (values) => {
          try {
            if (current) {
              await updateRedeemCode(current.id, values)
              message.success("更新成功")
            } else {
              await createRedeemCode(values)
              message.success("创建成功")
            }
            actionRef.current?.reload()
            setOpen(false)
            setCurrent(null)
          } catch (e) {
            // @ts-ignore
            message.error(e?.message || (current ? '更新失败' : '创建失败'))
          }
        }}
        modalProps={{
          destroyOnClose: true
        }}
      >
        <ProFormText
          label={'兑换码'}
          name={'code'}
          help={"不填则自动生成"}
        />
        <ProFormRadio.Group
          label={'类型'}
          name={'type'}
          rules={[{ required: true }]}
          valueEnum={{
            "plan": "VIP会员",
            "api": "API接口"
          }}
          fieldProps={{
            onChange: async (e) => {
              const type = e.target.value;
              await fetchTargetOptions(type);
            }
          }}
        />
        <ProFormRadio.Group
          label={'是否为优惠码'}
          name={'is_discount'}
          rules={[{ required: true }]}
          valueEnum={{
            "Y": "是",
            "N": "否"
          }}
        />
        <ProFormDependency name={['is_discount', 'type']}>
          {({ is_discount, type }) => {
            if (type && !targetOptions.length) {
              fetchTargetOptions(type);
            }
            return <ProFormSelect
              label={'目标'}
              name={'target_id'}
              rules={[{ required: true }]}
              options={targetOptions}
              dependencies={['type']}
              disabled={targetOptions.length === 0}
              placeholder={targetOptions.length === 0 ? '请先选择类型' : '请选择目标'}
              fieldProps={{
                loading: targetOptions.length === 0,
                showSearch: true,
                filterOption: (input, option) =>
                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
              }}
            />
          }}
        </ProFormDependency>
        <ProFormDependency name={['is_discount']}>
          {({ is_discount }) => {
            return (
              <>
                {is_discount === "Y" && <>
                  <ProFormRadio.Group
                    label={'优惠类型'}
                    name={'discount_type'}
                    rules={[{ required: true }]}
                    valueEnum={{
                      "percent": "百分比",
                      "amount": "金额"
                    }}
                  />
                  <ProFormDigit
                    label={'优惠值'}
                    name={'discount_value'}
                    rules={[{ required: true }]}
                  />
                  <ProFormDigit
                    label={'最低消费'}
                    name={'min_amount'}
                    rules={[{ required: true }]}
                  />
                </>}
                {is_discount === "N" && <>
                  <ProFormDigit
                    label={'数值'}
                    name={'value'}
                    rules={[{ required: true }]}
                    help={"如果是VIP会员，则表示天数；如果是API接口，则表示次数"}
                  />
                </>}
              </>
            )
          }}

        </ProFormDependency>
        <ProFormDigit
          label={'最大使用次数'}
          name={'max_uses'}
          initialValue={1}
          rules={[{ required: true }]}
        />
        <ProFormDateTimePicker
          label={'过期时间'}
          name={'expire_time'}
          rules={[{ required: true }]}
        />
        <ProFormTextArea
          label={'备注'}
          name={'remark'}
        />
      </ModalForm>

      <Modal
        title="兑换记录"
        open={recordModalOpen}
        onCancel={() => {
          setRecordModalOpen(false);
          setCurrentCode(null);
        }}
        footer={null}
        width={1000}
      >
        <ProTable<RedeemRecord>
          actionRef={recordActionRef}
          columns={recordColumns}
          params={{
            code_id: currentCode?.id
          }}
          request={getRedeemRecordList}
          search={false}
          options={false}
          pagination={{
            pageSize: 10
          }}
          rowKey="id"
          dateFormatter="string"
          toolbar={{
            title: `兑换码：${currentCode?.code || ''}`
          }}
        />
      </Modal>
    </PageContainer>
  )
}