import axiosInstance from "@/lib/axios";
import { RedeemCode, RedeemRecord } from "./data";


// 兑换码相关接口
export async function getRedeemCodeList(params: any) {
  return axiosInstance.get("/admin/redeem_code/index", { params })
}

export async function readRedeemCode(id: number) {
  return axiosInstance.get(`/admin/redeem_code/read/${id}`)
}

export async function createRedeemCode(values: Partial<RedeemCode>) {
  return axiosInstance.post(`/admin/redeem_code/save`, values)
}

export async function updateRedeemCode(id: number, values: Partial<RedeemCode>) {
  return axiosInstance.put(`/admin/redeem_code/update/${id}`, values)
}

export async function deleteRedeemCode(ids: string) {
  return axiosInstance.delete(`/admin/redeem_code/delete`, {
    data: { ids }
  })
}

// 兑换记录相关接口
export async function getRedeemRecordList(params: any) {
  return axiosInstance.get("/admin/redeem_record/index", { params })
}

export async function readRedeemRecord(id: number) {
  return axiosInstance.get(`/admin/redeem_record/read/${id}`)
}