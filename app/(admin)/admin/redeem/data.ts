import {STATUS} from "@/config/data";

export interface RedeemCode {
  id: number;
  code: string;
  type: 'plan' | 'api';
  is_discount: string;
  discount_type: 'percent' | 'amount';
  discount_value: number;
  min_amount: number;
  target_id: number;
  value: number;
  max_uses: number;
  used_count: number;
  expire_time?: string;
  remark?: string;
  status: STATUS;
  created_at?: string;
  updated_at?: string;
}

export interface RedeemRecord {
  id: number;
  user_id: number;
  code_id: number;
  code: string;
  type: 'plan' | 'api';
  target_id: number;
  value: number;
  created_at?: string;
  updated_at?: string;
}
