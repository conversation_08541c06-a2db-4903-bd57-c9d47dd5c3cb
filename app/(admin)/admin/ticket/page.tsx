"use client"

import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, message, Popconfirm} from "antd";
import {ActionType, LightFilter, ProFormDatePicker, ProTable} from "@ant-design/pro-components";
import {ProColumnType} from "@ant-design/pro-table";
import {getLoginLogs} from "@/app/(dashboard)/dashboard/secure/log/api";
import {getTicketCategory, getTickets} from "@/app/(dashboard)/dashboard/ticket/api";
import Link from "next/link";
import {useEffect, useRef, useState} from "react";
import {destroy, getList, getTicketStatics} from "@/app/(admin)/admin/ticket/api";
import {deleteBanner} from "@/app/(admin)/admin/banner/api";

const renderBadge = (count: number, active = false) => {
  return (
    <Badge
      count={count}
      style={{
        marginBlockStart: -2,
        marginInlineStart: 4,
        color: active ? '#1890FF' : '#999',
        backgroundColor: active ? '#E6F7FF' : '#eee',
      }}
    />
  );
};
export default function Page() {
  const actionRef = useRef<ActionType>()
  const [activeKey, setActiveKey] = useState<React.Key>('1');
  const [statics, setStatics] = useState({})
  const [searchParams, setSearchParams] = useState({})
  const handleDelete = async (ids: string) => {
    try {
      await destroy(ids)
      message.success("删除成功")
      actionRef.current?.reload()
    } catch (e) {
      // @ts-ignore
      message.error(e?.message || '删除失败')
    }
  }

  const columns: ProColumnType[] = [{
    dataIndex: "ticket_no",
    title: "工单编号",
    search: true,
  },
    {
      dataIndex: "user",
      title: "提单用户",
      render: function (_, entity) {
        return <Link href={`/admin/user?username=${entity?.user?.username}`}>{entity?.user?.username}</Link>
      },
      search: false,
    },
    {
      dataIndex: "title",
      title: "工单标题",
      search: false,
    },
    {
      dataIndex: "category",
      title: "工单分类",
      request: async () => {
        try {
          const {ticket_category} = await getTicketCategory()
          return ticket_category;
        } catch {
          return []
        }
      }
    },
    {
      dataIndex: "status",
      title: "工单状态",
      valueType: "select",
      valueEnum: {
        1: "待处理",
        2: "已接单",
        3: "待回复",
        4: "已完成",
      },
      search: false,
    },
    {
      dataIndex: "created_at",
      title: "创建时间",
      search: false,
    },
    {
      valueType: "option",
      title: "操作",
      render: (_, entity) => {
        return <>
          <div className={'flex gap-x-1'}>
            <div><Button type={'link'} href={`ticket/${entity.ticket_no}`} size={'small'}>详情</Button></div>
            <div><Popconfirm title={'是否删除'}><Button type={'link'} danger size={'small'} onClick={async () => {
              //@ts-ignore
              await handleDelete([entity.id])
            }}>删除</Button></Popconfirm></div>
          </div>
        </>
      }
    },
  ];
  const handleGetTicketStatics = async () => {
    try {
      const data = await getTicketStatics(searchParams)
      //@ts-ignore
      const temp = {}
      for (const datum of data) {
        temp["status_" + datum.status] = datum.count
      }
      setStatics(temp)
      console.log(temp)
    } catch {

    }
  }
  useEffect(() => {
    handleGetTicketStatics()
  }, [])

  return <>
    <Card bordered={false} title={'工单管理'}>
      <ProTable pagination={{
        pageSize: 10,
      }} beforeSearchSubmit={async (params) => {
        setSearchParams(params)
        handleGetTicketStatics()
      }} onLoad={() => {
        console.log('on load')
      }} toolbar={{
        menu: {
          type: 'tab',
          activeKey: activeKey,
          filter: (
            <LightFilter>
              <ProFormDatePicker name="startdate" label="响应日期"/>
            </LightFilter>
          ),
          items: [
            {
              key: '1',
              label: <span>待处理{renderBadge(statics["status_1"] || 0, activeKey === '1')}</span>,
            },
            {
              key: '2',
              label: <span>已接单{renderBadge(statics["status_2"] || 0, activeKey === '2')}</span>,
            },
            {
              key: '3',
              label: <span>待回复{renderBadge(statics["status_3"] || 0, activeKey === '3')}</span>,
            },
            {
              key: '4',
              label: <span>已完成{renderBadge(statics["status_4"] || 0, activeKey === '4')}</span>,
            },
          ],
          onChange: (key) => {
            setActiveKey(key as string);
            console.log(searchParams)
            actionRef.current?.reload()
          },
        },

      }} columns={columns} params={{
        status: activeKey
      }} actionRef={actionRef} request={async (params, sort, filter) => {
        try {
          return await getList(Object.assign({status: activeKey, ...searchParams}))
        } catch {
          return {}
        }
      }}></ProTable>
    </Card>
  </>
}