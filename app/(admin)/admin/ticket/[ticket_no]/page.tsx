"use client"

import {<PERSON><PERSON>, <PERSON><PERSON>, Card, Col, List, message, Row, Space} from "antd";
import {ProDescriptions, ProForm, ProFormTextArea} from "@ant-design/pro-components";
import {useEffect, useState} from "react";
import {useRouter} from "next/navigation";
import {createTicketReply, makeTicketOrder, makeTicketSuccess, read} from "@/app/(admin)/admin/ticket/api";
import {getDictData} from "@/app/(admin)/admin/dict/api";
import Link from "next/link";

export default function Page({params}: { params: { ticket_no } }) {
  const router = useRouter()
  const [ticket, setTicket] = useState()
  const [replies, setReplies] = useState([])
  const [form] = ProForm.useForm()
  const descriptionColumns = [
    {
      dataIndex: "ticket_no",
      title: "工单编号",
      search: true,
    },
    {
      dataIndex: "user",
      title: "提单用户",
      render: function (_, entity) {
        return <Link href={`/admin/user?username=${entity?.user?.username}`}>{entity?.user?.username}</Link>
      } 
    },
    {
      dataIndex: "title",
      title: "工单标题",
      search: false,
    },
    {
      dataIndex: "category",
      title: "工单分类",
      request: async () => getDictData("ticket_category")
    },
    {
      dataIndex: "status",
      title: "工单状态",
      valueType: "select",
      valueEnum: {
        1: "待处理",
        2: "已接单",
        3: "待回复",
        4: "已完成",
      }
    },
    {
      dataIndex: "created_at",
      title: "创建时间",
      search: false,
    },
  ]
  useEffect(() => {
    if (params.ticket_no) {
      read(params.ticket_no).then((data) => {
        //@ts-ignore
        setTicket(data)
        //@ts-ignore
        setReplies(data.replies)
      })
    } else {
      router.back()
    }
  }, [params]);
  return <div className={'flex flex-col gap-10'}>
    <div><Card extra={<Button size={'small'} type={'link'} onClick={() => {
      router.back()
    }}>返回</Button>} bordered={false} title={'基本信息'}>
      <ProDescriptions dataSource={ticket} columns={descriptionColumns}></ProDescriptions>
      <div className={'mt-2 text-right'}>
        <Space>
          {ticket && ticket.status == 1 && <Button type={'primary'} size={'small'} onClick={async () => {
            try {
              await makeTicketOrder({ticket_no: params.ticket_no})
              message.success("已接单")
              router.refresh()
            } catch (e) {
              message.error(e.message || '接单失败')
            }
          }}>接单</Button>}
          {ticket && ticket.status != 4 && <Button type={'primary'} size={'small'} onClick={async () => {
            try {
              await makeTicketSuccess({ticket_no: params.ticket_no})
              message.success("工单已完成")
              router.refresh()
            } catch (e) {
              message.error(e.message || '标记失败')
            }
          }}>标记完成</Button>}
        </Space>
      </div>
    </Card></div>

    <div>
      <Card bordered={false} title={'沟通记录'}>
        <List
          itemLayout="horizontal"
          dataSource={replies}
          renderItem={(item, index) => (
            <List.Item>
              <List.Item.Meta
                avatar={<Avatar
                  src={item.reply_type === 'user' ? '/images/ticket/user.png' : '/images/ticket/kefu.png'}/>}
                title={<div>
                  <span>{item.reply_type === 'user' ? '客户' : "我"}</span>
                  <span className={'ml-2'}>{item.created_at}</span>
                </div>}
                description={<div className={'font-bold text-[#2A303B]'}>{item.reply_content}</div>}
              />
            </List.Item>
          )}
        />
      </Card>
    </div>
    {ticket && (ticket.status == 2 || ticket.status == 3) && <div>
      <Card bordered={false} title={'发表回复'}>
        <div className={'w-[500px] p-5'}>
          <ProForm
            form={form}
            labelCol={{span: 8}}
            wrapperCol={{span: 16}}
            layout={'horizontal'}

            autoComplete="off"
            submitter={{
              render: (props, doms) => (
                <Row>
                  <Col span={16} offset={8}>
                    <Space>{doms}</Space>
                  </Col>
                </Row>
              )
            }}
            onFinish={async (values) => {
              // 在这里可以处理表单提交
              try {
                await createTicketReply(Object.assign({ticket_no: params.ticket_no, ...values}))
                message.success('提交成功');
                router.refresh()
                form.resetFields()
              } catch (e) {
                message.error(e.message || '添加失败')
              }
            }}
          >
            <ProFormTextArea label={'回复内容'} name={'content'} rules={[{required: true}]}/>
            {/*   <ProFormUploadButton name={'attachment'} fieldProps={{
            multiple: false,
          }} label={'附件'}/>*/}
          </ProForm>
        </div>
      </Card>
    </div>}
  </div>
}