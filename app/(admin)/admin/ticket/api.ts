import axiosInstance from "@/lib/axios";

export async function getList(params: any) {
  return axiosInstance.get("/admin/ticket/index", {params})
}

export async function read(id: string) {
  return axiosInstance.get(`/admin/ticket/read/${id}`)
}


export async function destroy(ids: string) {
  return axiosInstance.delete(`/admin/ticket/delete`, {
    data: {ids: ids}
  })
}

export async function createTicketReply(data: any) {
  return axiosInstance.post("admin/ticket/create_reply", data)
}

export async function makeTicketSuccess(data: any) {
  return axiosInstance.post("admin/ticket/make_success", data)
}

export async function makeTicketOrder(data: any) {
  return axiosInstance.post("admin/ticket/make_order", data)
}

export async function getTicketStatics(params:any){
  return axiosInstance.get("admin/ticket/ticket_statics",{params})
}