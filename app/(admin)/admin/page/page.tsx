"use client"

import {<PERSON><PERSON><PERSON><PERSON>} from "@ant-design/pro-layout";
import {
  ActionType,
  ModalForm,
  ProFormItem, ProFormRadio,
  ProFormText,
  ProTable
} from "@ant-design/pro-components";
import {ProColumnType} from "@ant-design/pro-table";
import {Button, message, Popconfirm, Space, Switch} from "antd";
import {useEffect, useRef, useState} from "react";
import {getDictData} from "@/app/(admin)/admin/dict/api";
import {PageType} from "@/app/(admin)/admin/page/data";
import {create, destroy, getList, read, update, updateStatus} from "@/app/(admin)/admin/page/api";
import dynamic from "next/dynamic";
const Wangeditor = dynamic(() => import('@/components/common/wangeditor'), {ssr: false})

export default function Page() {
  const [current, setCurrent] = useState<PageType | null>()
  const actionRef = useRef<ActionType>()

  const [open, setOpen] = useState(false)

  const handleDelete = async (ids: string) => {
    try {
      await destroy(ids)
      message.success("删除成功")
      actionRef.current?.reload()
    } catch (e) {
      // @ts-ignore
      message.error(e?.message || '删除失败')
    }
  }

  const handleCloseOpen = () => {
    setOpen(false)
    setCurrent(null)
  }

  const columns: ProColumnType<PageType>[] = [
    {
      dataIndex:"id",
      title:"编号"
    },
    {
      dataIndex:"slug",
      title:"slug"
    },
    {
      dataIndex:"title",
      title:"标题"
    },
    {
      title: "状态",
      dataIndex: "status",
      valueType: "radio",
      valueEnum: {
        "Y": "启用",
        "N": "禁用",
      },
      render: function (text, record) {
        return <Switch value={record.status === 'Y'} onChange={async (v) => {
          try {
            await updateStatus(record.id, v)
            message.success("更新成功")
            actionRef.current?.reload()
          } catch {
            message.error("更新失败")
          }
        }}/>
      }
    },
    {
      title: "创建时间",
      dataIndex: "created_at",
      valueType: 'dateRange',
    },
    {
      title: "操作",
      valueType: 'option',
      render: function (text, record) {
        return <Space>
          <Button type={'link'} size={'small'} onClick={() => {
            setCurrent(record)
            setTimeout(() => {
              setOpen(true)
            }, 100)
          }}>编辑</Button>
          <Popconfirm title={'确认删除吗？'} description={'删除后不可恢复'} onConfirm={async () => {
            await handleDelete([record.id].join(","))
          }}>
            <Button type={'link'} size={'small'} danger>删除</Button>
          </Popconfirm>
        </Space>
      },
      search: false,
    }
  ]


  useEffect(() => {
    if (!open) {
      setCurrent(null)
    }
  }, [open])


  return <>
    <PageContainer>
      <ProTable<PageType> params={{
        orderBy: "id",
        orderType: "desc",
      }} search={false} actionRef={actionRef} rowKey={'id'} columns={columns} scroll={{x: 750}}
                        request={async (params) => {
                          return getList(params);
                        }}
                        toolBarRender={() => [
                          <Button type={'primary'} size={'small'} key={'add'} onClick={() => {
                            setOpen(true)
                          }}>添加页面</Button>
                        ]}
      >
      </ProTable>
      <ModalForm title={'页面管理'} open={open} onOpenChange={setOpen} request={async () => {
        if (current) {
          const data = await read(current?.id||0)
          //@ts-ignore
          return data;
        }
        return {}
      }} onFinish={async (values) => {
        if (current) {
          try {
            await update(current?.id||0, values)
            message.success("更新成功")
            actionRef.current?.reload()
            handleCloseOpen()
          } catch (e) {
            //@ts-ignore
            message.error(e.message ?? '更新失败')
          }

        } else {
          try {
            await create(values)
            message.success("创建成功")
            actionRef.current?.reload()
            handleCloseOpen()
          } catch (e) {
            //@ts-ignore
            message.error(e.message ?? '创建失败')
          }
        }
      }} modalProps={{
        destroyOnClose: true
      }}>

        <ProFormText label={'页面标题'} name={'title'} rules={[{required: true}]}/>
        <ProFormText label={'页面描述'} name={'description'}/>
        <ProFormText label={'页面Slug'} name={'slug'} rules={[{required: true}]}/>
        <ProFormItem name={'content'} label={'接口介绍'}>
          <Wangeditor/>
        </ProFormItem>

        <ProFormRadio.Group label={'状态'} name={'status'} initialValue={'Y'}
                            //@ts-ignore
                            request={async () => getDictData('status')}></ProFormRadio.Group>
      </ModalForm>

    </PageContainer>
  </>
}