import axiosInstance from "@/lib/axios";

// @ts-ignore
export async function getList(params: any) {
  return axiosInstance.get("/admin/page/index", {params})
}

export async function read(id: number) {
  return axiosInstance.get(`/admin/page/read/${id}`)
}

export async function update(id: number, values: any) {
  return axiosInstance.put(`/admin/page/update/${id}`, values)
}

export async function create(values: any) {
  return axiosInstance.post(`/admin/page/save`, values)
}

export async function destroy(ids: string) {
  return axiosInstance.delete(`/admin/page/delete`, {
    data: {ids: ids}
  })
}

export async function updateStatus(id: number, status: boolean) {
  return axiosInstance.post("/admin/page/status", {
    id: id,
    status: status ? 'Y' : "N"
  })
}
