"use client"

import {<PERSON><PERSON><PERSON><PERSON>} from "@ant-design/pro-layout";
import {ActionType, BetaSchemaForm, ModalForm, ProFormColumnsType, ProTable} from "@ant-design/pro-components";
import {ApiCategory} from "@/app/(admin)/admin/api_category/data";
import {ProColumnType} from "@ant-design/pro-table";
import {Button, message, Popconfirm, Radio, Space, Switch, Typography} from "antd";
import axiosInstance from "@/lib/axios";
import {useEffect, useRef, useState} from "react";
import {Dict} from "@/app/(admin)/admin/dict/data";
import DataItem from "@/app/(admin)/admin/dict/data_item";
import {deleteDict, getDictList, readDict, saveDict, updateDict, updateDictStatus} from "@/app/(admin)/admin/dict/api";

export default function Page() {
  const [current, setCurrent] = useState<Dict | null>()
  const actionRef = useRef<ActionType>()

  const [open, setOpen] = useState(false)

  const [itemOpen, setItemOpen] = useState(false)

  const handleDelete = async (ids: string) => {
    try {
      await deleteDict(ids)
      message.success("删除成功")
      actionRef.current?.reload()
    } catch (e) {
      // @ts-ignore
      message.error(e?.message || '删除失败')
    }
  }

  const handleCloseOpen = () => {
    setOpen(false)
    setCurrent(null)
  }

  const columns: ProColumnType<Dict>[] = [
    {
      title: "字典名称",
      dataIndex: "name",
    },
    {
      title: "字典标识",
      dataIndex: "code",
      render: function (text, record) {
        return <Typography.Link onClick={() => {
          setCurrent(record)
          setTimeout(() => {
            setItemOpen(true)
          }, 100)
        }}>{text}</Typography.Link>
      }
    },
    {
      title: "状态",
      dataIndex: "status",
      valueType: "radio",
      valueEnum: {
        "Y": "启用",
        "N": "禁用",
      },
      render: function (text, record) {
        return <Switch value={record.status === 'Y'} onChange={async (v) => {
          try {
            await updateDictStatus(record.id, v)
            message.success("更新成功")
            actionRef.current?.reload()
          } catch {
            message.error("更新失败")
          }
        }}/>
      }
    },
    {
      title: "更新时间",
      dataIndex: "updated_at",
      search: false,
    },
    {
      title: "操作",
      valueType: 'option',
      render: function (text, record) {
        return <Space>
          <Button type={'primary'} size={'small'} onClick={() => {
            setCurrent(record)
            setTimeout(() => {
              setItemOpen(true)
            }, 100)
          }}>字典数据</Button>
          <Button type={'primary'} size={'small'} onClick={() => {
            setCurrent(record)
            setTimeout(() => {
              setOpen(true)
            }, 100)
          }}>编辑</Button>
          <Popconfirm title={'确认删除吗？'} description={'删除后不可恢复'} onConfirm={async () => {
            await handleDelete([record.id].join(","))
          }}>
            <Button size={'small'} danger>删除</Button>
          </Popconfirm>
        </Space>
      },
      search: false,
    }
  ]

  const formColumns: ProFormColumnsType<Dict>[] = [
    {
      title: "字典名称",
      dataIndex: "name",
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      }
    },
    {
      title: "字典标识",
      dataIndex: "code",
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      }
    },
    {
      title: "字典状态",
      dataIndex: "status",
      formItemProps: {
        rules: [],
      },
      valueType: "radio",
      valueEnum: {
        "Y": "启用",
        "N": "禁用",
      }
    },
  ]

  useEffect(() => {
    if (!open) {
      setCurrent(null)
    }

  }, [open])
  useEffect(() => {
    if (!itemOpen) {
      setCurrent(null)
    }
  }, [itemOpen]);

  return <>
    <PageContainer>
      <ProTable<Dict> params={{
        orderBy: "id",
        orderType: "desc"
      }} actionRef={actionRef} rowKey={'id'} columns={columns} scroll={{x: 750}}
                      request={async (params) => {
                        return await getDictList(params)
                      }}
                      toolBarRender={() => [
                        <Button type={'primary'} size={'small'} key={'add'} onClick={() => {
                          setOpen(true)
                        }}>添加字典</Button>
                      ]}
      >
      </ProTable>
      <ModalForm title={'数据字典'} open={open} onOpenChange={setOpen} request={async () => {
        if (current) {
          return await readDict(current?.id)
        }
        return {}
      }} onFinish={async (values) => {
        if (current) {
          try {
            await updateDict(current?.id, values)
            message.success("更新成功")
            actionRef.current?.reload()
            handleCloseOpen()
          } catch (e) {
            //@ts-ignore
            message.error(e.message ?? '更新失败')
          }

        } else {
          try {
            await saveDict(values)
            message.success("创建成功")
            actionRef.current?.reload()
            handleCloseOpen()
          } catch (e) {
            //@ts-ignore
            message.error(e.message ?? '创建失败')
          }
        }
      }} modalProps={{
        destroyOnClose: true
      }}>
        <BetaSchemaForm<Dict>
          title={'数据字典'} columns={formColumns} layoutType={'Embed'}
          //@ts-ignore
        ></BetaSchemaForm>
      </ModalForm>

      <DataItem open={itemOpen} setOpen={setItemOpen}
        //@ts-ignore
                current={current}></DataItem>
    </PageContainer>
  </>
}