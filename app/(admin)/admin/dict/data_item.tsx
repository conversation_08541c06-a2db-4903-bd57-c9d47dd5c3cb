"use client"

import {Dict, DictItem} from "@/app/(admin)/admin/dict/data";
import {<PERSON><PERSON>, Drawer, InputNumber, message, Popconfirm, Space, Switch} from "antd";
import {useEffect, useRef, useState} from "react";
import {
  ActionType,
  BetaSchemaForm, EditableProTable,
  ModalForm,
  ProFormColumnsType,
  ProFormDigit,
  ProTable
} from "@ant-design/pro-components";
import axiosInstance from "@/lib/axios";
import {ProColumnType} from "@ant-design/pro-table";
import {
  deleteDictItem,
  getDictItemList, numberOperationDictItem,
  readDictItem,
  saveDictItem,
  updateDictItem,
  updateDictItemStatus
} from "@/app/(admin)/admin/dict/api";

export default function DataItem(props: {
  current?: Dict,
  open: boolean,
  setOpen: (v: boolean) => void
}) {
  const [current, setCurrent] = useState<DictItem | null>()
  const actionRef = useRef<ActionType>()

  const [open, setOpen] = useState(false)

  const handleDelete = async (ids: string) => {
    try {
      await deleteDictItem(ids)
      message.success("删除成功")
      actionRef.current?.reload()
    } catch (e) {
      // @ts-ignore
      message.error(e?.message || '删除失败')
    }
  }

  const handleCloseOpen = () => {
    setOpen(false)
    setCurrent(null)
  }

  const columns: ProColumnType<DictItem>[] = [
    {
      title: "字典标签",
      dataIndex: "label",
    },
    {
      title: "字典键值",
      dataIndex: "value",
    },
    {
      title: "排序",
      dataIndex: "sort",
      valueType: "digit",
      search: false,
      render: function (text, record) {
        return <>
          <InputNumber defaultValue={record.sort} onChange={async (v) => {
            try {
              await numberOperationDictItem(record.id, v ?? 1)
              message.success("更新成功")
              actionRef.current?.reload()
            } catch (e) {
              // @ts-ignore
              message.error(e.message || '更新失败')
            }
          }}/>
        </>
      }
    },
    {
      title: "状态",
      dataIndex: "status",
      valueType: "radio",
      valueEnum: {
        "Y": "启用",
        "N": "禁用",
      },
      render: function (text, record) {
        return <Switch value={record.status === 'Y'} onChange={async (v) => {
          try {
            await updateDictItemStatus(record.id, v)
            message.success("更新成功")
            actionRef.current?.reload()
          } catch {
            message.error("更新失败")
          }
        }}/>
      }
    },
    {
      title: "更新时间",
      dataIndex: "updated_at",
      search: false,
    },
    {
      title: "操作",
      valueType: 'option',
      render: function (text, record) {
        return <Space>
          <Button size={'small'} onClick={() => {
            setCurrent(record)
            setTimeout(() => {
              setOpen(true)
            }, 100)
          }}>编辑</Button>
          <Popconfirm title={'确认删除吗？'} description={'删除后不可恢复'} onConfirm={async () => {
            await handleDelete([record.id].join(","))
          }}>
            <Button size={'small'} danger>删除</Button>
          </Popconfirm>
        </Space>
      },
      search: false,
    }
  ]

  const formColumns: ProFormColumnsType<DictItem>[] = [
    {
      title: "字典标签",
      dataIndex: "label",
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      }
    },
    {
      title: "字典键值",
      dataIndex: "value",
      formItemProps: {}
    },
    {
      title: "排序",
      dataIndex: "sort",
      formItemProps: {},
      valueType: "digit",
    },
    {
      title: "状态",
      dataIndex: "status",
      formItemProps: {
        rules: [],
      },
      valueType: "radio",
      valueEnum: {
        "Y": "启用",
        "N": "禁用",
      }
    },
  ]

  useEffect(() => {
    if (!open) {
      setCurrent(null)
    }
  }, [open])
  useEffect(() => {
    return () => {
      setCurrent(null)
    }
  }, []);

  return <Drawer title={`${props.current?.name}`} width={1024} onClose={() => props.setOpen(false)} open={props.open}
                 destroyOnClose>
    <ProTable<DictItem> params={{
      code: props.current?.code,
      orderBy: "sort",
      orderType: "desc"
    }} actionRef={actionRef} rowKey={'id'} columns={columns} scroll={{x: 750}}
                        request={async (params) => {
                          return await getDictItemList(params)
                        }}
                        toolBarRender={() => [
                          <Button type={'primary'} size={'small'} key={'add'} onClick={() => {
                            setOpen(true)
                          }}>添加字典</Button>
                        ]}
    >
    </ProTable>
    <ModalForm title={'字典数据'} open={open} onOpenChange={setOpen} request={async () => {
      if (current) {
        return await readDictItem(current.id)
      }
      return {}
    }} onFinish={async (values) => {
      if (current) {
        try {
          await updateDictItem(current.id, values)
          message.success("更新成功")
          actionRef.current?.reload()
          handleCloseOpen()
        } catch (e) {
          //@ts-ignore
          message.error(e.message ?? '更新失败')
        }

      } else {
        try {
          await saveDictItem({...values, dict_id: props.current?.id, code: props.current?.code})
          message.success("创建成功")
          actionRef.current?.reload()
          handleCloseOpen()
        } catch (e) {
          //@ts-ignore
          message.error(e.message ?? '创建失败')
        }
      }
    }} modalProps={{
      destroyOnClose: true
    }}>
      <BetaSchemaForm<DictItem>
        title={'字典数据'} columns={formColumns} layoutType={'Embed'}
        //@ts-ignore
      ></BetaSchemaForm>
    </ModalForm>
  </Drawer>
}