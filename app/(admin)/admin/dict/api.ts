import axiosInstance from "@/lib/axios";

export async function getDictList(params: any) {
  return axiosInstance.get("/admin/dict/index", {params})
}

export async function readDict(id: number) {
  return axiosInstance.get(`/admin/dict/read/${id}`)
}

export async function updateDict(id: number, values: any) {
  return axiosInstance.put(`/admin/dict/update/${id}`, values)
}

export async function saveDict(values: any) {
  return axiosInstance.post(`/admin/dict/save`, values)
}

export async function deleteDict(ids: string) {
  return axiosInstance.delete(`/admin/dict/delete`, {data: {ids: ids}})
}

export async function updateDictStatus(id: number, status: boolean) {
  return axiosInstance.post("/admin/dict/status", {
    id: id,
    status: status ? 'Y' : "N"
  })
}

export async function getDictData(code: string) {
  return await axiosInstance.get("/admin/dict_datum/list", {
    params: {code: code}
  })
}

export async function getDictItemList(params: any) {
  return axiosInstance.get("/admin/dict_datum/index", {params})
}

export async function readDictItem(id: number) {
  return axiosInstance.get(`/admin/dict_datum/read/${id}`)
}

export async function saveDictItem(values: any) {
  return axiosInstance.post("/admin/dict_datum/save", values)
}

export async function updateDictItem(id: number, values: any) {
  return axiosInstance.put(`/admin/dict_datum/update/${id}`, values)
}

export async function deleteDictItem(ids: string) {
  return axiosInstance.delete(`/admin/dict_datum/delete`, {data: {ids: ids}})
}

export async function updateDictItemStatus(id: number, status: boolean) {
  return axiosInstance.post("/admin/dict_datum/status", {
    id: id,
    status: status ? 'Y' : "N"
  })
}

export async function numberOperationDictItem(id: number, number: number) {
  return axiosInstance.put("/admin/dict_datum/numberOperation", {
    numberName: "sort",
    numberValue: number,
    id: id,
  })
}