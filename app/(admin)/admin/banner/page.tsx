"use client"

import {<PERSON><PERSON><PERSON><PERSON>} from "@ant-design/pro-layout";
import {
  ActionType,
  ModalForm,
  ProFormDigit, ProFormRadio,
  ProFormText,
  ProTable
} from "@ant-design/pro-components";
import {ProColumnType} from "@ant-design/pro-table";
import {Button, message, Popconfirm, Space, Switch} from "antd";
import {useEffect, useRef, useState} from "react";
import {
  createBanner,
  deleteBanner,
  getBannerList, readBanner,
  updateBanner,
  updateBannerStatus
} from "@/app/(admin)/admin/banner/api";
import {Banner} from "@/app/(admin)/admin/banner/data";
import {getDictData} from "@/app/(admin)/admin/dict/api";
import {ProFormUploadButton} from "@ant-design/pro-form";
import {env} from "@/env";
import Cookies from "js-cookie";
import {AccessTokenKey} from "@/config/data";

export default function Page() {
  const [current, setCurrent] = useState<Banner | null>()
  const actionRef = useRef<ActionType>()

  const [open, setOpen] = useState(false)

  const handleDelete = async (ids: string) => {
    try {
      await deleteBanner(ids)
      message.success("删除成功")
      actionRef.current?.reload()
    } catch (e) {
      // @ts-ignore
      message.error(e?.message || '删除失败')
    }
  }

  const handleCloseOpen = () => {
    setOpen(false)
    setCurrent(null)
  }

  const columns: ProColumnType<Banner>[] = [
    {
      title: "轮播标题",
      dataIndex: "title",
    },
    {
      title: "轮播链接",
      dataIndex: "link"
    },
    {
      title: "轮播图片",
      dataIndex: "image",
      valueType: "image",
    },
    {
      title: "跳转方式",
      dataIndex: "target",
      valueType: "radio",
      valueEnum: {
        "_bank": "新开页面",
        "_self": "原页面"
      }
    },
    {
      title: "排序",
      dataIndex: "sort",
      valueType: "digit",
    },
    {
      title: "状态",
      dataIndex: "status",
      valueType: "radio",
      valueEnum: {
        "Y": "启用",
        "N": "禁用",
      },
      render: function (text, record) {
        return <Switch value={record.status === 'Y'} onChange={async (v) => {
          try {
            await updateBannerStatus(record.id, v)
            message.success("更新成功")
            actionRef.current?.reload()
          } catch {
            message.error("更新失败")
          }
        }}/>
      }
    },
    {
      title: "创建时间",
      dataIndex: "created_at",
      valueType: 'dateRange',
    },
    {
      title: "操作",
      valueType: 'option',
      render: function (text, record) {
        return <Space>
          <Button type={'link'} size={'small'} onClick={() => {
            setCurrent(record)
            setTimeout(() => {
              setOpen(true)
            }, 100)
          }}>编辑</Button>
          <Popconfirm title={'确认删除吗？'} description={'删除后不可恢复'} onConfirm={async () => {
            await handleDelete([record.id].join(","))
          }}>
            <Button type={'link'} size={'small'} danger>删除</Button>
          </Popconfirm>
        </Space>
      },
      search: false,
    }
  ]


  useEffect(() => {
    if (!open) {
      setCurrent(null)
    }
  }, [open])


  return <>
    <PageContainer>
      <ProTable<Banner> params={{
        orderBy: "id",
        orderType: "desc",
      }} search={false} actionRef={actionRef} rowKey={'id'} columns={columns} scroll={{x: 750}}
                        request={async (params) => {
                          return getBannerList(params);
                        }}
                        toolBarRender={() => [
                          <Button type={'primary'} size={'small'} key={'add'} onClick={() => {
                            setOpen(true)
                          }}>添加轮播</Button>
                        ]}
      >
      </ProTable>
      <ModalForm title={'首页轮播'} open={open} onOpenChange={setOpen} request={async () => {
        if (current) {
          const data = await readBanner(current?.id)
          //@ts-ignore
          if (data.image) {
            //@ts-ignore
            data.image = [{url: data.image, status: "done", id: -1}]
            return data;
          }
        }
        return {}
      }} onFinish={async (values) => {
        if (current) {
          try {
            await updateBanner(current?.id, values)
            message.success("更新成功")
            actionRef.current?.reload()
            handleCloseOpen()
          } catch (e) {
            //@ts-ignore
            message.error(e.message ?? '更新失败')
          }

        } else {
          try {
            await createBanner(values)
            message.success("创建成功")
            actionRef.current?.reload()
            handleCloseOpen()
          } catch (e) {
            //@ts-ignore
            message.error(e.message ?? '创建失败')
          }
        }
      }} modalProps={{
        destroyOnClose: true
      }}>

        <ProFormText label={'轮播标题'} name={'title'} rules={[{required: true}]}/>
        <ProFormText label={'轮播链接'} name={'link'} rules={[{required: true}]}/>
        <ProFormUploadButton fieldProps={{
          multiple: false,
          headers: {
            "Authorization": "Bearer " + Cookies.get(AccessTokenKey)
          },
          onChange:(info)=>{
            if (info.file.status === "done"){
              if(info.file.response.code!=200){
                //@ts-ignore
                message.error(info.file.response.message)

              }
            }
          }
        }} transform={(value) => {
          const img = value[0]
          if (img.url) {
            return img.url
          } else if (img.response) {
            return img.response.data.url
          }
          return null
        }} max={1} listType={'picture-card'} label={'轮播图'} name={'image'}
                             action={env.NEXT_PUBLIC_API_URL + '/admin/common/upload'}/>
        <ProFormRadio.Group label={'跳转方式'} name={'target'} rules={[{required: true}]} valueEnum={{
          "_bank": "新开页面",
          "_self": "原页面"
        }}></ProFormRadio.Group>
        <ProFormDigit label={'排序'} name={'sort'} initialValue={1}></ProFormDigit>
        <ProFormRadio.Group label={'状态'} name={'status'} initialValue={'Y'}
          //@ts-ignore
                            request={async () => getDictData('status')}></ProFormRadio.Group>
      </ModalForm>

    </PageContainer>
  </>
}