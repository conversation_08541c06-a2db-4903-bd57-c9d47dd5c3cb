import axiosInstance from "@/lib/axios";

// @ts-ignore
export async function getBannerList(params: any) {
  return axiosInstance.get("/admin/banner/index", {params})
}

export async function readBanner(id: number) {
  return axiosInstance.get(`/admin/banner/read/${id}`)
}

export async function updateBanner(id: number, values: any) {
  return axiosInstance.put(`/admin/banner/update/${id}`, values)
}

export async function createBanner(values: any) {
  return axiosInstance.post(`/admin/banner/save`, values)
}

export async function deleteBanner(ids: string) {
  return axiosInstance.delete(`/admin/banner/delete`, {
    data: {ids: ids}
  })
}

export async function updateBannerStatus(id: number, status: boolean) {
  return axiosInstance.post("/admin/banner/status", {
    id: id,
    status: status ? 'Y' : "N"
  })
}