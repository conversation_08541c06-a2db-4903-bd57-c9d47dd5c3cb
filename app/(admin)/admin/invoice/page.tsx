"use client"

import {<PERSON><PERSON><PERSON><PERSON>} from "@ant-design/pro-layout";
import {ActionType, BetaSchemaForm, ModalForm, ProFormColumnsType, ProTable} from "@ant-design/pro-components";
import {ApiCategory} from "@/app/(admin)/admin/api_category/data";
import {ProColumnType} from "@ant-design/pro-table";
import {Button, message, Popconfirm, Space} from "antd";
import axiosInstance from "@/lib/axios";
import {useEffect, useRef, useState} from "react";
import {
  createApiCategory, deleteApiCategory,
  getApiCategoryList,
  readApiCategory,
  updateApiCategory
} from "@/app/(admin)/admin/api_category/api";
import {createFromIconfontCN} from "@ant-design/icons";
import {Invoice} from "@/app/(admin)/admin/invoice/data";
import {getDictData} from "@/app/(admin)/admin/dict/api";
import {deleteInvoice, getInvoiceList} from "@/app/(admin)/admin/invoice/api";
import Link from "next/link";

export default function Page() {
  const actionRef = useRef<ActionType>()


  const handleDelete = async (ids: string) => {
    try {
      await deleteInvoice(ids)
      message.success("删除成功")
      actionRef.current?.reload()
    } catch (e) {
      // @ts-ignore
      message.error(e?.message || '删除失败')
    }
  }


  const columns: ProColumnType<Invoice>[] = [
    {
      title: "ID",
      dataIndex: "id",
    },
    {
      title: "发票类型",
      dataIndex: "invoice_type",
      // @ts-ignore
      request: async () => getDictData("invoice_type"),
      valueType: "select",
    },
    {
      title: "抬头类型",
      dataIndex:
        "tax_type",
      // @ts-ignore
      request: async () => getDictData("tax_type"),
      valueType: "select",
    },
    {
      title: "发票抬头",
      dataIndex: "tax_title",
    }
    ,
    {
      title: "税号",
      dataIndex: "tax_number",
    }
    ,
    {
      title: "开户银行",
      dataIndex: "tax_bank"
    }
    ,
    {
      title: "银行卡号",
      dataIndex: "tax_bank_number"
    }
    ,
    {
      title: "注册地址",
      dataIndex: "tax_address"
    }
    ,
    {
      title: "联系电话",
      dataIndex: "tax_phone"
    }
    ,
    {
      title: "发票金额",
      dataIndex: "money",
    }
    ,
    {
      title: "审核状态",
      dataIndex: "audit",
      //@ts-ignore
      request: async () => getDictData("audit_type"),
      valueType: 'select',
    }
    ,
    {
      title: "创建时间",
      dataIndex: "created_at", valueType: 'dateRange',
      hidden: true,
    },
    {
      title: "操作",
      valueType: 'option',
      render: function (text, record) {
        return <Space>
          <Link href={`/admin/invoice/${record.id}`}><Button type={'primary'} size={'small'}>详情</Button></Link>
        </Space>
      },
      search: false,
    }
  ]

  return <>
    <PageContainer>
      <ProTable<Invoice> params={{
        orderBy: "id",
        orderType: "desc",
      }} actionRef={actionRef} rowKey={'id'} columns={columns} scroll={{x: 750}}
                         request={async (params) => {
                           return getInvoiceList(params);
                         }}
                         toolBarRender={() => []}
      >
      </ProTable>
    </PageContainer>
  </>
}