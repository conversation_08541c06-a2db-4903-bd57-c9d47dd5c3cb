"use client"

import {
  ProCard,
  ProDescriptions,
  ProDescriptionsActionType,
  ProForm,
  ProFormSelect, ProFormText, ProFormTextArea,
  ProTable
} from "@ant-design/pro-components";
import {Button, message, Space, Typography} from "antd";
import {useRef, useState} from "react";
import {useRouter} from "next/navigation";
import {auditInvoice, getInvoiceOrderList, readInvoice} from "../api";
import {getDictData} from "../../dict/api";
import {Invoice} from "../data";
import {PageContainer} from "@ant-design/pro-layout";

export default function Page({params}: { params: { id: number } }) {
  const router = useRouter()
  const descriptionColumns = [
    {
      title: "ID",
      dataIndex: "id",
    },
    {
      title: "发票类型",
      dataIndex: "invoice_type",
      // @ts-ignore
      request: async () => getDictData("invoice_type"),
      valueType: "select",
    },
    {
      title: "抬头类型",
      dataIndex:
        "tax_type",
      // @ts-ignore
      request: async () => getDictData("tax_type"),
      valueType: "select",
    },
    {
      title: "关联订单",
      dataIndex: "orders",
      search: false,
    },
    {
      title: "发票抬头",
      dataIndex: "tax_title",
    }
    ,
    {
      title: "税号",
      dataIndex: "tax_number",
    }
    ,
    {
      title: "开户银行",
      dataIndex: "tax_bank"
    }
    ,
    {
      title: "银行卡号",
      dataIndex: "tax_bank_number"
    }
    ,
    {
      title: "注册地址",
      dataIndex: "tax_address"
    }
    ,
    {
      title: "联系电话",
      dataIndex: "tax_phone"
    }
    ,
    {
      title: "发票金额",
      dataIndex: "money",
    }
    ,
    {
      title: "审核状态",
      dataIndex: "audit",
      //@ts-ignore
      request: async () => getDictData("audit_type"),
      valueType: 'select',
    }
    ,
    {
      title: "创建时间",
      dataIndex: "created_at", valueType: 'dateRange',
      hidden: true,
    },
  ];
  const actionRef = useRef<ProDescriptionsActionType>();
  const [current, setCurrent] = useState<Invoice>()

  const [form] = ProForm.useForm()


  // @ts-ignore
  return <>
    <PageContainer title={'发票详情'}>
      <div className={'mb-5'}>
        <ProCard extra={<>
          <Button type={'link'} onClick={() => {
            actionRef.current?.reload()
          }}>刷新</Button>
          <Button type={'link'} onClick={() => {
            router.back()
          }}>返回</Button>
        </>}>
          <Typography.Title level={5}>基础信息</Typography.Title>
          <div>
            <ProDescriptions actionRef={actionRef}
              //@ts-ignore
                             columns={descriptionColumns}
              //@ts-ignore
                             request={async () => {
                               try {
                                 const invoice = await readInvoice(params.id)
                                 //@ts-ignore
                                 setCurrent(invoice)
                                 form.setFieldsValue(invoice)
                                 return {
                                   data: invoice,
                                   success: true,
                                 }
                               } catch (e) {
                                 //@ts-ignore
                                 message.error(e.message)
                                 router.back()
                               }

                             }}>

            </ProDescriptions>
          </div>
        </ProCard>
      </div>
      <div className={'mb-5'}>
        <ProCard size={'small'}>
          <Typography.Title level={5}>关联订单列表</Typography.Title>
          <ProTable size={'small'} columns={[
            {
              title: "ID",
              dataIndex: "id",
              search: false,
            },
            {
              title: "订单标题",
              dataIndex: "title",
              search: false,
            },
            {
              title: "订单类型",
              dataIndex: "order_type",
              //@ts-ignore
              request: async () => getDictData('order_type'),
              valueType: "select",
            },
            {
              title: "订单编号",
              dataIndex: "trade_no",
            },
            {
              title: "外部订单编号",
              dataIndex: "out_trade_no"
            },
            {
              title: "订单金额",
              dataIndex: "price",
              search: false
            },
            {
              title: "支付方式",
              dataIndex: "pay_method",
              //@ts-ignore
              request: async () => getDictData("pay_method"),
              valueType: "select",

            },
            {
              title: "支付状态",
              dataIndex: "order_status",
              //@ts-ignore

              request: async () => getDictData("order_status"),
              valueType: "select",
            },
            {
              title: "创建时间",
              dataIndex: "created_at",
              valueType: 'dateRange',
              hidden: true,
            },
          ]}
                    //@ts-ignore
                    request={async () => {
            const data = await getInvoiceOrderList(params.id)
            return {
              data: data,
              success: true,
            }
          }} pagination={false} search={false}>

          </ProTable>
        </ProCard>
      </div>
      <div>
        <ProCard>
          <Typography.Title level={5}>审核</Typography.Title>
          <ProForm form={form} submitter={{
            submitButtonProps: {
              disabled: current?.audit != 1
            },
            resetButtonProps: {
              disabled: current?.audit != 1
            }
          }} onFinish={async (values) => {
            try {
              await auditInvoice(params.id, values)
              message.success("审核成功");
              actionRef.current?.reload()
            } catch (e) {
              //@ts-ignore
              message.error(e.message)
            }
          }}>
            <ProFormSelect rules={[{
              required: true,
            }]} label={'审核状态'} name={'audit'}
                           //@ts-ignore
                           request={async () => getDictData('audit_type')}></ProFormSelect>
            <ProFormTextArea label={'审核意见'} name={'audit_text'}/>
            <ProFormText label={'发票下载地址'} name={'url'}/>
          </ProForm>
        </ProCard>
      </div>
    </PageContainer>
  </>
}