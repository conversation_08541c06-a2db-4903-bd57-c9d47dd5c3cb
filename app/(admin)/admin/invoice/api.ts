import axiosInstance from "@/lib/axios";

// @ts-ignore
export async function getInvoiceList(params: any) {
  return axiosInstance.get("/admin/invoice/index", {params})
}

export async function getInvoiceOrderList(id: number) {
  return axiosInstance.get("/admin/invoice/orders/" + id)
}


export async function readInvoice(id: number) {
  return axiosInstance.get(`/admin/invoice/read/${id}`)
}

export async function updateInvoice(id: number, values: any) {
  return axiosInstance.put(`/admin/invoice/update/${id}`, values)
}

export async function createInvoice(values: any) {
  return axiosInstance.put(`/admin/invoice/save`, values)
}

export async function deleteInvoice(ids: string) {
  return axiosInstance.delete(`/admin/invoice/delete`, {
    data: {ids: ids}
  })
}


export async function auditInvoice(id: number, values: any) {
  return axiosInstance.post(`/admin/invoice/audit/${id}`, values)
}