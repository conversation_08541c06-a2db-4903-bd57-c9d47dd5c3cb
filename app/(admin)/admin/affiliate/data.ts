import { STATUS } from "@/config/data";

// 提现记录接口
export interface WithdrawRecord {
  id: number;
  user_id: number;
  user?: {
    name: string;
    email: string;
  };
  amount: number;
  fee: number;
  actual_amount: number;
  withdraw_type: 'alipay' | 'wallet';
  account_name: string | null;
  account_no: string | null;
  status: 'pending' | 'approved' | 'completed' | 'rejected';
  admin_id: number | null;
  reject_reason: string | null;
  proof: string | null;
  remark: string | null;
  created_at: string;
  updated_at: string;
} 