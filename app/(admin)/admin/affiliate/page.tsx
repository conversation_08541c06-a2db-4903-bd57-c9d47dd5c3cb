"use client"
import React, { useRef, useState } from 'react';
import { Page<PERSON>ontainer, ProTable } from '@ant-design/pro-components';
import type { ActionType, ProColumnType } from '@ant-design/pro-components';
import { Badge, Button, Descriptions, Form, Input, message, Modal, Popconfirm, Space, Tag, Upload } from 'antd';
import Link from 'next/link';
import { getWithdrawList, approveWithdraw, rejectWithdraw, completeWithdraw } from "./api";
import { WithdrawRecord } from './data';
import { useRouter } from 'next/navigation';
import { UploadOutlined } from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';

export default function Page() {
  const router = useRouter();
  const actionRef = useRef<ActionType>();
  const [rejectModalVisible, setRejectModalVisible] = useState(false);
  const [approveModalVisible, setApproveModalVisible] = useState(false);
  const [completeModalVisible, setCompleteModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<WithdrawRecord | null>(null);
  const [rejectForm] = Form.useForm();
  const [completeForm] = Form.useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);

  const handleApprove = async () => {
    try {
      if (!currentRecord) return;
      
      await approveWithdraw(currentRecord.id);
      message.success('审核通过成功');
      setApproveModalVisible(false);
      actionRef.current?.reload();
    } catch (error: any) {
      message.error(error.message || '操作失败');
    }
  };

  const handleReject = async () => {
    try {
      const values = await rejectForm.validateFields();
      if (currentRecord) {
        await rejectWithdraw(currentRecord.id, values.reject_reason);
        message.success('拒绝提现成功');
        setRejectModalVisible(false);
        rejectForm.resetFields();
        actionRef.current?.reload();
      }
    } catch (error: any) {
      message.error(error.message || '操作失败');
    }
  };

  const handleComplete = async () => {
    try {
      if (!currentRecord) return;
      
      const values = await completeForm.validateFields();
      const payload: { proof?: string, remark?: string } = {};
      
      if (values.remark) {
        payload.remark = values.remark;
      }
      
      if (fileList.length > 0 && fileList[0].response) {
        payload.proof = fileList[0].response.url;
      }
      
      await completeWithdraw(currentRecord.id, payload);
      message.success('标记完成成功');
      setCompleteModalVisible(false);
      completeForm.resetFields();
      setFileList([]);
      actionRef.current?.reload();
    } catch (error: any) {
      message.error(error.message || '操作失败');
    }
  };

  const openRejectModal = (record: WithdrawRecord) => {
    setCurrentRecord(record);
    setRejectModalVisible(true);
  };

  const openApproveModal = (record: WithdrawRecord) => {
    setCurrentRecord(record);
    setApproveModalVisible(true);
  };

  const openCompleteModal = (record: WithdrawRecord) => {
    setCurrentRecord(record);
    setCompleteModalVisible(true);
    
    // 如果有凭证，添加到文件列表
    if (record.proof) {
      setFileList([
        {
          uid: '-1',
          name: '打款凭证',
          status: 'done',
          url: record.proof,
        },
      ]);
    } else {
      setFileList([]);
    }
  };

  const openDetailModal = (record: WithdrawRecord) => {
    setCurrentRecord(record);
    setDetailModalVisible(true);
  };

  const uploadProps: UploadProps = {
    action: '/api/upload', // 替换为实际的上传API
    listType: 'picture',
    maxCount: 1,
    fileList,
    onChange({ fileList }) {
      setFileList(fileList);
    },
  };

  const columns: ProColumnType<WithdrawRecord>[] = [
    {
      title: "ID",
      dataIndex: "id",
      width: 60,
      search: true,
    },
    {
      title: "用户ID",
      dataIndex: "user_id",
      width: 80,
      search: true,
      render: (_, record) => (
        <Link href={`/admin/user/${record.user_id}`}>{record.user_id}</Link>
      ),
    },
    {
      title: "申请时间",
      dataIndex: "created_at",
      width: 170,
      sorter: true,
      search: false,
    },
    {
      title: "提现金额",
      dataIndex: "amount",
      width: 100,
      search: false,
      render: (_, record) => `￥${record.amount.toFixed(2)}`,
    },
    {
      title: "手续费",
      dataIndex: "fee",
      width: 80,
      search: false,
      render: (_, record) => `￥${record.fee.toFixed(2)}`,
    },
    {
      title: "实际到账",
      dataIndex: "actual_amount",
      width: 100,
      search: false,
      render: (_, record) => `￥${record.actual_amount.toFixed(2)}`,
    },
    {
      title: "提现方式",
      dataIndex: "withdraw_type",
      width: 100,
      valueEnum: {
        alipay: '支付宝',
        wallet: '钱包余额'
      },
      search: false,
      render: (_, record) => (
        record.withdraw_type === "alipay" ? "支付宝" : "钱包余额"
      ),
    },
    {
      title: "支付宝账号",
      dataIndex: "account_no",
      width: 150,
      search: false,
      render: (_, record) => (
        <div>
          {record.account_name && <div>姓名: {record.account_name}</div>}
          {record.account_no && <div>账号: {record.account_no}</div>}
        </div>
      ),
    },
    {
      title: "状态",
      dataIndex: "status",
      width: 100,
      valueEnum: {
        pending: '待审核',
        approved: '已审核',
        completed: '已完成',
        rejected: '已拒绝'
      },
      search: true,
      render: (_, record) => {
        if (record.status === "completed") return <Tag color="success">已完成</Tag>;
        if (record.status === "approved") return <Tag color="processing">已审核</Tag>;
        if (record.status === "rejected") return <Tag color="error">已拒绝</Tag>;
        return <Tag color="default">待审核</Tag>;
      },
    },
    {
      title: "拒绝原因",
      dataIndex: "reject_reason",
      ellipsis: true,
      width: 120,
      search: false,
      render: (_, record) => record.reject_reason || '-',
    },
    {
      title: "打款凭证",
      dataIndex: "proof",
      width: 100,
      search: false,
      render: (_, record) => record.proof ? (
        <a href={record.proof} target="_blank" rel="noopener noreferrer">查看凭证</a>
      ) : '-',
    },
    {
      title: "备注",
      dataIndex: "remark",
      ellipsis: true,
      width: 120,
      search: false,
      render: (_, record) => record.remark || '-',
    },
    {
      title: "操作",
      valueType: 'option',
      fixed: 'right',
      width: 180,
      search: false,
      render: (_, record) => {
        // 只有待审核状态可以进行审核操作
        if (record.status === 'pending') {
          return (
            <Space>
              <Button 
                type="primary" 
                size="small" 
                onClick={() => openDetailModal(record)}
              >
                查看详情
              </Button>
              <Button
                type="primary"
                size="small"
                onClick={() => openApproveModal(record)}
              >
                通过
              </Button>
              <Button 
                danger 
                size="small" 
                onClick={() => openRejectModal(record)}
              >
                拒绝
              </Button>
            </Space>
          );
        }
        
        // 已审核状态可以标记为已完成
        if (record.status === 'approved') {
          return (
            <Space>
              <Button 
                type="primary" 
                size="small" 
                onClick={() => openDetailModal(record)}
              >
                查看详情
              </Button>
              <Button
                type="primary"
                size="small"
                onClick={() => openCompleteModal(record)}
              >
                标记已打款
              </Button>
            </Space>
          );
        }
        
        return (
          <Button 
            type="primary" 
            size="small" 
            onClick={() => openDetailModal(record)}
          >
            查看详情
          </Button>
        );
      },
    },
  ];

  return (
    <PageContainer>
      <ProTable<WithdrawRecord>
        params={{
          orderBy: "id",
          orderType: "desc",
        }}
        search={{
        //   filterType: 'light',
          searchText: '搜索',
          resetText: '重置',
          optionRender: (searchConfig, formProps, dom) => [
            ...dom.reverse(),
          ],
        }}
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        scroll={{ x: 1500 }}
        request={async (params) => {
          try {
            return await getWithdrawList(params);
          } catch (error) {
            message.error('获取数据失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }
        }}
        toolBarRender={() => []}
      />

      {/* 查看详情弹窗 */}
      <Modal
        title="提现记录详情"
        open={detailModalVisible}
        footer={null}
        width={700}
        onCancel={() => setDetailModalVisible(false)}
      >
        {currentRecord && (
          <Descriptions bordered column={2}>
            <Descriptions.Item label="ID">{currentRecord.id}</Descriptions.Item>
            <Descriptions.Item label="用户ID">{currentRecord.user_id}</Descriptions.Item>
            <Descriptions.Item label="申请时间">{currentRecord.created_at}</Descriptions.Item>
            <Descriptions.Item label="提现金额">￥{currentRecord.amount.toFixed(2)}</Descriptions.Item>
            <Descriptions.Item label="手续费">￥{currentRecord.fee.toFixed(2)}</Descriptions.Item>
            <Descriptions.Item label="实际到账">￥{currentRecord.actual_amount.toFixed(2)}</Descriptions.Item>
            <Descriptions.Item label="提现方式">
              {currentRecord.withdraw_type === 'alipay' ? '支付宝' : '钱包余额'}
            </Descriptions.Item>
            <Descriptions.Item label="状态">
              {currentRecord.status === "completed" && <Tag color="success">已完成</Tag>}
              {currentRecord.status === "approved" && <Tag color="processing">已审核</Tag>}
              {currentRecord.status === "rejected" && <Tag color="error">已拒绝</Tag>}
              {currentRecord.status === "pending" && <Tag color="default">待审核</Tag>}
            </Descriptions.Item>
            <Descriptions.Item label="支付宝账号" span={2}>
              {currentRecord.account_name && <div>姓名: {currentRecord.account_name}</div>}
              {currentRecord.account_no && <div>账号: {currentRecord.account_no}</div>}
            </Descriptions.Item>
            {currentRecord.reject_reason && (
              <Descriptions.Item label="拒绝原因" span={2}>
                {currentRecord.reject_reason}
              </Descriptions.Item>
            )}
            {currentRecord.proof && (
              <Descriptions.Item label="打款凭证" span={2}>
                <a href={currentRecord.proof} target="_blank" rel="noopener noreferrer">
                  查看凭证
                </a>
              </Descriptions.Item>
            )}
            {currentRecord.remark && (
              <Descriptions.Item label="备注" span={2}>
                {currentRecord.remark}
              </Descriptions.Item>
            )}
          </Descriptions>
        )}
      </Modal>

      {/* 审核通过弹窗 */}
      <Modal
        title="审核通过提现申请"
        open={approveModalVisible}
        onOk={handleApprove}
        onCancel={() => setApproveModalVisible(false)}
      >
        <p>您确定要通过此提现申请吗？通过后需要手动打款给用户。</p>
        {currentRecord && (
          <div>
            <p>用户ID: {currentRecord.user_id}</p>
            <p>提现金额: ￥{currentRecord.amount.toFixed(2)}</p>
            <p>实际到账: ￥{currentRecord.actual_amount.toFixed(2)}</p>
            {currentRecord.withdraw_type === 'alipay' && (
              <div>
                <p>支付宝账户: {currentRecord.account_name}</p>
                <p>支付宝账号: {currentRecord.account_no}</p>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* 拒绝原因弹窗 */}
      <Modal
        title="拒绝提现"
        open={rejectModalVisible}
        onOk={handleReject}
        onCancel={() => {
          setRejectModalVisible(false);
          rejectForm.resetFields();
        }}
      >
        <Form form={rejectForm} layout="vertical">
          <Form.Item
            name="reject_reason"
            label="拒绝原因"
            rules={[{ required: true, message: '请输入拒绝原因' }]}
          >
            <Input.TextArea 
              rows={4} 
              placeholder="请输入拒绝原因，将会通知用户"
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 标记完成弹窗 */}
      <Modal
        title="标记提现已打款"
        open={completeModalVisible}
        onOk={handleComplete}
        onCancel={() => {
          setCompleteModalVisible(false);
          completeForm.resetFields();
          setFileList([]);
        }}
      >
        <Form form={completeForm} layout="vertical">
          <Form.Item name="proof" label="打款凭证">
            <Upload {...uploadProps}>
              <Button icon={<UploadOutlined />}>上传凭证</Button>
            </Upload>
          </Form.Item>
          <Form.Item name="remark" label="备注">
            <Input.TextArea rows={3} placeholder="可选备注信息" />
          </Form.Item>
        </Form>
      </Modal>
    </PageContainer>
  );
} 