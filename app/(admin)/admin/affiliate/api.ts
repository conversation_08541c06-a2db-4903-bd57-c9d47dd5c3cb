import axiosInstance from "@/lib/axios";

// 获取提现记录列表
export async function getWithdrawList(params: any) {
  return axiosInstance.get("/admin/user_referral_withdraw_record/index", { params });
}

// 获取提现记录详情
export async function getWithdrawDetail(id: number) {
  return axiosInstance.get(`/admin/user_referral_withdraw_record/${id}`);
}

// 审核通过提现申请
export async function approveWithdraw(id: number) {
  return axiosInstance.post(`/admin/user_referral_withdraw_record/approve/${id}`);
}

// 拒绝提现申请
export async function rejectWithdraw(id: number, reject_reason: string) {
   return axiosInstance.post(`/admin/user_referral_withdraw_record/reject/${id}`, { reject_reason });
}

// 标记提现为已完成（已打款）
export async function completeWithdraw(id: number, data: { proof?: string, remark?: string }) {
  return axiosInstance.post(`/admin/user_referral_withdraw_record/complete/${id}`, data);
} 