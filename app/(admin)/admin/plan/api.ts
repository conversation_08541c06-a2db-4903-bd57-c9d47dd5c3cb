import axiosInstance from "@/lib/axios";
import {ProFieldRequestData} from "@ant-design/pro-utils";

// @ts-ignore
export async function getPlanList(params: any) {
  return axiosInstance.get("/admin/plan/index", {params})
}

export async function readPlan(id: number) {
  return axiosInstance.get(`/admin/plan/read/${id}`)
}

export async function updatePlan(id: number, values: any) {
  return axiosInstance.put(`/admin/plan/update/${id}`, values)
}

export async function createPlan(values: any) {
  return axiosInstance.post(`/admin/plan/save`, values)
}

export async function deletePlan(ids: string) {
  return axiosInstance.delete(`/admin/plan/delete`, {
    data: {ids: ids}
  })
}

export async function updatePlanStatus(id: number, status: boolean) {
  return axiosInstance.post("/admin/plan/status", {
    id: id,
    status: status ? 'Y' : "N"
  })
}

export async function updatePlanVisible(id: number, status: boolean) {
  return axiosInstance.post("/admin/plan/visible", {
    id: id,
    visible: status ? 'Y' : "N"
  })
}


export async function getSelectPlan() {
  return axiosInstance.get("/admin/plan/select")
}

