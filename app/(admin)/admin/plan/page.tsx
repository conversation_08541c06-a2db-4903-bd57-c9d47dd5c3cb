"use client"

import { <PERSON><PERSON><PERSON><PERSON> } from "@ant-design/pro-layout";
import { ActionType, BetaSchemaForm, ModalForm, ProFormColumnsType, ProTable } from "@ant-design/pro-components";
import { ProColumnType } from "@ant-design/pro-table";
import { But<PERSON>, message, Popconfirm, Space, Switch } from "antd";
import { useEffect, useRef, useState } from "react";
import { Plan } from "@/app/(admin)/admin/plan/data";
import {
  createPlan,
  deletePlan,
  getPlanList,
  readPlan,
  updatePlan,
  updatePlanStatus,
  updatePlanVisible
} from "@/app/(admin)/admin/plan/api";

export default function Page() {
  const [current, setCurrent] = useState<Plan | null>()
  const actionRef = useRef<ActionType>()

  const [open, setOpen] = useState(false)

  const handleDelete = async (ids: string) => {
    try {
      await deletePlan(ids)
      message.success("删除成功")
      actionRef.current?.reload()
    } catch (e) {
      // @ts-ignore
      message.error(e?.message || '删除失败')
    }
  }

  const handleCloseOpen = () => {
    setOpen(false)
    setCurrent(null)
  }

  const columns: ProColumnType<Plan>[] = [
    {
      title: "ID",
      dataIndex: "id",
    },
    {
      title: "套餐名称",
      dataIndex: "name",
    },
    {
      title: "描述",
      dataIndex: "description",
      search: false,
    },
    {
      title: "每日请求限制",
      dataIndex: "daily_limit",
      search: false,
      renderText: (text) => {
        return text == -1 ? '不限' : text
      },
      tooltip: "免费接口请求次数",
    },
    {
      title: "接口申请次数",
      dataIndex: "api_limit",
      search: false,
      renderText: (text) => {
        return text == -1 ? '不限' : text
      },
      tooltip: "免费接口申请次数"
    },
    {
      title: "请求QPS",
      dataIndex: "request_qps",
      search: false,
      renderText: (text) => {
        return text == -1 ? '不限' : text
      }
    },
    {
      title: "月付价格",
      dataIndex: "price_month",
      search: false,
    },
    {
      title: "年付价格",
      dataIndex: "price_year",
      search: false,
    },
    {
      title: "状态",
      dataIndex: "status",
      valueType: "radio",
      valueEnum: {
        "Y": "启用",
        "N": "禁用",
      },
      render: function (text, record) {
        return <Switch value={record.status === 'Y'} onChange={async (v) => {
          try {
            await updatePlanStatus(record.id, v)
            message.success("更新成功")
            actionRef.current?.reload()
          } catch {
            message.error("更新失败")
          }
        }} />
      }
    },
    {
      title: "前台可见",
      dataIndex: "visible",
      valueType: "radio",
      valueEnum: {
        "Y": "启用",
        "N": "禁用",
      },
      render: function (text, record) {
        return <Switch value={record.visible === 'Y'} onChange={async (v) => {
          try {
            await updatePlanVisible(record.id, v)
            message.success("更新成功")
            actionRef.current?.reload()
          } catch {
            message.error("更新失败")
          }
        }} />
      }
    },
    {
      title: "创建时间",
      dataIndex: "created_at",
      valueType: 'dateRange',
      hidden: true,
    },
    {
      title: "更新时间",
      dataIndex: "updated_at",
      search: false,
    },
    {
      title: "操作",
      valueType: 'option',
      render: function (text, record) {
        return <Space>
          <Button type={'link'} size={'small'} onClick={() => {
            setCurrent(record)
            setTimeout(() => {
              setOpen(true)
            }, 100)
          }}>编辑</Button>
          <Popconfirm title={'确认删除吗？'} description={'删除后不可恢复'} onConfirm={async () => {
            await handleDelete([record.id].join(","))
          }}>
            <Button type={'link'} size={'small'} danger>删除</Button>
          </Popconfirm>
        </Space>
      },
      search: false,
    }
  ]

  const formColumns: ProFormColumnsType<Plan>[] = [
    {
      title: "套餐名称",
      dataIndex: "name",
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      title: "套餐描述",
      dataIndex: "description",
      formItemProps: {
        rules: [{ required: true }]
      }
    },
    {
      title: "每日请求次数",
      dataIndex: "daily_limit",
      formItemProps: {
        rules: [{ required: true }],

      },
      tooltip: "免费接口和会员接口每日可请求次数,-1为不限",
      valueType: "digit",
      fieldProps: {
        min: -1
      }
    },
    {
      title: "接口数量",
      dataIndex: "api_limit",
      formItemProps: {
        rules: [{ required: true }],
      },
      tooltip: "可以申请的免费接口和会员接口数量，-1为不限",
      valueType: "digit",
      fieldProps: {
        min: -1
      }
    },
    {
      title: "请求QPS",
      dataIndex: "request_qps",
      formItemProps: {
        rules: [{ required: true }],
      },
      tooltip: "每秒限制请求次数，-1为不限",
      valueType: "digit",
      fieldProps:{
        min:-1
      }
    },
    {
      title: "月付价格",
      dataIndex: "price_month",
      formItemProps: {
        rules: [{ required: true }]
      },
      valueType: "money"
    },
    {
      title: "年付价格",
      dataIndex: "price_year",
      formItemProps: {
        rules: [{ required: true }]
      },
      valueType: "money"
    },
    {
      title: "前台可见",
      dataIndex: "visible",
      formItemProps: {
        rules: [{ required: true }]
      },
      valueType: "radio",
      valueEnum: {
        Y: "可见",
        N: "隐藏"
      },
      initialValue: "Y"
    },
    {
      title: "状态",
      dataIndex: "status",
      formItemProps: {
        rules: [{ required: true }],
        required: true,
      },
      valueType: "radio",
      valueEnum: {
        Y: "启用",
        N: "禁用"
      },
      initialValue: "Y"
    },
  ]

  useEffect(() => {
    if (!open) {
      setCurrent(null)
    }
  }, [open])


  return <>
    <PageContainer>
      <ProTable<Plan> params={{
        orderBy: "id",
        orderType: "desc",
      }} actionRef={actionRef} rowKey={'id'} columns={columns} scroll={{ x: 750 }}
        request={async (params) => {
          return getPlanList(params);
        }}
        toolBarRender={() => [
          <Button type={'primary'} size={'small'} key={'add'} onClick={() => {
            setOpen(true)
          }}>添加套餐</Button>
        ]}
      >
      </ProTable>
      <ModalForm title={'会员套餐'} open={open} onOpenChange={setOpen} request={async () => {
        if (current) {
          return await readPlan(current?.id)
        }
        return {}
      }} onFinish={async (values) => {
        if (current) {
          try {
            await updatePlan(current?.id, values)
            message.success("更新成功")
            actionRef.current?.reload()
            handleCloseOpen()
          } catch (e) {
            //@ts-ignore
            message.error(e.message ?? '更新失败')
          }

        } else {
          try {
            await createPlan(values)
            message.success("创建成功")
            actionRef.current?.reload()
            handleCloseOpen()
          } catch (e) {
            //@ts-ignore
            message.error(e.message ?? '创建失败')
          }
        }
      }} modalProps={{
        destroyOnClose: true
      }}>
        <BetaSchemaForm<Plan>
          title={'会员套餐'} columns={formColumns} layoutType={'Embed'}
        //@ts-ignore
        ></BetaSchemaForm>
      </ModalForm>

    </PageContainer>
  </>
}