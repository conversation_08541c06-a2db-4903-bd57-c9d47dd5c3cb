import axiosInstance from "@/lib/axios";

// @ts-ignore
export async function getList(params: any) {
  return axiosInstance.get("/admin/socialite/index", {params})
}

export async function read(id: number) {
  return axiosInstance.get(`/admin/socialite/read/${id}`)
}

export async function update(id: number, values: any) {
  return axiosInstance.put(`/admin/socialite/update/${id}`, values)
}

export async function create(values: any) {
  return axiosInstance.post(`/admin/socialite/save`, values)
}

export async function destroy(ids: string) {
  return axiosInstance.delete(`/admin/socialite/delete`, {
    data: {ids: ids}
  })
}

export async function updateStatus(id: number, status: boolean) {
  return axiosInstance.post("/admin/socialite/status", {
    id: id,
    status: status ? 'Y' : "N"
  })
}
