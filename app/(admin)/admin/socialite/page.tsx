"use client"

import {<PERSON><PERSON><PERSON><PERSON>} from "@ant-design/pro-layout";
import {
  ActionType,
  ModalForm,
  ProFormItem, ProFormRadio, ProFormSelect,
  ProFormText, ProFormTextArea,
  ProTable
} from "@ant-design/pro-components";
import {ProColumnType} from "@ant-design/pro-table";
import {Button, message, Popconfirm, Space, Switch} from "antd";
import {useEffect, useRef, useState} from "react";
import {getDictData} from "@/app/(admin)/admin/dict/api";
import {SocialiteConfig} from "@/app/(admin)/admin/socialite/data";
import {create, destroy, getList, read, update, updateStatus} from "@/app/(admin)/admin/socialite/api";
import {ICONFONT} from "@/components/antd/icon";

export default function Page() {
  const [current, setCurrent] = useState<SocialiteConfig | null>()
  const actionRef = useRef<ActionType>()

  const [open, setOpen] = useState(false)

  const handleDelete = async (ids: string) => {
    try {
      await destroy(ids)
      message.success("删除成功")
      actionRef.current?.reload()
    } catch (e) {
      // @ts-ignore
      message.error(e?.message || '删除失败')
    }
  }

  const handleCloseOpen = () => {
    setOpen(false)
    setCurrent(null)
  }

  const columns: ProColumnType<SocialiteConfig>[] = [
    {
      dataIndex: "id",
      title: "编号"
    },
    {
      dataIndex: "name",
      title: "名称"
    },
    {
      dataIndex: "icon",
      title: "图标",
      render: function (_, e) {
        return <>
          {e.icon.includes("http") ? <img src={e.icon} title={e.name} className={'w-[24px] h-[24px]'}/>  : <ICONFONT title={e.name} className={'w-[24px] h-[24px]'} size={24} type={e.icon}/>}
        </>
      }
    },
    {
      dataIndex: "provider",
      title: "第三方平台",
      valueType: "select",
      request: async () => await getDictData('third_provider')
    },
    {
      title: "状态",
      dataIndex: "status",
      valueType: "radio",
      valueEnum: {
        "Y": "启用",
        "N": "禁用",
      },
      render: function (text, record) {
        return <Switch value={record.status === 'Y'} onChange={async (v) => {
          try {
            await updateStatus(record.id, v)
            message.success("更新成功")
            actionRef.current?.reload()
          } catch {
            message.error("更新失败")
          }
        }}/>
      }
    },
    {
      title: "创建时间",
      dataIndex: "created_at",
      valueType: 'dateRange',
    },
    {
      title: "操作",
      valueType: 'option',
      render: function (text, record) {
        return <Space>
          <Button type={'link'} size={'small'} onClick={() => {
            setCurrent(record)
            setTimeout(() => {
              setOpen(true)
            }, 100)
          }}>编辑</Button>
          <Popconfirm title={'确认删除吗？'} description={'删除后不可恢复'} onConfirm={async () => {
            await handleDelete([record.id].join(","))
          }}>
            <Button type={'link'} size={'small'} danger>删除</Button>
          </Popconfirm>
        </Space>
      },
      search: false,
    }
  ]


  useEffect(() => {
    if (!open) {
      setCurrent(null)
    }
  }, [open])


  return <>
    <PageContainer>
      <ProTable<SocialiteConfig> params={{
        orderBy: "id",
        orderType: "desc",
      }} search={false}  actionRef={actionRef} rowKey={'id'} columns={columns} scroll={{x: 750}}
                                 request={async (params) => {
                                   return getList(params);
                                 }}
                                 toolBarRender={() => [
                                   <Button type={'primary'} size={'small'} key={'add'} onClick={() => {
                                     setOpen(true)
                                   }}>添加配置</Button>
                                 ]}
      >
      </ProTable>
      <ModalForm title={'第三方登录'} open={open} onOpenChange={setOpen} request={async () => {
        if (current) {
          const data = await read(current?.id || 0)
          //@ts-ignore
          return data;
        }
        return {}
      }} onFinish={async (values) => {
        if (current) {
          try {
            await update(current?.id || 0, values)
            message.success("更新成功")
            actionRef.current?.reload()
            handleCloseOpen()
          } catch (e) {
            //@ts-ignore
            message.error(e.message ?? '更新失败')
          }

        } else {
          try {
            await create(values)
            message.success("创建成功")
            actionRef.current?.reload()
            handleCloseOpen()
          } catch (e) {
            //@ts-ignore
            message.error(e.message ?? '创建失败')
          }
        }
      }} modalProps={{
        destroyOnClose: true
      }}>

        <ProFormText label={'名称'} name={'name'} rules={[{required: true}]}/>
        <ProFormText label={'图标'} help={'支持 iconfont 和 http 链接'} name={'icon'} rules={[{required: true}]}/>
        <ProFormSelect label={'第三方平台'} name={'provider'} request={async ()=>getDictData('third_provider')} rules={[{required: true}]}/>
        <ProFormText label={'client_id'} name={'client_id'} rules={[{required: true}]}/>
        <ProFormText label={'client_secret'} name={'client_secret'} rules={[{required: true}]}/>
        <ProFormText label={'回调地址'} name={'redirect'} rules={[{required: true}]}/>
        <ProFormTextArea label={'扩展'} name={'extra'} rules={[{required: false}]}/>

        <ProFormRadio.Group label={'状态'} name={'status'} initialValue={'Y'}
          //@ts-ignore
                            request={async () => getDictData('status')}></ProFormRadio.Group>
      </ModalForm>

    </PageContainer>
  </>
}