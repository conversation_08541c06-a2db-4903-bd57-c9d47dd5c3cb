import axiosInstance from "@/lib/axios";

// @ts-ignore
export async function getList(params: any) {
  return axiosInstance.get("/admin/menu/index", {params})
}

export async function read(id: number) {
  return axiosInstance.get(`/admin/menu/read/${id}`)
}

export async function update(id: number, values: any) {
  return axiosInstance.put(`/admin/menu/update/${id}`, values)
}

export async function create(values: any) {
  return axiosInstance.post(`/admin/menu/save`, values)
}

export async function destroy(ids: string) {
  return axiosInstance.delete(`/admin/menu/delete`, {
    data: {ids: ids}
  })
}

export async function updateStatus(id: number, status: boolean) {
  return axiosInstance.post("/admin/menu/status", {
    id: id,
    status: status ? 'Y' : "N"
  })
}
export async function numberOperation(id: number, number: number) {
  return axiosInstance.put("/admin/menu/numberOperation", {
    numberName: "sort",
    numberValue: number,
    id: id,
  })
}
