"use client"

import { <PERSON><PERSON><PERSON><PERSON> } from "@ant-design/pro-layout";
import {
  ActionType,
  ModalForm,
  ProFormDigit,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProTable
} from "@ant-design/pro-components";
import { ProColumnType } from "@ant-design/pro-table";
import { Button, InputNumber, message, Popconfirm, Space, Switch } from "antd";
import { useEffect, useRef, useState } from "react";
import { create, destroy, getList, numberOperation, read, update, updateStatus } from "@/app/(admin)/admin/menu/api";
import { Menu } from "@/app/(admin)/admin/menu/data";
import { getDictData } from "@/app/(admin)/admin/dict/api";

export default function Page() {
  const [current, setCurrent] = useState<Menu | null>()
  const actionRef = useRef<ActionType>()

  const [open, setOpen] = useState(false)

  const handleDelete = async (ids: string) => {
    try {
      await destroy(ids)
      message.success("删除成功")
      actionRef.current?.reload()
    } catch (e) {
      // @ts-ignore
      message.error(e?.message || '删除失败')
    }
  }

  const handleCloseOpen = () => {
    setOpen(false)
    setCurrent(null)
  }

  const columns: ProColumnType<Menu>[] = [
    {
      dataIndex: "id",
      title: "编号",
      search: false,
    },
    {
      dataIndex: "name",
      title: "菜单名称",
      search: false,
    },
    {
      dataIndex: "link",
      title: "链接",
      search: false,

    },
    {
      dataIndex: "position",
      title: "菜单位置",
      request: async function () {
        return await getDictData('menu_position')
      },
      valueType: "radio"
    },
    {
      dataIndex: "sort",
      title: "排序",
      search: false,
      render: function (text, record) {
        return <>
          <InputNumber defaultValue={record.sort} onChange={async (v) => {
            try {
              await numberOperation(record.id, Number(v) ?? 1)
              message.success("更新成功")
              actionRef.current?.reload()
            } catch (e) {
              // @ts-ignore
              message.error(e.message || '更新失败')
            }
          }} />
        </>
      }
    },
    {
      title: "状态",
      dataIndex: "status",
      valueType: "radio",
      valueEnum: {
        "Y": "启用",
        "N": "禁用",
      },
      render: function (text, record) {
        return <Switch value={record.status === 'Y'} onChange={async (v) => {
          try {
            await updateStatus(record.id, v)
            message.success("更新成功")
            actionRef.current?.reload()
          } catch {
            message.error("更新失败")
          }
        }} />
      },
      search: false,
    },
    {
      title: "创建时间",
      dataIndex: "created_at",
      valueType: 'dateRange',
      search: false,
    },
    {
      title: "操作",
      valueType: 'option',
      render: function (text, record) {
        return <Space>
          <Button type={'link'} size={'small'} onClick={() => {
            setCurrent(record)
            setTimeout(() => {
              setOpen(true)
            }, 100)
          }}>编辑</Button>
          <Popconfirm title={'确认删除吗？'} description={'删除后不可恢复'} onConfirm={async () => {
            await handleDelete([record.id].join(","))
          }}>
            <Button type={'link'} size={'small'} danger>删除</Button>
          </Popconfirm>
        </Space>
      },
      search: false,
    }
  ]


  useEffect(() => {
    if (!open) {
      setCurrent(null)
    }
  }, [open])

  const [activeKey, setActiveKey] = useState("top")

  useEffect(() => {
    actionRef.current?.reload()
  }, [activeKey])

  return <>
    <PageContainer>
      <ProTable<Menu> params={{
        orderBy: "id",
        orderType: "desc",
      }} search={false} toolbar={{
        multipleLine: true,
        tabs: {
          activeKey,
          onChange: (key) => setActiveKey(key as string),
          items: [
            {
              key: 'top',
              tab: '顶部菜单',
            },
            {
              key: 'bottom',
              tab: '底部菜单',
            },
            {
              key: 'link_exchange',
              tab: '友情链接',
            },
          ],
        }
      }} pagination={false} actionRef={actionRef} rowKey={'id'} columns={columns} scroll={{ x: 750 }}
        request={async (params) => {
          try {
            const list = await getList({ position: activeKey });
            return {
              data: list,
              success: true
            }
          } catch {
            return { success: false }
          }
        }}
        toolBarRender={() => [
          <Button type={'primary'} size={'small'} key={'add'} onClick={() => {
            setOpen(true)
          }}>添加菜单</Button>
        ]}
      >
      </ProTable>
      <ModalForm title={'页面管理'} open={open} onOpenChange={setOpen} request={async () => {
        if (current) {
          //@ts-ignore
          return await read(current?.id || 0);
        }
        return {}
      }} onFinish={async (values) => {
        if (current) {
          try {
            await update(current?.id || 0, values)
            message.success("更新成功")
            actionRef.current?.reload()
            handleCloseOpen()
          } catch (e) {
            //@ts-ignore
            message.error(e.message ?? '更新失败')
          }

        } else {
          try {
            await create(values)
            message.success("创建成功")
            actionRef.current?.reload()
            handleCloseOpen()
          } catch (e) {
            //@ts-ignore
            message.error(e.message ?? '创建失败')
          }
        }
      }} modalProps={{
        destroyOnClose: true
      }}>

        <ProFormText label={'菜单名称'} name={'name'} rules={[{ required: true }]} />
        <ProFormSelect label={'上级菜单'} name={'pid'} fieldProps={{
          fieldNames: {
            label: "name",
            value: "id",
          }
        }} request={async () => {
          try {
            return getList({ position: activeKey })
          } catch {
            return {}
          }
        }}></ProFormSelect>
        <ProFormText label={'菜单链接'} name={'link'} />
        <ProFormDigit label={'排序'} name={'sort'} />
        <ProFormRadio.Group label={'菜单位置'} name={'position'} initialValue={activeKey}
          //@ts-ignore
          request={async () => getDictData('menu_position')}></ProFormRadio.Group>
        <ProFormRadio.Group label={'状态'} name={'status'} initialValue={'Y'}
          //@ts-ignore
          request={async () => getDictData('status')}></ProFormRadio.Group>
      </ModalForm>

    </PageContainer>
  </>
}