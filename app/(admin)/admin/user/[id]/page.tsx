"use client"

import { ProCard, ProDescriptions, ProDescriptionsActionType } from "@ant-design/pro-components";
import { Button, message, Typography } from "antd";
import { readUser, updateUser } from "@/app/(admin)/admin/user/api";
import { useRef } from "react";
import { useRouter } from "next/navigation";
import { User } from "@/app/(admin)/admin/user/data";
import OrderTable from "@/app/(admin)/admin/user/components/OrderTable";
import LogTable from "@/app/(admin)/admin/user/components/LogTable";
import UserApiTable from "@/app/(admin)/admin/user/components/UserApiTable";
import ReferralSettingCard from "@/app/(admin)/admin/user/components/ReferralSettingCard";

export default function Page({ params }: { params: { id: number } }) {
  const router = useRouter()
  const descriptionColumns = [
    {
      title: "用户ID",
      dataIndex: "id",
      copyable: true,
      editable: false,

    },
    {
      title: "用户名",
      dataIndex: "username",
      copyable: true,
      editable: false,
    },
    {
      title: "昵称",
      dataIndex: "nickname",
      editable: false,
    },
    {
      title: "手机号",
      dataIndex: "phone",
      copyable: true,
      editable: false,

    },
    {
      title: "邮箱",
      dataIndex: "email",
      copyable: true,
      editable: false,

    },
    {
      title: "会员套餐",
      dataIndex: "plan",
      render: (_: any, record: User) => {
        return <>{record.plan.name}</>
      },
      editable: false,

    },
    {
      title: "套餐到期时间",
      dataIndex: "plan_end_at",
      editable: false,

    },
    {
      title: "状态",
      dataIndex: "status",
      valueEnum: {
        Y: "正常",
        N: "禁用"
      },
      valueType: "radio",
    }
  ];

  const actionRef = useRef<ProDescriptionsActionType>();

  return <>
    <div className={'mb-5'}>
      <ProCard extra={<>
        <Button type={'link'} onClick={() => {
          actionRef.current?.reload()
        }}>刷新</Button>
        <Button type={'link'} onClick={() => {
          router.back()
        }}>返回</Button>
      </>}>
        <Typography.Title level={5}>基础信息</Typography.Title>
        <div>
          <ProDescriptions actionRef={actionRef}
            //@ts-ignore
            columns={descriptionColumns} editable={{
              onSave: async function (key, record) {
                try {
                  //@ts-ignore
                  await updateUser(record.id, { key: record[key] })
                  message.success("更新成功")
                } catch (e) {
                  //@ts-ignore
                  message.error(e.message)
                }
              }
            }}
            //@ts-ignore
            request={async () => {
              try {
                const user = await readUser(params.id)
                return {
                  data: user,
                  success: true,
                }
              } catch (e) {
                //@ts-ignore
                message.error(e.message)
                router.back()
              }

            }}>

          </ProDescriptions>
        </div>
      </ProCard>
    </div>
    <div>
      <ProCard
        tabs={{
          type: 'card',
          items: [
            {
              label: '订单记录',
              key: 'orders',
              children: <OrderTable userId={params.id} />
            },
            {
              label: '请求日志',
              key: 'logs',
              children: <LogTable userId={params.id} />
            },
            {
              label: '已申请接口',
              key: 'apis',
              children: <UserApiTable userId={params.id} />
            },
            {
              label: '邀请设置',
              key: 'referral',
              children: <ReferralSettingCard userId={params.id} />
            }
          ]
        }}>
        <Typography.Title level={5}>附加信息</Typography.Title>
      </ProCard>
    </div>

  </>
}