"use client"

import {ActionType, BetaSchemaForm, ModalForm, ProFormColumnsType, ProTable} from "@ant-design/pro-components";
import {useRef, useState} from "react";
import {User} from "@/app/(admin)/admin/user/data";
import {ProColumnType} from "@ant-design/pro-table";
import {Button, message, Popconfirm, Space, Switch, Typography} from "antd";
import {
  createUser,
  generateUserToken,
  getUserList,
  readUser,
  updateUser,
  updateUserStatus
} from "@/app/(admin)/admin/user/api";
import {getSelectPlan} from "@/app/(admin)/admin/plan/api";
import {getDictData} from "@/app/(admin)/admin/dict/api";
import Link from "next/link";
import {useSearchParams} from "next/navigation";

export default function Page() {
  const [current, setCurrent] = useState<User | null>()
  const actionRef = useRef<ActionType>()
  const searchParams = useSearchParams()


  const columns: ProColumnType<User>[] = [
    {
      title: "ID",
      dataIndex: "id",
    },
    {
      title: "用户名",
      dataIndex: "username",
      initialValue: searchParams.get("username") || ''
    },
    {
      title: "手机号",
      dataIndex: "phone"
    },
    {
      title: "邮箱",
      dataIndex: "email"
    },
    {
      title: "会员套餐",
      dataIndex: "plan_id",
      //@ts-ignore
      request: async () => getSelectPlan(),
      render: function (dom, record) {
        return <>{record.plan?.name}</>
      }
    },
    {
      title: "套餐到期时间",
      dataIndex: "plan_end_at",
      valueType: "dateTime",
      render: function (dom, record) {
        return <>{record.plan_end_at || '-'}</>
      },
      search: false,
    },
    {
      title: "状态",
      dataIndex: "status",
      valueType: "radio",
      valueEnum: {
        "Y": "启用",
        "N": "禁用",
      },
      render: function (text, record) {
        return <Switch value={record.status === 'Y'} onChange={async (v) => {
          try {
            await updateUserStatus(record.id, v)
            message.success("更新成功")
            actionRef.current?.reload()
          } catch {
            message.error("更新失败")
          }
        }}/>
      }
    },
    {
      title: "创建时间",
      dataIndex: "created_at",
      search: false,
    },
    {
      title: "操作",
      valueType: 'option',
      render: function (text, record) {
        return <Space>
          <Button type={'link'} size={'small'} onClick={() => {
            setCurrent(record)
            setTimeout(() => {
              setOpen(true)
            }, 100)
          }}>编辑</Button>
          <Link href={`/admin/user/${record.id}`}>
            <Button type={'link'} size={'small'}>详情</Button>
          </Link>
        </Space>
      },
      search: false,
    }
  ]

  // @ts-ignore
  const formColumns: ProFormColumnsType<User>[] = [
    {
      title: "用户名",
      dataIndex: "username",
      formItemProps: {
        rules: [
          {required: true},
        ]
      }
    },
    {
      title: "密码",
      dataIndex: "password",
      formItemProps: {},
      valueType: "password"
    },
    {
      title: "手机号",
      dataIndex: "phone",
      formItemProps: {
        rules: [
          {required: true},
        ]
      },
    },
    {
      title: "邮箱",
      dataIndex: "email",
      formItemProps: {
        rules: [
          {required: false},
        ]
      },
    },
    {
      title: "套餐",
      dataIndex: "plan_id",
      formItemProps: {
        rules: [
          {required: false},
        ]
      },
      //@ts-ignore
      request: async () => getSelectPlan(),
      valueType: "select",
    },
    {
      title: "套餐到期时间",
      dataIndex: "plan_end_at",
      valueType: "dateTime",
      formItemProps: {
        rules: [
          {required: false},
        ]
      },
    },
    {
      title: "状态",
      dataIndex: "status",
      valueType: "radio",
      //@ts-ignore
      request: async () => getDictData("status"),
    }
  ]

  const [open, setOpen] = useState(false)
  const handleCloseOpen = () => {
    setOpen(false)
    setCurrent(null)
  }

  return <>
    <ProTable<User> params={{
        orderBy: "id",
        orderType: "desc"
      }} columns={columns} request={(params) => {
      return getUserList(params)
    }}
                    toolBarRender={() => [
                      <Button key={'add'} onClick={() => setOpen(true)} size={'small'}
                              type={'primary'}>新建用户</Button>
                    ]}
                    form={{}}></ProTable>
    <ModalForm title={(current ? '编辑' : "创建") + "用户"} open={open} onOpenChange={(v) => {
      if (!v) {
        setCurrent(null)
        setOpen(false)
      }
    }} request={async () => {
      if (current) {
        return await readUser(current?.id)
      }
      return {}
    }} onFinish={async (values) => {
      if (current) {
        try {
          await updateUser(current?.id, values)
          message.success("更新成功")
          actionRef.current?.reload()
          handleCloseOpen()
        } catch (e) {
          //@ts-ignore
          message.error(e.message ?? '更新失败')
        }
      } else {
        try {
          await createUser(values)
          message.success("创建成功")
          actionRef.current?.reload()
          handleCloseOpen()
        } catch (e) {
          //@ts-ignore
          message.error(e.message ?? '创建失败')
        }
      }
    }} modalProps={{
      destroyOnClose: true
    }}>
      <BetaSchemaForm columns={formColumns} layoutType={'Embed'}/>
    </ModalForm>
  </>
}