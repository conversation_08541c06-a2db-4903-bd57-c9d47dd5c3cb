import { AffiliateInfo } from "@/app/(dashboard)/dashboard/affiliate/index/components/types";
import axiosInstance from "@/lib/axios";

// @ts-ignore
export async function getUserList(params: any) {
  return axiosInstance.get("/admin/user/index", { params })
}

export async function readUser(id: number) {
  return axiosInstance.get(`/admin/user/read/${id}`)
}

export async function updateUser(id: number, values: any) {
  return axiosInstance.put(`/admin/user/update/${id}`, values)
}

export async function createUser(values: any) {
  return axiosInstance.post(`/admin/user/save`, values)
}

export async function deleteUser(ids: string) {
  return axiosInstance.delete(`/admin/user/delete`, {
    data: { ids: ids }
  })
}

export async function updateUserStatus(id: number, status: boolean) {
  return axiosInstance.post("/admin/user/status", {
    id: id,
    status: status ? 'Y' : "N"
  })
}

export async function generateUserToken(id: number) {
  return axiosInstance.post(`/admin/user/resetToken/${id}`,)
}

export async function getUserApis(params: any) {
  return axiosInstance.get(`/admin/user/apis`, { params })
}


export async function getUserReferralSetting(id: number): Promise<AffiliateInfo> {
  return axiosInstance.get(`/admin/user/user_referral_setting/${id}`)
}

export async function getUserReferralUserList(params:any) {
  return axiosInstance.get("/admin/user/user_referral_user_list", params)
}