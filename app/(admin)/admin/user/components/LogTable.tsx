"use client"

import { ProTable } from "@ant-design/pro-table";
import { getApiLogs } from "@/app/(admin)/admin/data/api_request_log/api";
import { getApiList } from "@/app/(admin)/admin/api/api";
import { useState, useEffect } from "react";
import Link from "next/link";
import { message } from "antd";

interface LogTableProps {
  userId: number;
}

const LogTable: React.FC<LogTableProps> = ({ userId }) => {
  const [apiEnum, setApiEnum] = useState<Record<string, string>>({});

  // 加载接口列表
  const loadApis = async () => {
    try {
      const data = await getApiList({ pageSize: 1000 });
      const temp: Record<string, string> = {};
      for (const item of data.data) {
        temp[item.id] = item.name;
      }
      setApiEnum(temp);
    } catch (e) {
      message.error((e as Error).message);
    }
  };

  useEffect(() => {
    loadApis();
  }, []);

  // 请求日志列定义
  const logColumns = [
    {
      title: "ID",
      dataIndex: "log_id",
    },
    {
      title: "接口",
      dataIndex: "api_id",
      align: "center" as const,
      valueType: "select",
      valueEnum: apiEnum,
      fieldProps: {
        showSearch: true,
        maxCount: 1,
        mode: "tags"
      },
      search: {
        transform: function (value: any, namePath: string, allValues: Record<string, any>) {
          if (value) {
            allValues[namePath] = value[0];
          }
          return allValues;
        }
      },
      render: function (dom: React.ReactNode, entity: any) {
        return <Link href={`/api/${entity.api_id}/api_document`} target={"_blank"}>{dom}</Link>;
      }
    },
    {
      title: "请求IP",
      dataIndex: "client_ip",
      search: false,
    },
    {
      title: "状态码",
      dataIndex: "response_code",
      search: false,
    },
    {
      title: "计费",
      dataIndex: "usage_count",
      search: false,
    },
    {
      title: "耗时(毫秒)",
      dataIndex: "duration_ms",
      search: false,
    },
    {
      title: "请求时间",
      dataIndex: "request_timestamp",
      search: false,
    }
  ];

  return (
    <ProTable
      columns={logColumns}
      request={async (param) => {
        return await getApiLogs({
          ...param,
          user_id: userId
        });
      }}
      search={{
        filterType: 'light',
      }}
      pagination={{
        pageSize: 10
      }}
      dateFormatter="string"
    />
  );
};

export default LogTable; 