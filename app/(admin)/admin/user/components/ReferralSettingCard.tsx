"use client"

import { ProDescriptions } from "@ant-design/pro-components";
import { Typography, Card, Spin, Tag } from "antd";
import { useEffect, useState } from "react";
import { getUserReferralSetting } from "@/app/(admin)/admin/user/api";
import { AffiliateInfo } from "@/app/(dashboard)/dashboard/affiliate/index/components/types";

interface ReferralSettingCardProps {
  userId: number;
}

const ReferralSettingCard: React.FC<ReferralSettingCardProps> = ({ userId }) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [affiliateInfo, setAffiliateInfo] = useState<AffiliateInfo | null>(null);

  const loadReferralSetting = async () => {
    try {
      setLoading(true);
      const response = await getUserReferralSetting(userId);
      setAffiliateInfo(response);
    } catch (error) {
      console.error("加载用户邀请设置失败", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadReferralSetting();
  }, [userId]);

  if (loading) {
    return <Spin tip="加载中..." />;
  }

  if (!affiliateInfo) {
    return <Typography.Text type="secondary">未找到邀请设置信息</Typography.Text>;
  }

  return (
    <Card>
      <ProDescriptions
        title="邀请设置信息"
        column={2}
        tooltip="用户的推广计划设置和佣金信息"
        dataSource={affiliateInfo}
        columns={[
          {
            title: "是否开启邀请",
            dataIndex: "has_setting",
            valueEnum: {
              true: { text: "已开启", status: "Success" },
              false: { text: "未开启", status: "Default" }
            }
          },
          {
            title: "邀请链接",
            dataIndex: "invite_link",
            copyable: true,
            ellipsis: true,
            valueType: "text"
          },
          {
            title: "推荐码",
            dataIndex: ["setting", "code"],
            copyable: true,
            ellipsis: true,
            render: (_, record) => {
              return record.setting ? record.setting.code : "未设置";
            }
          },
          {
            title: "奖励类型",
            dataIndex: ["setting", "reward_type"],
            render: (_, record) => {
              if (!record.setting) return "未设置";
              return record.setting.reward_type === "amount" ? 
                <Tag color="green">金额奖励</Tag> : 
                <Tag color="blue">会员奖励</Tag>;
            }
          },
          {
            title: "奖励内容",
            dataIndex: ["setting", "reward_value"],
            render: (_, record) => {
              if (!record.setting) return "未设置";
              if (record.setting.reward_type === "amount") {
                return `订单金额的 ${record.setting.amount_percentage}%`;
              } else {
                return `${record.setting.plan?.name || ''} ${record.setting.plan_days} 天`;
              }
            }
          },
          {
            title: "已使用次数",
            dataIndex: ["setting", "used_count"],
            render: (_, record) => {
              return record.setting ? record.setting.used_count : 0;
            }
          },
          {
            title: "自动领取会员",
            dataIndex: ["setting", "auto_claim_plan"],
            valueEnum: {
              true: { text: "是", status: "Success" },
              false: { text: "否", status: "Default" }
            },
            render: (_, record) => {
              if (!record.setting) return "未设置";
              return record.setting.auto_claim_plan ? 
                <Tag color="success">已开启</Tag> : 
                <Tag color="default">未开启</Tag>;
            }
          },
          {
            title: "可切换奖励",
            dataIndex: ["setting", "enable_switch"],
            render: (_, record) => {
              if (!record.setting) return "未设置";
              return record.setting.enable_switch ? 
                <Tag color="success">可切换</Tag> : 
                <Tag color="default">不可切换</Tag>;
            }
          },
          {
            title: "已切换",
            dataIndex: ["setting", "has_switched"],
            render: (_, record) => {
              if (!record.setting) return "未设置";
              return record.setting.has_switched ? 
                <Tag color="processing">是</Tag> : 
                <Tag color="default">否</Tag>;
            }
          },
          {
            title: "当前余额",
            dataIndex: ["balance", "balance"],
            render: (_, record) => {
              return record.balance ? `¥${record.balance.balance}` : "¥0";
            }
          },
          {
            title: "总奖励金额",
            dataIndex: ["balance", "total_reward"],
            render: (_, record) => {
              return record.balance ? `¥${record.balance.total_reward}` : "¥0";
            }
          },
          {
            title: "总提现金额",
            dataIndex: ["balance", "total_withdraw"],
            render: (_, record) => {
              return record.balance ? `¥${record.balance.total_withdraw}` : "¥0";
            }
          },
          {
            title: "冻结余额",
            dataIndex: ["balance", "frozen_balance"],
            render: (_, record) => {
              return record.balance ? `¥${record.balance.frozen_balance}` : "¥0";
            }
          },
          {
            title: "创建时间",
            dataIndex: ["setting", "created_at"],
            valueType: "dateTime",
            render: (_, record) => {
              return record.setting ? record.setting.created_at : "-";
            }
          }
        ]}
      />
    </Card>
  );
};

export default ReferralSettingCard; 