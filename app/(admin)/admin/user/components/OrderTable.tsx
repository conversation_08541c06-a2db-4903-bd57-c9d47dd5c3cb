"use client"

import { ProTable } from "@ant-design/pro-table";
import { getOrderList } from "@/app/(admin)/admin/order/api";

interface OrderTableProps {
  userId: number;
}

const OrderTable: React.FC<OrderTableProps> = ({ userId }) => {
  const orderColumns = [
    {
      title: "订单标题",
      dataIndex: "title",
    },
    {
      title: "订单编号",
      dataIndex: "trade_no",
    },
    {
      title: "订单金额",
      dataIndex: "price",
    },
    {
      title: "支付状态",
      dataIndex: "order_status",
      valueEnum: {
        0: "未支付",
        1: "已支付",
        2: "已取消",
        3: "已退款"
      }
    },
    {
      title: "创建时间",
      dataIndex: "created_at"
    }
  ];

  return (
    <ProTable
      columns={orderColumns}
      request={async (param) => {
        return await getOrderList({
          ...param,
          user_id: userId
        });
      }}
      search={false}
      pagination={{
        pageSize: 10
      }}
      dateFormatter="string"
    />
  );
};

export default OrderTable; 