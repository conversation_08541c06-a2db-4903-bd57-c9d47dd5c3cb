"use client"

import { ProTable } from "@ant-design/pro-table";
import { getUserApis } from "@/app/(admin)/admin/user/api";
import Link from "next/link";
import { useState } from "react";
import { Button, Space, Modal, Table } from "antd";

interface UserApiTableProps {
  userId: number;
}

interface ApiRecord {
  id: number;
  api_id: number;
  api: {
    id: number;
    name: string;
    type: number;
  };
  request: {
    can_use_num: number;
    daily_limit: number;
  };
  record: {
    can_use_num: number;
  };
  records: Array<{
    id: number;
    num: number;
    use_num: number;
    can_use_num: number;
    expire_time: string;
    created_at: string;
  }>;
  created_at: string;
}

const UserApiTable: React.FC<UserApiTableProps> = ({ userId }) => {
  const [recordModalOpen, setRecordModalOpen] = useState(false);
  const [recordData, setRecordData] = useState<Array<any>>([]);
  const [currentApi, setCurrentApi] = useState<ApiRecord | null>(null);

  return (
    <>
      <ProTable<ApiRecord>
        columns={[
          {
            title: "接口名称",
            dataIndex: "api_name",
            render: (_, record) => (
              <Link href={`/admin/api/${record.api_id}`}>{record.api?.name}</Link>
            )
          },
          {
            title: "接口类型",
            dataIndex: "type",
            valueEnum: {
              1: "免费接口",
              2: "会员接口",
              3: "计次接口"
            },
            valueType: 'select',
            render: (_, record) => {
              return record.api.type === 3 ? "计次接口" : record.api.type === 2 ? "会员接口" : "免费接口";
            }
          },
          {
            title: "剩余次数",
            dataIndex: "remain_count",
            render: (_, record) => {
              if (record.api.type === 3) {
                return <Space>
                  <span>{record.record.can_use_num || 0}次</span>
                  <Button
                    type="link"
                    size="small"
                    onClick={() => {
                      setCurrentApi(record);
                      setRecordData(record.records || []);
                      setRecordModalOpen(true);
                    }}
                  >
                    查看记录
                  </Button>
                </Space>;
              }
              return `${record.request.can_use_num === -1 ? '不限' : record.request.can_use_num || 0}/${record.request.daily_limit === -1 ? '不限' : record.request.daily_limit} 次`;
            },
            search: false
          },
          {
            title: "申请时间",
            dataIndex: "created_at",
            search: false
          }
        ]}
        request={async (param) => {
          // 修改分页参数名称
          const { current, api_name } = param;
          return await getUserApis({
            ...param,
            keyword: api_name,
            page: current,
            user_id: userId
          });
        }}
        search={{
          filterType: 'light',
          defaultCollapsed: false
        }}
        pagination={{
          pageSize: 10,
          showQuickJumper: true
        }}
        dateFormatter="string"
      />

      <Modal
        width={800}
        title={`${currentApi?.api?.name || ''} - 接口次数包`}
        footer={null}
        open={recordModalOpen}
        onCancel={() => setRecordModalOpen(false)}
      >
        <Table
          pagination={false}
          rowKey={'id'}
          dataSource={recordData}
          columns={[
            {
              dataIndex: "id",
              title: "编号",
            },
            {
              dataIndex: "num",
              title: "总次数",
            },
            {
              dataIndex: "use_num",
              title: "已用次数"
            },
            {
              dataIndex: "can_use_num",
              title: "剩余次数",
              render: (_, record: any) => {
                return record?.num - record?.use_num;
              }
            },
            {
              dataIndex: "expire_time",
              title: "过期时间",
              sorter: (a: any, b: any) =>
                (new Date(a.expire_time)).getTime() - (new Date(b.expire_time)).getTime()
            },
            {
              dataIndex: "created_at",
              title: "创建时间",
            }
          ]}
        />
      </Modal>
    </>
  );
};

export default UserApiTable; 