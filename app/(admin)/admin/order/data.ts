import {User} from "@/app/(admin)/admin/user/data";

export interface Order {
  id: number;
  user_id: number;
  title: string
  pay_method: string
  trade_no: string
  out_trade_no: string
  price: number;
  order_type: string
  orderable_type: string
  order_status: number;
  coupon_id: number;
  api?: OrderApi
  plan?: OrderPlan;
  user?: User
}

export interface OrderApi {
  id: number;
  api_id: number;
  user_id: number;
  duration: number;
  num: number;
}

export interface OrderPlan {
  id: number;
  plan_id: number;
  user_id: number;
  duration_type: string;
  duration: number;
  old_plan_id: number;
  pre_end_date: string;
  new_end_date: string;
}