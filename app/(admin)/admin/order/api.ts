// @ts-ignore
import axiosInstance from "@/lib/axios";

// @ts-ignore
export async function getOrderList(params: any) {
  return axiosInstance.get("/admin/order/index", {params})
}

export async function readOrder(id: number) {
  return axiosInstance.get(`/admin/order/read/${id}`)
}

export async function updateOrder(id: number, values: any) {
  return axiosInstance.put(`/admin/order/update/${id}`, values)
}

export async function createOrder(values: any) {
  return axiosInstance.put(`/admin/order/save`, values)
}

export async function deleteOrder(ids: string) {
  return axiosInstance.delete(`/admin/order/delete`, {
    data: {ids: ids}
  })
}

