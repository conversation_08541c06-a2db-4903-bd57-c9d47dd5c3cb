"use client"

import {<PERSON><PERSON><PERSON><PERSON>} from "@ant-design/pro-layout";
import {ActionType, ProTable} from "@ant-design/pro-components";
import {ProColumnType} from "@ant-design/pro-table";
import {Space} from "antd";
import {useRef} from "react";
import {Order} from "./data";
import {getDictData} from "@/app/(admin)/admin/dict/api";
import {getOrderList} from "@/app/(admin)/admin/order/api";
import Link from "next/link";


export default function Page() {
  const actionRef = useRef<ActionType>()

  const columns: ProColumnType<Order>[] = [
    {
      title: "id",
      dataIndex: "id",
      search: false,
    },
    {
      title: "用户",
      dataIndex: "user_id",
      render: (dom, entity) => {
        return <Link href={`/admin/user?username=${entity?.user?.username}`}>{entity.user?.username}</Link>
      }
    },
    {
      title: "订单标题",
      dataIndex: "title",
      search: false,
    },
    {
      title: "订单类型",
      dataIndex: "order_type",
      //@ts-ignore
      request: async () => getDictData('order_type'),
      valueType: "radioButton",
    },
    {
      title: "订单编号",
      dataIndex: "trade_no",
    },
    {
      title: "外部订单编号",
      dataIndex: "out_trade_no"
    },
    {
      title: "订单金额",
      dataIndex: "price",
      search: false
    },
    {
      title: "支付方式",
      dataIndex: "pay_method",
      //@ts-ignore
      request: async () => getDictData("pay_method"),
      valueType: "select",

    },
    {
      title: "支付状态",
      dataIndex: "order_status",
      //@ts-ignore

      request: async () => getDictData("order_status"),
      valueType: "select",
    },
    {
      title: "创建时间",
      dataIndex: "created_at",
      valueType: 'dateRange',
      hidden: true,
    },
    {
      title: "更新时间",
      dataIndex: "updated_at",
      search: false,
    },
    {
      title: "操作",
      valueType: 'option',
      render: function (text, record) {
        return <Space>
          {/* <Button type={'primary'} size={'small'} onClick={() => {
            setCurrent(record)
            setTimeout(() => {
              setOpen(true)
            }, 100)
          }}>编辑</Button>*/}
          {/* <Popconfirm title={'确认删除吗？'} description={'删除后不可恢复'} onConfirm={async () => {
            await handleDelete([record.id].join(","))
          }}>
            <Button size={'small'} danger>删除</Button>
          </Popconfirm>*/}
        </Space>
      },
      search: false,
    }
  ]


  return <>
    <PageContainer>
      <ProTable<Order> params={{
        orderBy: "id",
        orderType: "desc",
      }} actionRef={actionRef} rowKey={'id'} columns={columns} scroll={{x: 750}}
                       request={async (params) => {
                         return getOrderList(params);
                       }}
                       toolBarRender={() => []}
      >
      </ProTable>

    </PageContainer>
  </>
}