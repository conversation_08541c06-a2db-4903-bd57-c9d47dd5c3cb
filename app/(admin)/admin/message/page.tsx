"use client"

import {<PERSON><PERSON><PERSON><PERSON>} from "@ant-design/pro-layout";
import {
  ActionType,
  ModalForm, ProFormDependency,
  ProFormItem, ProFormRadio, ProFormSelect,
  ProFormText,
  ProTable
} from "@ant-design/pro-components";
import {ProColumnType} from "@ant-design/pro-table";
import {Button, message, Popconfirm, Space, Switch} from "antd";
import {useEffect, useRef, useState} from "react";
import {getDictData} from "@/app/(admin)/admin/dict/api";
import {Message} from "@/app/(admin)/admin/message/data";
import {create, destroy, getList, read, update, updateStatus} from "@/app/(admin)/admin/message/api";
import dynamic from "next/dynamic";
import {getUserList} from "@/app/(admin)/admin/user/api";
import {getSelectPlan} from "@/app/(admin)/admin/plan/api";

const Wangeditor = dynamic(() => import('@/components/common/wangeditor'), {ssr: false})

export default function Page() {
  const [current, setCurrent] = useState<Message | null>()
  const actionRef = useRef<ActionType>()

  const [open, setOpen] = useState(false)

  const handleDelete = async (ids: string) => {
    try {
      await destroy(ids)
      message.success("删除成功")
      actionRef.current?.reload()
    } catch (e) {
      // @ts-ignore
      message.error(e?.message || '删除失败')
    }
  }

  const handleCloseOpen = () => {
    setOpen(false)
    setCurrent(null)
  }

  const columns: ProColumnType<Message>[] = [
    {
      dataIndex: "id",
      title: "编号",
      search: false,
    },
    {
      dataIndex: "title",
      title: "标题",
      search: true,
    },
    {
      dataIndex: "message_type",
      title: "消息类型",
      search: true,
      valueType: "radio",
      request: async () => getDictData('message_type'),
    },
    {
      title: "创建时间",
      dataIndex: "created_at",
      search: false,
    },
    {
      title: "操作",
      valueType: 'option',
      render: function (text, record) {
        return <Space>
          <Button type={'link'} size={'small'} onClick={() => {
            setCurrent(record)
            setTimeout(() => {
              setOpen(true)
            }, 100)
          }}>编辑</Button>
          <Popconfirm title={'确认删除吗？'} description={'删除后不可恢复'} onConfirm={async () => {
            await handleDelete([record.id].join(","))
          }}>
            <Button type={'link'} size={'small'} danger>删除</Button>
          </Popconfirm>
        </Space>
      },
      search: false,
    }
  ]


  useEffect(() => {
    if (!open) {
      setCurrent(null)
    }
  }, [open])

  const [users, setUsers] = useState([])


  return <>
    <PageContainer>
      <ProTable<Message> params={{
        orderBy: "id",
        orderType: "desc",
      }} actionRef={actionRef} rowKey={'id'} columns={columns} scroll={{x: 750}}
                         request={async (params) => {
                           return getList(params);
                         }}
                         toolBarRender={() => [
                           <Button type={'primary'} size={'small'} key={'add'} onClick={() => {
                             setOpen(true)
                           }}>添加消息</Button>
                         ]}
      >
      </ProTable>
      <ModalForm title={'消息管理'} open={open} onOpenChange={setOpen} request={async () => {
        if (current) {
          const data = await read(current?.id || 0)
          //@ts-ignore
          return data;
        }
        return {}
      }} onFinish={async (values) => {
        if (current) {
          try {
            await update(current?.id || 0, values)
            message.success("更新成功")
            actionRef.current?.reload()
            handleCloseOpen()
          } catch (e) {
            //@ts-ignore
            message.error(e.message ?? '更新失败')
          }

        } else {
          try {
            await create(values)
            message.success("创建成功")
            actionRef.current?.reload()
            handleCloseOpen()
          } catch (e) {
            //@ts-ignore
            message.error(e.message ?? '创建失败')
          }
        }
      }} modalProps={{
        destroyOnClose: true
      }}>

        <ProFormText label={'消息标题'} name={'title'} rules={[{required: true}]}/>
        <ProFormRadio.Group label={'消息类型'} initialValue={'global'} name={'message_type'}
                            request={async () => getDictData('message_type')}/>
        <ProFormDependency name={['message_type']}>
          {({message_type}) => {
            return message_type != "global" &&
              <ProFormSelect mode={'multiple'} name={'user_ids'} options={users} label={'用户'} fieldProps={{
                showSearch: true,
                onSearch: async function (value) {
                  const data = await getUserList({name: value})
                  const temp = []
                  for (const t of data.data) {
                    temp.push({
                      label: t.username,
                      value: t.id
                    })
                  }
                  setUsers(temp)
                }
              }}/>
          }}
        </ProFormDependency>
        <ProFormItem name={'content'} label={'消息内容'}>
          <Wangeditor/>
        </ProFormItem>
      </ModalForm>

    </PageContainer>
  </>
}