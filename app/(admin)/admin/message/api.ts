import axiosInstance from "@/lib/axios";

// @ts-ignore
export async function getList(params: any) {
  return axiosInstance.get("/admin/message/index", {params})
}

export async function read(id: number) {
  return axiosInstance.get(`/admin/message/read/${id}`)
}

export async function update(id: number, values: any) {
  return axiosInstance.put(`/admin/message/update/${id}`, values)
}

export async function create(values: any) {
  return axiosInstance.post(`/admin/message/save`, values)
}

export async function destroy(ids: string) {
  return axiosInstance.delete(`/admin/message/delete`, {
    data: {ids: ids}
  })
}

export async function updateStatus(id: number, status: boolean) {
  return axiosInstance.post("/admin/message/status", {
    id: id,
    status: status ? 'Y' : "N"
  })
}
