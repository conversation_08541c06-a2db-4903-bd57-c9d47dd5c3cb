import axiosInstance from "@/lib/axios";

export async function getUserStatics() {
  return axiosInstance.get("/admin/workspace/user_statics")
}


export async function getOrderStatics() {
  return axiosInstance.get("/admin/workspace/order_statics")
}

export async function getTickets() {
  return axiosInstance.get("/admin/workspace/tickets")
}


export async function getUserRegChart(params) {
  return axiosInstance.get("/admin/workspace/user_reg_chart",{params})
}

export async function getOrder<PERSON>hart(params) {
  return axiosInstance.get("/admin/workspace/order_chart",{params})
}
