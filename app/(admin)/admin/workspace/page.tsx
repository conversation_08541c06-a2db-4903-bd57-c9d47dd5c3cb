"use client"
import {StatisticCard} from "@ant-design/pro-card";
import {DatePicker,  Empty, Statistic} from "antd";
import {Line<PERSON><PERSON>, Line, Legend} from "@visactor/react-vchart";
import React, {useEffect, useState} from "react";
import {ProCard} from "@ant-design/pro-components";


import customParseFormat from 'dayjs/plugin/customParseFormat';
import dayjs from "dayjs";
import {
  getOrderChart,
  getOrderStatics,
  getTickets,
  getUserRegChart,
  getUserStatics
} from "@/app/(admin)/admin/workspace/api";
import Link from "next/link";

dayjs.extend(customParseFormat);

const {RangePicker} = DatePicker;
const dateFormat = 'YYYY/MM/DD';


const UserChart = ({data}) => {
  return <div>
    {data.length>0? <LineChart data={{
      id: "user",
      values: data
    }}>
      <Line xField={'date'} yField={'count'} seriesField={'count_field'}></Line>
    </LineChart>:<Empty/>}
  </div>
}

const OrderChart = ({data}) => {
  return <div>
    {data.length>0 ?  <LineChart data={{
      id: "order",
      values: data
    }}>
      <Line xField={'date'} yField={'paid'} seriesField={'paid_field'}></Line>
      <Line xField={'date'} yField={'pending'} seriesField={'pending_field'}></Line>
      <Legend/>
    </LineChart> : <Empty/>}
  </div>
}


export default function Page() {
  const [tab, setTab] = useState('tab2');
  const [userStatics, setUserStatics] = useState({
    today_user: 0,
    total_user: 0,
    total_vip_user: 0,
  })
  const handlerLoadUserStatics = () => {
    getUserStatics().then((data) => {
      //@ts-ignore
      setUserStatics(data)
    }).catch(() => {
    })
  }

  const handlerLoadOrderStatics = () => {
    getOrderStatics().then((data) => {
      //@ts-ignore
      setOrderStatics(data)
    }).catch(() => {
    })
  }

  const handlerTickets = () => {
    getTickets().then((data) => {
      //@ts-ignore
      setTickets(data)
    }).catch(() => {
    })
  }


  const [orderStatics, setOrderStatics] = useState({
    today_count_order: 0,
    total_count_order: 0,
    today_order_amount: 0,
    total_order_amount: 0,
  })

  const [tickets, setTickets] = useState({
    wait: 0,
    reply: 0,
  })

  const [searchDate, setSearchDate] = useState({
    start_date: dayjs().startOf('month').format(dateFormat),
    end_date: dayjs().endOf('month').format(dateFormat)
  })
  const [userRegChartData, setUserRegChartData] = useState([])
  const [orderChartData, setOrderChartData] = useState([])

  const handleLoadUserRegChartData = () => {
    getUserRegChart(searchDate).then((data) => {
      //@ts-ignore
      setUserRegChartData(data)
    }).then(() => {

    })
  }

  const handleLoadOrderChartData = () => {
    getOrderChart(searchDate).then((data) => {
      //@ts-ignore
      setOrderChartData(data)
    }).then(() => {

    })
  }


  useEffect(() => {
    handlerLoadUserStatics()
    handlerLoadOrderStatics()
    handlerTickets()

  }, []);

  useEffect(() => {
    handleLoadUserRegChartData()
    handleLoadOrderChartData()
  }, [searchDate]);


  return <>
    <div className={'grid grid-cols-1 md:grid-cols-3 gap-5'}>
      <StatisticCard title={'用户统计'}>
        <div className={'grid grid-cols-2'}>
          <div>
            <Statistic title={'今日用户'} value={userStatics.today_user}/>
            <Statistic title={'总用户'} value={userStatics.total_user}/>
          </div>
          <div>
            <Statistic title={'会员用户'} value={userStatics.total_vip_user}/>
          </div>
        </div>
        {/*<div className={'flex gap-5'}>*/}
        {/*  <div className={'flex flex-col'}>*/}
        {/*    <Statistic title={'今日用户'} value={10}/>*/}
        {/*    <Statistic title={'总用户'} value={100000}/>*/}
        {/*  </div>*/}
        {/*  <div>*/}
        {/*    /!*<LineChart width={220} height={150} data={{*!/*/}
        {/*    /!*  id: "e",*!/*/}
        {/*    /!*  values: [{*!/*/}
        {/*    /!*    date: "2024-11-17",*!/*/}
        {/*    /!*    value: 1,*!/*/}
        {/*    /!*  }, {*!/*/}
        {/*    /!*    date: "2024-11-18",*!/*/}
        {/*    /!*    value: 2,*!/*/}
        {/*    /!*  },{*!/*/}
        {/*    /!*    date: "2024-11-19",*!/*/}
        {/*    /!*    value: 3,*!/*/}
        {/*    /!*  },{*!/*/}
        {/*    /!*    date: "2024-11-2-",*!/*/}
        {/*    /!*    value: 10,*!/*/}
        {/*    /!*  }]*!/*/}
        {/*    /!*}}>*!/*/}
        {/*    /!*  <Line xField={"date"} yField={"value"}></Line>*!/*/}
        {/*    /!*</LineChart>*!/*/}
        {/*  </div>*/}
        {/*</div>*/}

      </StatisticCard>
      <StatisticCard title={'订单'}>
        <div className={'grid grid-cols-2'}>
          <div>
            <Statistic title={'今日订单'} value={orderStatics.today_count_order}/>
            <Statistic title={'订单金额'} value={orderStatics.today_order_amount}/>
          </div>
          <div>
            <Statistic title={'本月订单'} value={orderStatics.total_count_order}/>
            <Statistic title={'订单金额'} value={orderStatics.total_order_amount}/>
          </div>
        </div>
      </StatisticCard>
      {/*<StatisticCard title={'接口'}>*/}
      {/*  <Statistic title={'今日申请接口'} value={10}/>*/}
      {/*  <Statistic title={'本月申请接口'} value={100000}/>*/}
      {/*</StatisticCard>*/}
      <StatisticCard title={'工单'}>
        <Link href={'/admin/ticket?status=1'}><Statistic title={'待分配工单'} value={tickets.wait}/></Link>
        <Link href={'/admin/ticket?status=3'}><Statistic title={'待回复工单'} value={tickets.reply}/></Link>
      </StatisticCard>
    </div>

    {/*<div className={'mt-5'}>*/}
    {/*  <div className={'grid grid-cols-1 md:grid-cols-4'}>*/}
    {/*    <ProCard>*/}
    {/*      <Statistic title="今日UV" value={79.0} precision={2}/>*/}
    {/*    </ProCard>*/}
    {/*    <ProCard>*/}
    {/*      <Statistic title="冻结金额" value={112893.0} precision={2}/>*/}
    {/*    </ProCard>*/}
    {/*    <ProCard>*/}
    {/*      <Statistic title="信息完整度" value={93} suffix="/ 100"/>*/}
    {/*    </ProCard>*/}
    {/*    <ProCard>*/}
    {/*      <Statistic title="冻结金额" value={112893.0}/>*/}
    {/*    </ProCard>*/}
    {/*  </div>*/}
    {/*</div>*/}
    <div className={'mt-5'}>
      <ProCard
        title={'图标统计'}
        extra={<div>
          <RangePicker
            onChange={(e, dates) => {
              if (dates) {
                setSearchDate({start_date: dates[0], end_date: dates[1]})
              }
            }}
            defaultValue={[dayjs(dayjs().startOf('month'), dateFormat), dayjs(dayjs().endOf('month'), dateFormat)]}
            format={dateFormat}
          />
        </div>}
        tabs={{
          activeKey: tab,
          items: [
            {
              label: `注册统计`,
              key: 'tab1',
              children: <UserChart data={userRegChartData}/>,
            },
            {
              label: `订单统计`,
              key: 'tab2',
              children: <OrderChart data={orderChartData}/>,
            },
          ],
          onChange: (key) => {
            setTab(key);
          }
        }}
      />
    </div>
  </>
}