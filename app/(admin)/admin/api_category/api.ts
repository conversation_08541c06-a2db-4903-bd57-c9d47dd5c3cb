import axiosInstance from "@/lib/axios";

// @ts-ignore
export async function getApiCategoryList(params: any) {
  return axiosInstance.get("/admin/api_category/index", {params})
}

export async function readApiCategory(id: number) {
  return axiosInstance.get(`/admin/api_category/read/${id}`)
}

export async function updateApiCategory(id: number, values: any) {
  return axiosInstance.put(`/admin/api_category/update/${id}`, values)
}

export async function createApiCategory(values: any) {
  return axiosInstance.post(`/admin/api_category/save`, values)
}

export async function deleteApiCategory(ids: string) {
  return axiosInstance.delete(`/admin/api_category/delete`, {
    data: {ids: ids}
  })
}

export async function getApiCategorySelect() {
  return await axiosInstance.get("/admin/api_category/select")
}


export async function numberOperationApiCategory(id: number, number: number) {
  return axiosInstance.put("/admin/api_category/numberOperation", {
    numberName: "sort",
    numberValue: number,
    id: id,
  })
}