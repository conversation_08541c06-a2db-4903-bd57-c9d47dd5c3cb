"use client"

import {<PERSON><PERSON><PERSON><PERSON>} from "@ant-design/pro-layout";
import {ActionType, BetaSchemaForm, ModalForm, ProFormColumnsType, ProTable} from "@ant-design/pro-components";
import {ApiCategory} from "@/app/(admin)/admin/api_category/data";
import {ProColumnType} from "@ant-design/pro-table";
import {Button, InputNumber, message, Popconfirm, Space} from "antd";
import {useEffect, useRef, useState} from "react";
import {
  createApiCategory, deleteApiCategory,
  getApiCategoryList, numberOperationApiCategory,
  readApiCategory,
  updateApiCategory
} from "@/app/(admin)/admin/api_category/api";
import {ICONFONT} from "@/components/antd/icon";

export default function Page() {
  const [current, setCurrent] = useState<ApiCategory | null>()
  const actionRef = useRef<ActionType>()

  const [open, setOpen] = useState(false)

  const handleDelete = async (ids: string) => {
    try {
      await deleteApiCategory(ids)
      message.success("删除成功")
      actionRef.current?.reload()
    } catch (e) {
      // @ts-ignore
      message.error(e?.message || '删除失败')
    }
  }

  const handleCloseOpen = () => {
    setOpen(false)
    setCurrent(null)
  }

  const columns: ProColumnType<ApiCategory>[] = [
    {
      title: "ID",
      dataIndex: "id",
    },
    {
      title: "分类名称",
      dataIndex: "name",
    },
    {
      title: "分类图标",
      dataIndex: "icon",
      search: false,
      render: function (text) {
        //@ts-ignore
        return text && text.includes('http') ? <img src={text} alt={text} className="w-5 h-5"/> : <ICONFONT style={{fontSize: '20px',height: '20px',width: '20px'}} type={text}/>
      }
    },
    {
      title: "排序",
      dataIndex: "sort",
      valueType: "digit",
      search: false,
      render: function (text, record) {
        return <>
          <InputNumber defaultValue={record.sort} onChange={async (v) => {
            try {
              await numberOperationApiCategory(record.id, v ?? 1)
              message.success("更新成功")
              actionRef.current?.reload()
            } catch (e) {
              // @ts-ignore
              message.error(e.message || '更新失败')
            }
          }}/>
        </>
      }
    },
    {
      title: "描述",
      dataIndex: "description",
      search: false,
    },
    {
      title: "首页列表展示",
      dataIndex: 'home_show',
      valueType: "switch",
      search: false,
      render: function (dom, record) {
        return <>{record.home_show ? '是' : '否'}</>
      }
    },
    {
      title: "创建时间",
      dataIndex: "created_at",
      valueType: 'dateRange',
      hidden: true,
    },
    {
      title: "更新时间",
      dataIndex: "updated_at",
      search: false,
    },
    {
      title: "操作",
      valueType: 'option',
      render: function (text, record) {
        return <Space>
          <Button type={'primary'} size={'small'} onClick={() => {
            setCurrent(record)
            setTimeout(() => {
              setOpen(true)
            }, 100)
          }}>编辑</Button>
          <Popconfirm title={'确认删除吗？'} description={'删除后不可恢复'} onConfirm={async () => {
            await handleDelete([record.id].join(","))
          }}>
            <Button size={'small'} danger>删除</Button>
          </Popconfirm>
        </Space>
      },
      search: false,
    }
  ]

  const formColumns: ProFormColumnsType<ApiCategory>[] = [
    {
      title: "分类标题",
      dataIndex: "name",
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      }
    },
    {
      title: "分类图标",
      dataIndex: "icon",
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      }
    },
    {
      title: "分类描述",
      dataIndex: "description",
      formItemProps: {
        rules: [],
      },
      valueType: "textarea"
    },
    {
      title: "排序",
      dataIndex: "sort",
      formItemProps: {
        rules: [],
      },
      valueType: "digit",
      initialValue: 1,
    }, {
      title: "首页列表展示",
      dataIndex: "home_show",
      formItemProps: {
        rules: [],
      },
      valueType: "switch",
      initialValue: false,
    },
  ]

  useEffect(() => {
    if (!open) {
      setCurrent(null)
    }
  }, [open])


  return <>
    <PageContainer>
      <ProTable<ApiCategory> params={{
        orderBy: "id",
        orderType: "desc",
      }} actionRef={actionRef} rowKey={'id'} columns={columns} scroll={{x: 750}}
                             request={async (params) => {
                               return getApiCategoryList(params);
                             }}
                             toolBarRender={() => [
                               <Button type={'primary'} size={'small'} key={'add'} onClick={() => {
                                 setOpen(true)
                               }}>添加分类</Button>
                             ]}
      >
      </ProTable>
      <ModalForm title={'接口分类'} open={open} onOpenChange={setOpen} request={async () => {
        if (current) {
          return await readApiCategory(current?.id)
        }
        return {}
      }} onFinish={async (values) => {
        if (current) {
          try {
            await updateApiCategory(current?.id, values)
            message.success("更新成功")
            actionRef.current?.reload()
            handleCloseOpen()
          } catch (e) {
            //@ts-ignore
            message.error(e.message ?? '更新失败')
          }

        } else {
          try {
            await createApiCategory(values)
            message.success("创建成功")
            actionRef.current?.reload()
            handleCloseOpen()
          } catch (e) {
            //@ts-ignore
            message.error(e.message ?? '创建失败')
          }
        }
      }} modalProps={{
        destroyOnClose: true
      }}>
        <BetaSchemaForm<ApiCategory>
          title={'接口分类'} columns={formColumns} layoutType={'Embed'}
          //@ts-ignore
        ></BetaSchemaForm>
      </ModalForm>

    </PageContainer>
  </>
}