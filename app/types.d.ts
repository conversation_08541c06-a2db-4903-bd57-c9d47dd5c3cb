import { Order, OrderApi } from "@/app/(admin)/admin/order/data";
import { User } from "@/app/(admin)/admin/user/data";
import { ApiPlanLimit } from "./(admin)/admin/api/data";

export interface Banner {
  title: string;
  image: string;
  link: string
  target: string
}

export interface ApiCategory {
  id: number;
  name: string;
  icon: string;
  description: string;
  sort: number;
}

export interface SiteConfig {
  site_logo: string;
  site_name: string;
  site_title: string;
  site_keywords: string;
  api_domain: string;
  site_gongan: string;
  site_icp: string;
  site_open_reg: boolean;
  site_description: string;
  captcha_type: string;
  geetest_captcha_id?: string
  kefu?: string;
  kefu_img?: string
  privacy_policy: string;
  service_agreement: string;
  site_script?: string;
}

export interface HomeApi {
  id: number;
  name: string;
  icon: string;
  description: string;
  sort: number;
  apis: Api[]
}

export interface Api {
  description: string | undefined;
  id: number;
  name: string;
  prices: ApiPrice[];
  img: string;
  type: number;
  introduce: string | undefined;
  category: ApiCategory,
  nodes: ApiNode[]
  question: string | undefined;
  min_price: number | undefined;
  plan_limits: ApiPlanLimit[]
}

export interface ApiPrice {
  price: number;
  num: number;
  id: number;
  is_trail: true;
  unit_price: number;
}

export interface ApiNode {
  name: string;
  id: number;
  request_url: string;
  request_method: string;
  is_free: string;
  response: string;
  request_params: ApiNodeParams[];
  response_params: ApiNodeParams[];
  description?: string;
}

export interface ApiNodeParams {
  name: string;
  data_type: string;
  is_required: true;
  description: string;
  default: string;
  example: string;
}

export interface PurchaseApi {
  order: Order,
  payment_info?: {
    type: string;
    url: string;
    param?: object
  }
  is_payment: boolean;
}

export interface OrderStatus {
  is_pay: boolean;
  pay_message: string
}

export interface UserApi {
  api_id: number;
  apply_time: string;
  daily_limit: number;
  id: number;
  is_free: boolean
  qps: number;
  use_trail: number;
  use_num: number;
}