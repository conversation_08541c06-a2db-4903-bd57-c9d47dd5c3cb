"use server"

import request, { apiRequest } from "@/lib/request";
import { Api, ApiCategory, Banner, HomeApi, SiteConfig, UserApi } from "@/app/types";
import { Plan } from "@/app/(admin)/admin/plan/data";
import { User } from "@/app/(admin)/admin/user/data";
import { PageType } from "@/app/(admin)/admin/page/data";
import { cookies } from 'next/headers'
import { AffCodeKey } from '@/config/data';

export async function getSystemConfig(): Promise<SiteConfig> {
  try {
    return await apiRequest<SiteConfig>("/frontend/index/config");
  } catch (e) {
    // @ts-ignore
    return {}
  }
}

export async function getBanner(): Promise<Banner[]> {
  try {
    return await apiRequest<Banner[]>('/frontend/index/banner');
  } catch (e) {
    // @ts-ignore
    return []
  }
}

export async function getIndexCategories(): Promise<ApiCategory[]> {
  try {
    return await apiRequest<ApiCategory[]>("/frontend/index/categories")
  } catch (e) {
    // @ts-ignore
    return []
  }
}

export async function getIndexApis(): Promise<HomeApi[]> {
  try {
    return await apiRequest<HomeApi[]>("/frontend/index/apis")
  } catch (e) {
    return [] as HomeApi[]
  }
}

export async function getImageCaptcha() {
  try {
    return await request.get("/frontend/captcha/captcha");
  } catch (e) {
    return {}
  }
}

export async function getApiDetail(id: number): Promise<Api | null> {
  try {
    return await apiRequest<Api>(`/frontend/api/find/${id}`)
  } catch (e) {
    return null
  }
}

export async function getPlans(): Promise<Plan[] | null> {
  try {
    return await apiRequest<Plan[]>(`/frontend/index/plans`)
  } catch (e) {
    return null
  }
}

export async function getUserHasApi(id: number): Promise<UserApi | null> {
  try {
    return await apiRequest(`/frontend/user/has_api/${id}`)
  } catch (e) {
    return null;
  }
}

export async function applyUserApi(id: number) {
  try {
    await apiRequest(`frontend/purchase/apply_api/${id}`, "POST")
    return { success: true, message: "申请成功" }
  } catch (e) {
    return { success: false, message: e.message }
  }
}

export async function getUserProfile(): Promise<User | null> {
  try {
    return await apiRequest<User>(`frontend/user/profile`, "GET")
  } catch (e) {
    return null
  }
}

export async function getPageContent(slug: string): Promise<PageType | null> {
  try {
    return await apiRequest<PageType>(`frontend/index/page/${slug}`, "GET")
  } catch (e) {
    return null
  }
}

export async function getMenuList(position: string) {
  try {
    return await apiRequest<PageType>(`frontend/index/menu?position=${position}`, "GET")
  } catch (e) {
    return null
  }
}

export async function getSystemCodes() {
  try {
    return await apiRequest<any[]>(`frontend/index/system_codes`, "GET")
  } catch (e) {
    return []
  }
}

export async function setAffCode(affCode: string) {
  cookies().set({
    name: AffCodeKey,
    value: affCode,
    httpOnly: false,
    path: "/",
    maxAge: 60 * 60 * 24 * 30, // 30天
    sameSite: "lax"
  });
}
