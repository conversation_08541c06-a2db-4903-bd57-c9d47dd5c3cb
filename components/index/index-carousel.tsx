"use client"
import {Carousel, Row, Col, Empty} from "antd";
import Link from 'next/link'
import Image from "next/image";
import {ApiC<PERSON><PERSON><PERSON>, Banner} from "@/app/types";
import {ICONFONT} from "@/components/antd/icon";

interface IndexCarouselProps {
  banners: Banner[]
  categories: ApiCategory[]
}

export function IndexCarousel({banners, categories}: IndexCarouselProps) {
  return <>
    <div className={'grid grid-cols-12 justify-between gap-5 md:h-[400px] mx-1 md:mx-0'}>
      <div className={'md:col-span-3 col-span-12'}>
        <div>
          <div
            className="h-[285px] bg-[#F7F8FA]  leading-[20px] lg:mr-[24px] mb-[15px] rounded px-[12px] box-border text-[14px] text-center">
            <div className="flex flex-row justify-between px-[5px] py-[12px]">
              <div className="text-[14px] font-bold">分类</div>
              <div className="">
                <Link href={'/explore'}>更多</Link>
              </div>
            </div>
            <div
              className="overflow-hidden border-t-[1px] border-t-solid border-t-[#d9d9d9] flex flex-col justify-between">
              <div className="flex flex-wrap justify-between pt-[10px] text-sm">
                {categories && categories.map((category) => (
                  <Link key={category.id} className="w-[45%] md:w-[100px] pb-[10px]" href={`/explore?category_id=${category.id}`}
                        rel="nofollow">
                    <div className="flex leading-[30px] ">
                      <div
                        className="mr-[10px] w-[30px] min-w-[30px] h-[30px] leading-[30px] bg-[#efefef] border-none rounded ">
                        {category.icon.includes('http') ? <img src={category.icon} alt={category.name} className="w-[30px] h-[30px] object-cover" /> : <ICONFONT type={category.icon} className={'text-[#000541] !text-[28px]'}></ICONFONT>}
                      </div>
                      <div
                        className=" flex-1 text-left text-[#000541] hover:text-blue-600"
                        title={category.name}>{category.name}
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </div>
          <div className='mr-[24px] hidden lg:block'>
            <a className="text-center rounded"
               href="/?utm_source=home&amp;utm_medium=home" rel="nofollow" target="_blank"><img
              src="/images/alapi-windows-01.png"
              alt="ALAPI"
              className="rounded-[4px] w-[100%]" width={100} height={100}/>
            </a>
          </div>
        </div>
      </div>
      <div className={'col-span-9 hidden md:block w-full'}>
        <div className={'overflow-hidden'}>
          {banners && banners?.length > 0 ? <>
            <Carousel arrows={true} autoplay dots style={{width: "100%"}} className={'w-full  h-[400px]'}>
              {banners.map((banner: Banner, index) => (
                <div key={index}>
                  <Link target={banner.target} href={banner.link}> <img
                    src={banner.image}
                    style={{width: '100%', height: 400}}
                    alt={banner.title}/></Link>
                </div>
              ))}
            </Carousel>
          </> : <>
            <div className={'justify-center items-center'}>
              <Empty/>
            </div>
          </>}


        </div>
      </div>
    </div>
  </>
}