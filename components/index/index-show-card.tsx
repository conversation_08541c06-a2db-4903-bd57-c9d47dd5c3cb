"use client"
import {<PERSON>, Grid, Typography, Row, Col, Tag, Button} from "antd";
import React from "react";
import {Api, ApiCategory, HomeApi} from "@/app/types";
import Link from "next/link";
import { PlanColor } from "@/config/data";

const {Title, Text} = Typography;

interface IndexShowCardProps {
  api: HomeApi
}

const IndexShowCard = ({api}: IndexShowCardProps) => {

  const apis = api.apis

  return <>
    <div className="mb-[24px] w-full">
      <div className={'flex justify-between items-center mx-2 md:mx-0 mb-3'}>
        <div>
          <Title level={4} className="!mb-0">{api.name}</Title>
        </div>
        <div>
          <Link href={`/explore?category_id=${api.id}`} className="text-blue-600 hover:text-blue-700 text-sm">
            更多
          </Link>
        </div>
      </div>
      <div className={'grid grid-cols-12'}>
        <div className={'col-span-3 md:block hidden'}>
          <Card className="w-[270px]	min-h-[275px] rounded-lg bg-blue5">
            <div className=''>
              <Title className='text-white'>{api.name}</Title>
              <Text className='text-white'>
                {api.description}
              </Text>
            </div>
          </Card>
        </div>
        <div className={'col-span-12 md:col-span-9'}>
          <div className={'grid mx-2 grid-cols-1 gap-y-6 md:mx-0 md:grid-cols-3 md:gap-4'}>
            {apis && apis.length > 0 && apis.slice(0, 6).map((e) => {
              return <div key={"api-" + e.id} className={'w-full'}>
                <Link href={`/api/${e.id}/introduction`}>
                  <Card className='h-[130px] w-full cursor-pointer hover:bg-slate-100	' hoverable={true}>
                    <div className='p-0 flex'>
                      <div>
                        <div className="w-[40px] h-[40px] bg-cover " style={{
                          backgroundImage: `url(${e.img})`,
                          backgroundPosition: "50%"
                        }}>
                        </div>
                      </div>
                      <div className='flex-1 pl-2'>
                        <div className='flex-none items-center mb-[10px]'>
                          <p className='text-[#333] font-bold truncate' title={e.name}>{e.name} {e.type == 1 ?
                            <Tag color={PlanColor.free}>免费接口</Tag> : (e.type == 2 ? <Tag color={PlanColor.vip}>会员接口</Tag> :
                              <Tag color={PlanColor.pro}>计次接口</Tag>)}  </p>
                        </div>
                        <div className='w-full flex-1 '>
                          <p
                            className='text-xs line-clamp-3 min-h-[40px]'
                            title={e.description}>{e.description}</p>

                          {e.type === 3 ? 
                            (e.min_price && <p className='text-xs font-bold text-[#ff1744] mt-1'>￥{e.min_price}/次</p>) :
                            <Button size={'small'} type={'link'}>免费申请</Button>}
                        </div>
                      </div>
                    </div>
                  </Card>
                </Link>
              </div>
            })}
          </div>

        </div>
      </div>
    </div>
  </>

};

export default IndexShowCard;
