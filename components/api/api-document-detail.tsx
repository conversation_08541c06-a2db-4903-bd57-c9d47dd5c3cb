"use client"
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Input, message, Table, Typography } from "antd";
import { CopyOutlined } from "@ant-design/icons";
// @ts-ignore
import { CopyToClipboard } from 'react-copy-to-clipboard';
import CodeExample from "@/components/api/code_example";
import { ApiParamsColumns, ApiResponseParamsColumns } from "@/components/api/data";
import { ApiNode, SiteConfig } from "@/app/types";
import { useSiteConfig } from "@/components/provider/site-provider";
import JsonView from "@uiw/react-json-view";
import { useEffect, useState } from "react";
import { convertResponseParams, getApiUrl } from "@/utils/helper";
import Link from "next/link";
import { useAuthModal } from "../provider/auth-modal-context";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";

export default function ApiDocumentDetail({ node, show_test_api = true, id = 0 }: {
  input: object,
  show_test_api?: boolean,
  id: number
  node: ApiNode
}) {
  const session = useSession();

  const { openModal } = useAuthModal();
  const route = useRouter()

  const siteConfig = useSiteConfig() as SiteConfig

  const [response, setResponse] = useState("")

  const [apiUrl, setApiUrl] = useState("")

  // 添加默认 token 参数
  const requestParams = [
    {
      name: 'token',
      type: 'string',
      is_required: true,
      description: <>接口调用token,需要在 <Typography.Link onClick={() => {
        if (session.status != "authenticated") {
          openModal()
          return;
        }
        route.push('/dashboard/data/token')
      }}>token管理</Typography.Link> 中创建</>,
    },
    ...(node.request_params || [])
  ]

  //处理响应参数 使其支持children
  //

  useEffect(() => {
    try {
      setResponse(JSON.parse(node.response))
    } catch {
    }
    setApiUrl(getApiUrl(siteConfig.api_domain, node.request_url))

  }, [node, siteConfig.api_domain]);

  return <>
    <div className=' p-0 w-full'>
      <div className="mt-2 mb-5">
        {/* 描述 */}
        {node?.description || ''}
      </div>
      <div className='mb-10'>
        <h3 className="text-lg mb-[10px] font-normal">接口地址</h3>
        <Input.Search addonBefore="GET" value={apiUrl}
          suffix={<> <CopyToClipboard
            text={'https://' + siteConfig.api_domain + '/api' + node.request_url}><span
              className={'cursor-pointer'}
              onCopy={() => message.success("复制成功")}><CopyOutlined /></span></CopyToClipboard> </>}
          enterButton={<Link href={`/test/${id}`}>测试API</Link>}></Input.Search>
      </div>
      <div className='mb-10'>
        <h3 className="text-lg mb-[10px] font-normal">请求参数</h3>
        <Table id={'request_params'} rowKey='name' key={'name'} columns={ApiParamsColumns}
          dataSource={requestParams}
          pagination={false} expandable={{
            rowExpandable: (record) => !!record.example,
            expandedRowRender: (record) => <>示例参数： <code>{record.example}</code></>
          }}
        />
      </div>
      <div className="mb-10 w-full">
        <h3 className="text-lg mb-[10px] font-normal">请求示例代码</h3>
        <Alert className={'p-2'} type='info'
          description={'为保证示例代码成功执行，请按参数说明规范填写参数值，同时需删除为空值的参数'} />
        <CodeExample id={id} api_url={apiUrl} node={node} show_test_api={show_test_api} />
      </div>
      <div className={'mb-10 w-full'}>
        <div className={'flex items-center justify-between'}>
          <h3 className="text-lg mb-[10px] font-normal">成功示例</h3>
          <Typography.Text copyable={{
            text: node.response,
          }}></Typography.Text>
        </div>
        <Card>
          <JsonView enableClipboard={false} displayObjectSize={false} displayDataTypes={false} collapsed={1}
            value={response as Object}></JsonView>
        </Card>
      </div>
      <div className={'mb-10 w-full'}>
        <h3 className="text-lg mb-[10px] font-normal">响应参数</h3>
        <Table 
          id={'response_params'} 
          rowKey='name' 
          key={'name'} 
          columns={ApiResponseParamsColumns}
          dataSource={convertResponseParams(node.response_params)}
          pagination={false}
        />
      </div>
    </div>

  </>
}