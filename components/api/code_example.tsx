"use client"
import {useEffect, useState} from "react";
import {<PERSON><PERSON>, <PERSON><PERSON>, message as Message, Space} from "antd";
import {codeConvert} from "@/utils/httpsnippet";
import {useRouter} from 'next/navigation'
import {AceCodeEditor} from "@/components/common/ace-code-editor";
import {CodeLanguage} from "@/components/api/data";
import {ApiNode} from "@/app/types";
import {GenerateSnippetOptions,} from "@/utils/helper";

interface Props {
  show_test_api: boolean
  id: number;
  node: ApiNode,
  api_url: string
  method?: string
  request_params?: object
}

export default function CodeExample({node, api_url, show_test_api = true, id = 0, method, request_params}: Props) {
  const router = useRouter()

  const [input, setInput] = useState({})
  useEffect(() => {
    if (node) {
      console.log(request_params)
      setInput(GenerateSnippetOptions(node, api_url, method, request_params))
    }

  }, [api_url, node, method, request_params]);


  const [lang, setLang] = useState("java")
  const [acgLang, setAceLang] = useState("java")
  const [clientId, setClientId] = useState("")
  const [code, setCode] = useState('')
  useEffect(() => {
    try {
      setCode(codeConvert(lang, input, clientId))
    } catch (e) {
      console.log(e)
      console.log(input)
      setCode("")
    }
  }, [clientId, input, lang]);

  return <>
    <div>
      <div className='mt-2'>
        <Space size='large'>
          <Cascader className={'w-[300px]'} size={'middle'} defaultValue={['Java']} placeholder='请选择'
                    onChange={(value, selectedOptions) => {
                      const opt = selectedOptions[selectedOptions.length - 1]
                      setLang(opt.lang)
                      setClientId(opt.id)
                      if (opt.lang == "go") {
                        setAceLang("golang")
                      } else if (lang == "shell") {
                        setAceLang("sh")
                      } else {
                        setAceLang(opt.lang)
                      }
                    }} options={CodeLanguage} fieldNames={{
            children: 'child',
            label: 'label',
            value: 'id',
          }}>
          </Cascader>
          {show_test_api && <Button type='primary' onClick={() => {
            router.push('/test/' + id)
          }}>测试API</Button>}
        </Space>
      </div>
      <div className='mt-2'>
        <AceCodeEditor code={String(code)} mode={lang}></AceCodeEditor>
      </div>
    </div>
  </>
}