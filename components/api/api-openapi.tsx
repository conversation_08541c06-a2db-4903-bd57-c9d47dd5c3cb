"use client"
import { env } from "@/env"
import { <PERSON><PERSON>, Button, Input, Radio, Space, Typography, message } from "antd"
import { CopyOutlined, DownloadOutlined, EyeOutlined } from '@ant-design/icons'
import { useEffect, useState } from "react"
import J<PERSON>View from "@uiw/react-json-view"
import { CopyToClipboard } from 'react-copy-to-clipboard';


const OpenAPI = ({ id }: { id: number }) => {
    const { NEXT_PUBLIC_API_URL } = env
    const OPENAPI_URL = `${NEXT_PUBLIC_API_URL}/openapi/${id}.json`
    const [messageApi, contextHolder] = message.useMessage()
    const [previewVisible, setPreviewVisible] = useState(true)
    const [previewType, setPreviewType] = useState<'online' | 'local'>('online')
    const [openApiData, setOpenApiData] = useState<any>(null)

    const handleCopySuccess = () => {
        messageApi.success('已复制 OpenAPI 地址')
    }

    const handleDownload = () => {
        const link = document.createElement('a')
        link.href = OPENAPI_URL
        link.setAttribute('download', `openapi-${id}.json`)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
    }

    const togglePreview = () => {
        setPreviewVisible(!previewVisible)
    }

    useEffect(() => {
        const fetchOpenAPI = async () => {
            const response = await fetch(OPENAPI_URL)
            const data = await response.json()
            setOpenApiData(data)
        }
        fetchOpenAPI()
    }, [])

    return <>
        {contextHolder}
        <div className='mx-2'>
            <div className="pt-[30px]">
                <Alert
                    message="OpenAPI 接口文档"
                    description="通过以下 OpenAPI 地址，您可以获取此 API 的 OpenAPI 规范文档。OpenAPI 规范是 RESTful API 的标准化描述格式，可用于生成文档、客户端代码等。"
                    type="info"
                    showIcon
                    className="mb-4"
                />

                <div className="flex flex-col gap-4">
                    <div>
                        <Typography.Title level={5}>OpenAPI 地址</Typography.Title>
                        <Space.Compact style={{ width: '100%' }}>
                            <Input
                                value={OPENAPI_URL}
                                readOnly
                                addonBefore="URL"
                            />
                            <CopyToClipboard text={OPENAPI_URL} onCopy={handleCopySuccess}>
                                <Button icon={<CopyOutlined />}>复制</Button>
                            </CopyToClipboard>
                        </Space.Compact>
                    </div>

                    <div>
                        <Space>
                            <Button
                                type="primary"
                                icon={<DownloadOutlined />}
                                onClick={handleDownload}
                            >
                                下载 OpenAPI 文档
                            </Button>
                            <Button
                                icon={<EyeOutlined />}
                                onClick={togglePreview}
                            >
                                {previewVisible ? '隐藏预览' : '查看预览'}
                            </Button>
                        </Space>
                    </div>

                    {previewVisible && (
                        <div className="mt-4 border rounded p-4 bg-gray-50">
                            <div className="flex justify-between items-center mb-4">
                                <Typography.Title level={5} style={{ margin: 0 }}>OpenAPI 预览</Typography.Title>
                                <Radio.Group
                                    value={previewType}
                                    onChange={e => setPreviewType(e.target.value)}
                                    optionType="button"
                                    buttonStyle="solid"
                                >
                                    <Radio.Button value="online">在线预览 (Swagger Editor)</Radio.Button>
                                    <Radio.Button value="local">本地预览 OpenAPI</Radio.Button>
                                </Radio.Group>
                            </div>

                            <div className="h-[600px] overflow-auto">
                                {previewType === 'online' ? (
                                    <div className="swagger-ui-container">
                                        <div className="mt-4 p-4 border rounded bg-white">
                                            <iframe
                                                src={`/api/swagger-ui?url=${encodeURIComponent(OPENAPI_URL)}`}
                                                width="100%"
                                                height="600px"
                                                title="本地 Swagger UI 预览"
                                            />
                                        </div>
                                    </div>

                                ) : (
                                    <div className="swagger-ui-container">
                                        <div className="mt-4 p-4 border rounded bg-white">
                                            <JsonView enableClipboard={false} displayObjectSize={false} displayDataTypes={false} collapsed={false}
                                                value={openApiData as Object}></JsonView>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    </>
}

export default OpenAPI

