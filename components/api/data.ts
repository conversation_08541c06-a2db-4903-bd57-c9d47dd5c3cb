export const Api<PERSON>aramsColumns = [
  {
    title: "参数名",
    dataIndex: "name"
  },
  {
    title: "类型",
    dataIndex: "data_type"
  },
  {
    title: "是否必填",
    dataIndex: "is_required",
    //@ts-ignore
    render: function (_, record) {
      return record.is_required ? '是' : '否'
    }
  },
  {
    title: "说明",
    dataIndex: "description"
  }
]

export const ApiResponseParamsColumns = [
  {
    title: "参数名",
    dataIndex: "name",
    width: "100px"
  },
  {
    title: "类型",
    dataIndex: "data_type",
    width: "80px"
  },
  {
    title: "是否必填",
    dataIndex: "is_required",
    //@ts-ignore
    render: function (_, record) {
      return record.is_required ? '是' : '否'
    },
    width: "80px"
  },
  {
    title: "说明",
    dataIndex: "description",
    width: "150px"
  }
]

export const CodeLanguage = [
  {
    label: "Java",
    id: "okhttp",
    lang: "java"
  },
  {
    label: "PHP",
    id: "php",
    lang: "php",
    child: [
      {
        label: "curl",
        id: "curl",
        lang: "php",
      },
      {
        label: "guzzle",
        id: "guzzle",
        lang: "php"
      }
    ]
  },
  {
    label: "Python",
    id: "requests",
    lang: "python",
  },
  {
    label: "Javascript",
    id: "javascript",
    lang: "javascript",
    child: [
      {
        label: "jquery",
        id: "jquery",
        lang: "javascript",
      },
      {
        label: "fetch",
        id: "fetch",
        lang: "javascript",
      }, {
        label: "xhr",
        id: "xhr",
        lang: "javascript",
      }
    ]
  },
  {
    label: "NodeJS",
    id: "node",
    lang: "node",
    child: [
      {
        label: "native",
        id: "native",
        lang: "node",
      },
      {
        label: "request",
        id: "request",
        lang: "node",
      }
    ]
  },
  {
    label: "Golang",
    id: "native",
    lang: "go",
  },
  {
    label: "微信小程序",
    id: "wxapp",
    lang: "javascript"
  },
  {
    label: "UniAPP",
    id: "uniapp",
    lang: "javascript"
  },
  {
    label: "cURL",
    id: "curl",
    lang: "shell",
  }
]