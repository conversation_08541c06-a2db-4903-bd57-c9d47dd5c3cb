import { <PERSON><PERSON>, Button, Descriptions, Drawer, Form, Input, Menu, message, Select, Space, Spin, Table, Tabs } from "antd";
import { CopyOutlined, SendOutlined, ZoomInOutlined } from "@ant-design/icons";
import { AceCodeEditor } from "@/components/common/ace-code-editor";
import CodeExample from "@/components/api/code_example";
import { ApiNode, SiteConfig } from "@/app/types";
//@ts-ignore
import { CopyToClipboard } from 'react-copy-to-clipboard';
import { getApiUrl, jsonToQueryString } from "@/utils/helper";
import { useEffect, useMemo, useState, useCallback } from "react";
import axios, { AxiosHeaders, AxiosResponse } from "axios";
import JsonView from "@uiw/react-json-view";
import { monokaiTheme } from '@uiw/react-json-view/monokai';
import TokenTable from "@/app/(dashboard)/dashboard/data/token/table";
import OpenAPI from "./api-openapi";

const FormItem = Form.Item;

interface ColumnsType {
  param: string;
  description: string;
  value: string;
  required: boolean
}


const ApiTestForm = ({ node, siteConfig, token, onRequestAfter }: { node: ApiNode, siteConfig: SiteConfig, token?: string, onRequestAfter: () => void }) => {

  const [form] = Form.useForm();

  const [requestMethod, setRequestMethod] = useState("")
  const apiUrl = getApiUrl(siteConfig.api_domain, node.request_url)
  const [requestUrl, setRequestUrl] = useState(apiUrl)
  const [selectedRowKeys, setSelectedRowKeys] = useState(['token']);
  const [sendRequestParams, setSendRequestParams] = useState({})
  const [isSendRequest, setIsSendRequest] = useState(false)

  const [responseData, setResponseData] = useState({})
  const [responseHeader, setResponseHeader] = useState<{ label: string, children: string }[]>([])

  const [resp, setResp] = useState<AxiosResponse>()

  const [responseDuration, setResponseDuration] = useState(0)

  const [data, setData] = useState([])

  useEffect(() => {
    const fields = form.getFieldsValue();
    const temp = {};
    for (const row of selectedRowKeys) {
      if (fields[row]) {
        temp[row] = fields[row];
      }
    }
    setSendRequestParams(temp);

    if (node.request_method !== 'GET') {
      return;
    }
    const tempQuery = jsonToQueryString(temp);
    if (tempQuery) {
      setRequestUrl(apiUrl + "?" + jsonToQueryString(temp));
    }
  }, [selectedRowKeys, node.request_method, apiUrl]);

  const handleValuesChange = useCallback(async () => {
    const fields = form.getFieldsValue();
    const temp = {};

    for (const row of selectedRowKeys) {
      temp[row] = fields[row] || '';
    }

    setSendRequestParams(temp);

    if (node.request_method !== 'GET') {
      return;
    }

    const tempQuery = jsonToQueryString(temp);
    if (tempQuery) {
      setRequestUrl(apiUrl + "?" + jsonToQueryString(temp));
    } else {
      setRequestUrl(apiUrl);
    }
  }, [selectedRowKeys, node.request_method, apiUrl, form]);
  useEffect(() => {
    if (node.request_params) {
      const defaultCheck = ["token"]
      const savedToken = localStorage.getItem('api_test_token') || token
      const temp = [
        {
          param: "token",
          description: "token秘钥",
          value: savedToken,
          required: true,
          ready: true,
        }
      ]
      for (const param of node.request_params) {
        temp.push({
          param: param.name,
          description: param.description,
          value: param.default,
          required: param.is_required,
          ready: false,
        })
        if (param.is_required) {
          defaultCheck.push(param.name)
        }
      }
      //@ts-ignore
      setData(temp)
      setSelectedRowKeys(defaultCheck)

      // 手动触发一次表单值更新
      handleValuesChange()
    }

  }, [node.request_params, token])

  const sendRequest = async () => {
    // const validate =  (await form.validateFields())
    form.validateFields().then(async (res) => {
      setIsSendRequest(true)
      const start = Date.now()
      const response = await axios.request({
        url: requestUrl,
        method: requestMethod,
        params: requestMethod === "GET" ? sendRequestParams : {},
        data: requestMethod === "POST" ? sendRequestParams : {},
        withCredentials: false,
        headers: {
        },
        maxRedirects: 0,
        validateStatus: function (status) {
          return true
        },
      })
      setResponseDuration(Date.now() - start)

      setResponseData(response.data)
      const repHeaders = response.headers as AxiosHeaders
      const tempHeader = []
      for (let repHeadersKey in repHeaders) {
        tempHeader.push({
          label: repHeadersKey,
          children: repHeaders[repHeadersKey]
        })
      }
      //@ts-ignore
      setResponseHeader(tempHeader)
      setResp(response)

    }).catch(err => {
      console.log(err)
      if (typeof err == 'object') {
        const errorFields = err.errorFields
        if (errorFields && errorFields.length > 0) {
          message.error("请求参数有误: " + errorFields[0]["errors"][0] || '')
        } else {
          message.warning("请求参数有误，请检查请求参数")
        }
      } else {
        message.warning("请求参数有误，请检查请求参数")
      }
    }).finally(() => {
      setIsSendRequest(false)
      onRequestAfter()
    })

  }
  const [tokenModelOpen, setTokenModelOpen] = useState(false)

  const columns = [
    {
      title: '参数名',
      dataIndex: 'param',
      width: '100px',
    },
    {
      title: '说明',
      dataIndex: 'description',
      width: '300px'
    },
    {
      title: '参数值',
      dataIndex: 'value',
      render: function (col: any, item: ColumnsType) {
        //默认token
        return <div className={'items-center flex'}>
          <FormItem initialValue={item.value} rules={[{ required: item.required, message: `${item.param}不能为空` }]}
            className='w-full'
            name={item.param}><Input suffix={item.param === 'token' && <>
              <Button size="middle" className="md:block hidden" onClick={() => {
                setTokenModelOpen(true)
              }}>选择Token</Button>
              <Button size="small" className="md:hidden block" onClick={() => {
                setTokenModelOpen(true)
              }} icon={<ZoomInOutlined />}></Button>
            </>} /></FormItem>
        </div>
      },
    }
  ];

  const getRequestMethodOptions = useMemo(() => {
    const method = node.request_method
    if (method === "ANY") {
      setRequestMethod("GET")
      return [
        {
          label: "GET",
          value: "GET",
        },
        {
          label: "POST",
          value: "POST",
        }
      ]
    }
    setRequestMethod(method)
    return [
      {
        label: method,
        value: method
      }
    ]
  }, [node])

  useEffect(() => {
    return () => {
      // 清理工作
      setSendRequestParams({});
      setRequestUrl(apiUrl);
    };
  }, []);

  return <>
    <div className="flex md:flex-row flex-col h-full">
      <div className='border-r flex-1 p-5'>
        {/* 标题*/}
        <div className='text-lg font-medium'>
          {node.name}
        </div>
        <div className='mt-5'>
          <div className='text-lg'>
            API地址
          </div>
          <div className='flex'>
            <div className='flex-1'>
              <Input addonBefore={<Select className={'w-[80px]'} defaultValue={requestMethod}
                onChange={(value) => setRequestMethod(value)}
                options={getRequestMethodOptions} />}
                readOnly={true} addonAfter={<> <span><CopyToClipboard onCopy={async () => {
                  message.success("复制成功")
                }}
                  text={apiUrl}><CopyOutlined /></CopyToClipboard></span> </>}
                value={requestUrl} size={'middle'} />
            </div>
            <div className='ml-1'>
              <Button type='primary' size={'middle'} icon={<SendOutlined />}
                onClick={() => sendRequest()} disabled={isSendRequest}>发送请求</Button>
            </div>
          </div>
        </div>
        <div className={'mt-5'}>
          <div className='text-lg'>
            请求参数
          </div>
          <div className={''}>
            <Form
              form={form}
              onValuesChange={handleValuesChange}
              initialValues={{
                // token: localStorage.getItem('api_test_token') || token
              }}
            >
              <Table columns={columns} dataSource={data} rowKey={'param'} pagination={false}
                rowSelection={{
                  type: "checkbox",
                  selectedRowKeys,
                  onChange: (selectedRowKeys) => {
                    // @ts-ignore
                    setSelectedRowKeys(selectedRowKeys);
                  },
                  getCheckboxProps: (record) => {
                    return {
                      disabled: record.required == true,
                    };
                  },
                }}
                scroll={{ x: 'max-content' }}
              />
            </Form>
          </div>
        </div>
      </div>
      <div className='flex-1'>
        <div className='p-5'>
          <Tabs>
            <Tabs.TabPane tab={'返回结果'}>
              <div
                className='flex bg-primary border leading-[30px] h-[30px] text-white	text-sm px-2 mb-1 rounded'>
                <div className='flex-1'>
                  {resp?.status}
                </div>
                <div>
                  <span className="text-right mr-5">Size：{resp?.headers['content-length'] ?? 0} Bytes</span>
                  <span className="text-right ">Time： {responseDuration} ms</span>
                </div>
              </div>
              <div className=""><Spin spinning={isSendRequest}>
                <AceCodeEditor theme={'github'} code={JSON.stringify(responseData, null, 2)}
                  mode={'json'}></AceCodeEditor>
              </Spin></div>
              <div className="mt-2"><Alert  message="由于前端请求限制，在线调试并不能有效的处理重定向和二进制内容显示" type="warning" /></div>
            </Tabs.TabPane>
            <Tabs.TabPane tab={'返回头部'} key={'response_header'}>
              <Descriptions column={1} items={responseHeader}>
              </Descriptions>
            </Tabs.TabPane>
            <Tabs.TabPane tab={'请求参数'} key={'request_params'}>
              <JsonView style={monokaiTheme} enableClipboard={false} displayObjectSize={false} displayDataTypes={false}
                collapsed={1}
                value={sendRequestParams}></JsonView>
            </Tabs.TabPane>
            <Tabs.TabPane tab={'代码示例'} key={'code'}>
              <CodeExample node={node} api_url={requestUrl} id={0} show_test_api={false} method={requestMethod}
                request_params={sendRequestParams} />
            </Tabs.TabPane>
            <Tabs.TabPane tab={'OpenAPI'} key={'openapi'}>
              <OpenAPI id={node.id} />
            </Tabs.TabPane>
          </Tabs>
        </div>
      </div>
    </div>
    <div>
      <Drawer width={900} title={'token管理'} open={tokenModelOpen} onClose={() => setTokenModelOpen(false)} afterOpenChange={(v) => setTokenModelOpen(v)}>
        <TokenTable onSelect={(entity) => {
          //@ts-ignore
          form.setFieldValue("token", entity.token)
          localStorage.setItem('api_test_token', entity.token)
          setTokenModelOpen(false)
          handleValuesChange()
        }} />
      </Drawer>
    </div>
  </>
}

export default ApiTestForm