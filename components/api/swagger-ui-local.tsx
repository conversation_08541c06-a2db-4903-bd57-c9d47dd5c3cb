"use client"

import React, { useEffect, useState } from 'react'
import { <PERSON><PERSON>, Button, Spin, Tabs, Typography } from 'antd'
import { CodeOutlined, CopyOutlined, DownloadOutlined } from '@ant-design/icons'
import { env } from '@/env'

interface SwaggerUILocalProps {
  openApiUrl: string
}

const SwaggerUILocal: React.FC<SwaggerUILocalProps> = ({ openApiUrl }) => {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [openApiSpec, setOpenApiSpec] = useState<any>(null)
  const { NEXT_PUBLIC_API_URL } = env

  useEffect(() => {
    const fetchOpenApiSpec = async () => {
      try {
        setIsLoading(true)
        const response = await fetch(openApiUrl)
        
        if (!response.ok) {
          throw new Error(`获取 OpenAPI 规范失败: ${response.status}`)
        }
        
        const data = await response.json()
        setOpenApiSpec(data)
      } catch (err) {
        setError(err instanceof Error ? err.message : '获取 OpenAPI 规范时发生未知错误')
      } finally {
        setIsLoading(false)
      }
    }

    fetchOpenApiSpec()
  }, [openApiUrl])

  const handleCopyDockerCommand = () => {
    const dockerCommand = `docker run -p 8080:8080 -e SWAGGER_JSON_URL=${openApiUrl} swaggerapi/swagger-ui`
    navigator.clipboard.writeText(dockerCommand)
      .then(() => {
        // 可以添加复制成功的提示
      })
  }

  const handleCopyNpmCommand = () => {
    const npmCommand = `npm install swagger-ui-react`
    navigator.clipboard.writeText(npmCommand)
      .then(() => {
        // 可以添加复制成功的提示
      })
  }

  if (isLoading) {
    return <Spin tip="加载中..." />
  }

  if (error) {
    return (
      <Alert
        message="加载错误"
        description={error}
        type="error"
        showIcon
      />
    )
  }

  return (
    <div className="swagger-ui-local">
      <Tabs
        defaultActiveKey="deploy"
        items={[
          {
            key: 'deploy',
            label: '私有化部署方案',
            children: (
              <div className="p-4">
                <Typography.Title level={5}>OpenAPI 预览私有化部署方案</Typography.Title>
                <Typography.Paragraph>
                  以下是几种在您自己的环境中部署 OpenAPI 预览的方法，适用于内网环境或安全要求较高的场景。
                </Typography.Paragraph>

                <div className="mt-4 bg-gray-50 p-4 rounded">
                  <Typography.Title level={5}>1. 使用 Docker 部署 Swagger UI</Typography.Title>
                  <div className="flex items-center mt-2">
                    <Typography.Text code className="bg-black text-white p-2 rounded flex-1">
                      docker run -p 8080:8080 -e SWAGGER_JSON_URL=${openApiUrl} swaggerapi/swagger-ui
                    </Typography.Text>
                    <Button 
                      icon={<CopyOutlined />} 
                      className="ml-2"
                      onClick={handleCopyDockerCommand}
                    >
                      复制
                    </Button>
                  </div>
                  <Typography.Text type="secondary" className="mt-2 block">
                    Docker 是最快的部署方式，只需一行命令即可在本地运行 Swagger UI，并指向您的 OpenAPI 规范。
                  </Typography.Text>
                </div>

                <div className="mt-4 bg-gray-50 p-4 rounded">
                  <Typography.Title level={5}>2. 在 Next.js 项目中集成 Swagger UI</Typography.Title>
                  <div className="flex items-center mt-2">
                    <Typography.Text code className="bg-black text-white p-2 rounded flex-1">
                      npm install swagger-ui-react
                    </Typography.Text>
                    <Button 
                      icon={<CopyOutlined />} 
                      className="ml-2"
                      onClick={handleCopyNpmCommand}
                    >
                      复制
                    </Button>
                  </div>
                  <Typography.Text type="secondary" className="mt-2 block">
                    安装 Swagger UI React 组件，然后在您的项目中使用它：
                  </Typography.Text>
                  <div className="mt-2 bg-gray-700 text-white p-3 rounded overflow-auto">
                    <pre className="whitespace-pre-wrap">
{`import SwaggerUI from 'swagger-ui-react'
import 'swagger-ui-react/swagger-ui.css'

export default function ApiDoc() {
  return <SwaggerUI url="${openApiUrl}" />
}`}
                    </pre>
                  </div>
                </div>

                <div className="mt-4 bg-gray-50 p-4 rounded">
                  <Typography.Title level={5}>3. 使用轻量级替代方案 ReDoc</Typography.Title>
                  <Typography.Text type="secondary" className="mt-2 block">
                    ReDoc 是一个轻量级的替代方案，特别适合注重文档阅读体验：
                  </Typography.Text>
                  <div className="mt-2 bg-gray-700 text-white p-3 rounded overflow-auto">
                    <pre className="whitespace-pre-wrap">
{`npm install redoc
// 或者直接使用 CDN 方式引入

// HTML 方式
<redoc spec-url="${openApiUrl}"></redoc>
<script src="https://cdn.jsdelivr.net/npm/redoc/bundles/redoc.standalone.js"></script>`}
                    </pre>
                  </div>
                </div>

                <div className="mt-4">
                  <Button 
                    type="primary" 
                    icon={<DownloadOutlined />}
                    href={`${NEXT_PUBLIC_API_URL}/api/download-swagger-ui?url=${encodeURIComponent(openApiUrl)}`}
                  >
                    下载 Swagger UI 静态部署包
                  </Button>
                  <Typography.Text type="secondary" className="ml-4">
                    下载包含 Swagger UI 的静态文件，已配置好指向您的 OpenAPI 规范
                  </Typography.Text>
                </div>
              </div>
            ),
          },
          {
            key: 'preview',
            label: '内置预览',
            children: (
              <iframe
                src={`/api/swagger-ui?url=${encodeURIComponent(openApiUrl)}`}
                width="100%"
                height="600px"
                title="本地 Swagger UI 预览"
              />
            ),
          },
          {
            key: 'code',
            label: (
              <span>
                <CodeOutlined /> OpenAPI 规范
              </span>
            ),
            children: (
              <div className="p-4">
                <div className="flex justify-between mb-2">
                  <Typography.Title level={5}>OpenAPI JSON</Typography.Title>
                  <Button 
                    icon={<CopyOutlined />}
                    onClick={() => navigator.clipboard.writeText(JSON.stringify(openApiSpec, null, 2))}
                  >
                    复制 JSON
                  </Button>
                </div>
                <div className="bg-gray-100 p-4 rounded overflow-auto h-[500px]">
                  <pre className="text-sm">{JSON.stringify(openApiSpec, null, 2)}</pre>
                </div>
              </div>
            ),
          },
        ]}
      />
    </div>
  )
}

export default SwaggerUILocal 