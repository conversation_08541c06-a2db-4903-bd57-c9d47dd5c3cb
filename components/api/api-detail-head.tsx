"use client"

import { <PERSON><PERSON>, ConfigProvider, message, Skeleton, Tag, Tooltip } from "antd";
import { CheckCard } from "@ant-design/pro-card";
import { Api, ApiPrice, Purchase<PERSON>pi, User<PERSON>pi } from "@/app/types";
import { useEffect, useState } from "react";
import { applyUserApi, getPlans, getUserHasApi } from "@/app/actions";
import { Plan } from "@/app/(admin)/admin/plan/data";
import { FreeApi, PlanColor, ProApi, VipApi } from "@/config/data";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { useAuthModal } from "@/components/provider/auth-modal-context";
import { apiRequest } from "@/lib/axios";

export default function ApiDetailHead({ api }: { api: Api }) {
  const session = useSession();
  const { openModal } = useAuthModal();

  const route = useRouter()
  const [freeTrail, setFreeTrail] = useState<ApiPrice>()

  const [prices, setPrices] = useState<ApiPrice[]>([])

  const [planLoading, setPlanLoading] = useState(false)
  const [plan, setPlan] = useState<Plan>()
  const [planId, setPlanId] = useState(0)
  const [plans, setPlans] = useState<Plan[]>([])

  const [price, setPrice] = useState<ApiPrice>()
  const [priceId, setPriceId] = useState(0)
  const [userApi, setUserApi] = useState<UserApi | null>()

  const getPrice = (id: number) => {
    return prices.find(e => e.id === id)
  }

  const applyApi = async () => {
    if (session.status != "authenticated") {
      openModal()
      return;
    }
    const res = await applyUserApi(api.id)
    if (res.success) {
      message.success("申请成功")
      route.refresh()
    } else {
      message.warning(res.message)
      if (api.type === VipApi) {
        await route.push("/vip")
      }
    }
  }

  const getPlanLimit = (planId: number) => {
    if (api.plan_limits && api.plan_limits.length > 0) {
      const planLimit = api.plan_limits.find(limit => limit.plan_id === planId);
      if (planLimit) {
        return planLimit.daily_limit;
      }
    }
    const plan = plans.find(p => p.id === planId);
    return plan?.daily_limit;
  }

  useEffect(() => {
    if (api.type === VipApi || api.type === FreeApi) {
      setPlanLoading(true)
      getPlans().then(function (item) {
        if (item) {
          if (api.type === VipApi) {
            const temp: Plan[] = []
            item.forEach((e => {
              if (!e.is_free_plan) {
                temp.push(e)
              }
            }))
            if (temp.length > 0) {
              setPlan(temp[0])
              setPlanId(temp[0].id)
              setPlans(temp)
            }
          } else {
            setPlan(item[0])
            setPlans(item)
          }
          setPlanLoading(false)
        }
      })
    }
    getUserHasApi(api.id).then(r => {
      if (r) {
        setUserApi(r)
      }
    })

  }, [api])

  useEffect(() => {
    if (api.prices.length > 0) {
      const temp: ApiPrice[] = []
      let i = 0
      for (const price of api.prices) {
        if (price.is_trail) {
          setFreeTrail(price)
        } else {
          if (i === 0) {
            setPriceId(price.id)
            setPrice(price)
          }
          i++
          temp.push(price)
        }
      }
      setPrices(temp)
    }
  }, [api.prices])
  useEffect(() => {
  }, [planId]);

  return <>
    <ConfigProvider theme={{
      token: {
        padding: 6,
        paddingSM: 6,
      }
    }}>
      <div className='flex flex-row mx-2'>
        <div className='h-[60px] bg-cover mr-[25px]' style={{
          flex: "0 0 60px",
          backgroundImage: `url(${api.img})`,
          backgroundPosition: "50%"
        }}>
        </div>
        <div className='flex-1'>
          <div className="flex items-center">
            <div className="flex items-center flex-1"><p
              className="text-lg font-bold text_omit title m-0">{api.name}</p>
              <span className=" ml-[10px] "><Tag color={'#108ee9'}>{api.category?.name}</Tag></span>
              <span> {api.type == FreeApi ? <Tag color={PlanColor.free}>免费接口</Tag> : (api.type == VipApi ?
                <Tag color={PlanColor.vip}>会员接口</Tag> : <Tag color={PlanColor.pro}>计次接口</Tag>)} </span>
              {api.plan_limits && api.plan_limits.length > 0 && <span><Tooltip title='该接口独立次数限制,不和通用套餐限制次数一样，请以接口页面为准'><Tag color={'#ff6a00'}>独立限制</Tag></Tooltip></span>}
            </div>
          </div>
          <div
            className="desc mt-[15px] text-[#666] mb-1 lg:pr-[120px] pr-0 break-words">
            {api.description}
          </div>
          {(api.type === FreeApi || api.type === VipApi) && <div>
            <Skeleton loading={planLoading}>
              <div className="flex flex-row items-center pt-5">
                <div className="w-[60px] text-xs text-[#666] mb-4 leading-[38px]">规格</div>
                <div className="flex-wrap flex flex-row items-center ">
                  <div>
                    <div className="flex-wrap flex flex-row items-center flex-1">
                      <CheckCard.Group value={planId} onChange={(e) => {
                        setPlanId(Number(e))
                      }} className={'p-0'}>
                        {plans.map((item, index) => {
                          const actualLimit = getPlanLimit(item.id);

                          return <CheckCard
                            onClick={() => {
                              setPlan(item)
                            }}
                            key={index}
                            className={'w-[200px]'}
                            size={'small'}
                            title={<>
                              <div className={'text-center'}>
                                【{item.name}】{actualLimit}次/天
                              </div>
                            </>}
                            value={item.id}
                          />
                        })}
                      </CheckCard.Group>
                    </div>
                  </div>
                </div>
              </div>
              <div className="mb-4 flex flex-row items-center">
                <div className="w-[60px] text-xs text-[#666] leading-[38px]">说明</div>
                <div className="flex-wrap flex flex-row items-center ">
                  {plan && <div>
                    【{plan.name}】限制 {getPlanLimit(plan.id)} 次/天，
                    {plan.request_qps} QPS
                  </div>}
                </div>
              </div>
            </Skeleton>
          </div>}
          {api.type === ProApi ? <div>
            <div className="flex flex-row mt-5">
              <div className="w-[60px] text-xs text-[#666] mb-4 leading-[38px]">规格</div>
              <div className="flex-wrap flex flex-row items-center flex-1">

                <CheckCard.Group value={priceId} onChange={(e) => {
                  setPriceId(Number(e))
                }} className={'p-0'}>
                  {prices.map((item, index) => {
                    return <CheckCard onClick={() => {
                      setPrice(item)
                    }} key={index} style={{
                      width: "150px",
                    }} className={'w-[150px]'} size={'small'} title={<>
                      <div className={'text-center'}>{item.num}次</div>
                    </>} value={item.id} />
                  })}
                </CheckCard.Group>
              </div>
            </div>
            <div className="flex flex-row items-center">
              <div className="w-[60px] text-xs text-[#666]">套餐价格</div>
              <div className="text-lg"><span
                className="text-[#ff6a00] font-bold">￥ {price?.price ?? 0} </span><span
                  className="text-sm ml-[10px] text-[#666]">{price?.unit_price ?? 0} /次</span></div>
            </div>
            <div className="py-5 border-box flex lg:flex-row flex-wrap gap-5">
              {(!userApi || (userApi && !userApi.use_trail)) &&
                <Button type={'primary'} className='w-[176px] mr-[14px]' onClick={async () => {
                  if (session.status === 'authenticated') {
                    // buy api
                    try {
                      const res = await apiRequest<PurchaseApi>("/frontend/purchase/api", "POST", {
                        pay_method: "wechat_scan",
                        api_id: api.id,
                        price_id: freeTrail?.id,
                        num: 1,
                      })
                      message.success("申请成功")
                      route.push(`/test/${api.id}`)
                    } catch (e) {
                      message.warning(e.message)
                    }
                  } else {
                    openModal()
                  }
                }}>免费试用 {freeTrail?.num} 次</Button>}
              <Button type={'primary'} className='w-[176px] mr-[14px]' onClick={() => {
                if (session.status === 'authenticated') {
                  route.push(`/purchase/${api.id}?price_id=${priceId}`)
                } else {
                  openModal()
                }
              }}>购买套餐</Button>
              <Button type={'dashed'} className='w-[176px] mr-[14px]' onClick={() => {
                if (session.status === 'authenticated') {
                  route.push(`/test/${api.id}`)
                } else {
                  openModal()
                }
              }}>测试API</Button>
            </div>
          </div> : <div>
            <div className="py-5 border-box flex lg:flex-row flex-wrap gap-5">
              {userApi ? <Button type={'primary'} className='w-[176px] mr-[14px]' onClick={() => {
                message.success("您已申请该接口，请勿重新申请")
              }}>已申请</Button> :
                <Button type={'primary'} className='w-[176px] mr-[14px]' onClick={applyApi}>免费申请</Button>}
              <Button type={'dashed'} className='w-[176px] mr-[14px]' onClick={() => {
                if (session.status === 'authenticated') {
                  route.push(`/test/${api.id}`)
                } else {
                  openModal()
                }
              }}>测试API</Button>
            </div>
          </div>}
        </div>
      </div>
    </ConfigProvider>
  </>
}