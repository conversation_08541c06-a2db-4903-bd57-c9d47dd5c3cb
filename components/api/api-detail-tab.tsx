"use client"
// import { useState } from "react"; // Ensure this line is removed or commented
import { usePathname } from "next/navigation";
import Link from 'next/link'
import { createFromIconfontCN } from "@ant-design/icons";
import { Api } from "@/app/types";

const IconFont = createFromIconfontCN({
  scriptUrl: "//at.alicdn.com/t/c/font_4496065_0kkxhq4p8irl.js?spm=a313x.manage_type_myprojects.i1.10.5f543a81tF4H7z&file=font_4496065_0kkxhq4p8irl.js"
})

export default function ApiDetailTab({ id, api }: { id: number, api: Api }) {
  const pathname = usePathname();

  const menuItems = [
    { key: "introduction", label: "接口介绍", href: `/api/${id}/introduction` },
    { key: "api_document", label: "接口文档", href: `/api/${id}/api_document` },
    { key: "question", label: "常见问题", href: `/api/${id}/question` },
    { key: "openapi", label: "OpenAPI", href: `/api/${id}/openapi` },
  ];

  const getActiveKey = () => {
    const path = pathname;
    if (path.includes("/openapi")) return "openapi";
    if (path.includes("/question")) return "question";
    if (path.includes("/api_document")) return "api_document";
    if (path.includes("/introduction")) return "introduction";
    
    const basePath = `/api/${id}`;
    if (path === basePath || path === `${basePath}/`) {
      return "introduction";
    }
    
    return "introduction"; 
  };

  const activeKey = getActiveKey();

  return (
    <nav className="sticky top-0 z-50 bg-white border-b shadow-sm w-full">
      <div className="max-w-7xl mx-auto px-0 sm:px-6 lg:px-8">
        <div 
          className="
            flex items-center h-12
            overflow-x-auto whitespace-nowrap scrollbar-hide px-2
            md:overflow-visible md:justify-start
            md:px-0
          "
        >
          <div 
            className="
              flex items-center h-full
              space-x-2
              md:space-x-0
            "
          >
            {menuItems.map(item => (
              <Link
                key={item.key}
                href={item.href}
                className={`
                  shrink-0 h-full flex items-center px-4 text-sm font-medium border-b-2 
                  transition-colors duration-150 ease-in-out
                  ${activeKey === item.key
                    ? 'text-blue-500 border-blue-500'
                    : 'text-gray-700 border-transparent hover:text-blue-500'
                  }
                `}
              >
                {item.label}
              </Link>
            ))}
          </div>
        </div>
      </div>
    </nav>
  );
}