"use client"

import React from 'react';

import { Empty } from "antd";
import { ICONFONT } from '@/components/antd/icon';

const HomePage = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div className="flex flex-col items-start p-4 border">
        <div className="flex items-center gap-2 font-[18px] font-bold">
          <ICONFONT type="icon-kekaoanquan" style={{ fontSize: '24px' }}></ICONFONT>
          <div className="font-[24px]">可靠安全</div>
        </div>
        <div>
          <p className="text-[14px] text-[#4A4A4A]">
            提供高可用、低延迟的服务，实施严格的安全措施，确保业务和数据安全。
          </p>
        </div>
      </div>

      <div className="flex flex-col items-start p-4 border">
        <div className="flex items-center gap-2 font-[18px] font-bold">
          <ICONFONT type="icon-fuwuzhuanye1" style={{ fontSize: '24px' }}></ICONFONT>
          <div className="font-[24px]">服务专业</div>
        </div>
        <div>
          <p className="text-[14px] text-[#4A4A4A]">
            专业团队提供全方位的售前和售后服务，随时解答您的疑问。
          </p>
        </div>
      </div>

      <div className="flex flex-col items-start  p-4 border">
        <div className=" flex items-center gap-2 font-[18px] font-bold">
          <ICONFONT type="icon-shujuhegui" style={{ fontSize: '24px' }}></ICONFONT>
          <div className="font-[24px]">数据合规</div>
        </div>
        <div>
          <p className="text-[14px] text-[#4A4A4A]">
            严格遵守数据保护法规，确保您的数据安全合规。
          </p>
        </div>
      </div>

      <div className="flex flex-col items-start  p-4 border ">
        <div className="flex items-center gap-2 font-[18px] font-bold">
          <ICONFONT type="icon-linghuochixu" style={{ fontSize: '24px' }}></ICONFONT>
          <div className="font-[24px]">灵活持续</div>
        </div>
        <div>
          <p className="text-[14px] text-[#4A4A4A]">
            提供多样化的解决方案，并持续优化以满足您不断变化的业务需求。
          </p>
        </div>
      </div>
    </div>
  );
};
const Introduction = ({ introduction }: { introduction: string }) => {
  return <>
    <div className='mx-2'>
      <div className="pt-[30px]">
        <div className="markdown-body editormd-preview-container  ">
          <div>
            {introduction && <div className='prose' dangerouslySetInnerHTML={{ __html: introduction }}>
            </div>}
            {!introduction && <Empty description={'暂无内容'} />}
            <div className='mt-10'>
              <h3 className={'text-xl mb-2'}>服务保障</h3>
              {/*<p><Image*/}
              {/*  src="/images/service.png"*/}
              {/*  alt="" width={1000} height={1000}/>*/}
              {/*</p>*/}
              <HomePage />
            </div>
          </div>
        </div>
      </div>
    </div>
  </>
}
export default Introduction