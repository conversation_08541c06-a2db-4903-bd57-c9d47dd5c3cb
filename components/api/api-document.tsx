"use client"
import {Api, ApiNode} from "@/app/types";
import {Table, Tabs, Tag} from "antd";
import ApiDocumentDetail from "@/components/api/api-document-detail";
import {useEffect, useState} from "react";
import {getSystemCodes} from "@/app/actions";

const TabChild = () => {

}

const ApiDocument = ({nodes, id,systemCodes}: { nodes: ApiNode[], id: number;systemCodes:any[] }) => {

  const [tabs, setTabs] = useState([])


  useEffect(() => {
    const temp = []
    for (let node of nodes) {
      temp.push({
        key: node.id,
        label: <>
          {node.name}
          {node.is_free === "Y" && <Tag color="red">免费</Tag>}
        </>,
        children: <ApiDocumentDetail node={node} id={id} input={{}} key={node.id} show_test_api={false}/>
      })
    }
    // @ts-ignore
    setTabs(temp)
  }, [nodes]);


  return <>
    <div className={'m-h-[500px] mx-2'}>
      <div className={'pt-[30px] '}>
        <div className={'mb-10'} id={'api-document'}>
          <h2 className='text-xl mb-[10px]'>API 列表</h2>
          <div>
            <Tabs items={tabs}>
            </Tabs>
          </div>
        </div>
        <div className='mb-10 mt-10' id='httpStatus'>
          <h2 className='text-xl mb-[10px]'>
            系统级错误码参照
          </h2>
          <div>
            <Table rowKey={'code'} key={'code'} columns={[
              {
                title: "状态码",
                dataIndex: "code"
              },
              {
                title: "说明",
                dataIndex: "message"
              }
            ]} dataSource={systemCodes} size={'small'} pagination={false}></Table>
          </div>
        </div>

      </div>
    </div>
  </>
}
export default ApiDocument