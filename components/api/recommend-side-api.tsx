"use client"
import { Api } from "@/app/types";
import { apiRequest } from "@/lib/axios";
import { useEffect, useState } from "react";
import Link from 'next/link'

export function RecommendSideApi() {

  const [apis, setApis] = useState<Api[]>([]);
  const handleGetRecommendApi = () => {
    try {
      apiRequest<Api[]>("/frontend/api/get_side_recommend_api").then((res) => {
        setApis(res);
      });
    } catch (e) {
      console.log(e);
    }
  }

  useEffect(() => {
    handleGetRecommendApi()
  }, []);

  return <div className={'apis-list'}>
    {apis.length > 0 && apis.map((api) => (
      <Link
        href={`/api/${api.id}/api_document`}
        key={api.id}
        className={'flex w-[286px] mb-[10px] cursor-pointer hover:shadow-xs p-2 no-underline'}
      >
        <div className={'flex flex-row'}>
          <div style={{
            backgroundImage: `url(${api.img})`,
            width: "50px",
            height: "50px",
            backgroundSize: "cover",
            backgroundRepeat: "no-repeat",
            backgroundColor: "#f8fcff"
          }}></div>
        </div>
        <div className={'content flex justify-between flex-col pl-2'}>
          <div className={'flex flex-row items-center mb-[10px]'}>
            <p className={'text-[#333] font-bold truncate'} title={api.name}>{api.name}</p>
          </div>
          <div>
            <p className={'max-w-[220px] h-[40px] leading-[20px] text-[#666] text-xs truncate ...'}
              title={api.description}>{api.description}</p>
          </div>
        </div>
      </Link>
    ))}
  </div>
}