"use client"

import React, { CSSProperties } from 'react';

import { Collapse, CollapseProps, Empty, theme } from "antd";
import { ICONFONT } from '@/components/antd/icon';
import { CaretRightOutlined } from '@ant-design/icons';
import Link from 'next/link';

const HomePage = () => {
    return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex flex-col items-start p-4 border">
                <div className="flex items-center gap-2 font-[18px] font-bold">
                    <ICONFONT type="icon-kekaoanquan" style={{ fontSize: '24px' }}></ICONFONT>
                    <div className="font-[24px]">可靠安全</div>
                </div>
                <div>
                    <p className="text-[14px] text-[#4A4A4A]">
                        提供高可用、低延迟的服务，实施严格的安全措施，确保业务和数据安全。
                    </p>
                </div>
            </div>

            <div className="flex flex-col items-start p-4 border">
                <div className="flex items-center gap-2 font-[18px] font-bold">
                    <ICONFONT type="icon-fuwuzhuanye1" style={{ fontSize: '24px' }}></ICONFONT>
                    <div className="font-[24px]">服务专业</div>
                </div>
                <div>
                    <p className="text-[14px] text-[#4A4A4A]">
                        专业团队提供全方位的售前和售后服务，随时解答您的疑问。
                    </p>
                </div>
            </div>

            <div className="flex flex-col items-start  p-4 border">
                <div className=" flex items-center gap-2 font-[18px] font-bold">
                    <ICONFONT type="icon-shujuhegui" style={{ fontSize: '24px' }}></ICONFONT>
                    <div className="font-[24px]">数据合规</div>
                </div>
                <div>
                    <p className="text-[14px] text-[#4A4A4A]">
                        严格遵守数据保护法规，确保您的数据安全合规。
                    </p>
                </div>
            </div>

            <div className="flex flex-col items-start  p-4 border ">
                <div className="flex items-center gap-2 font-[18px] font-bold">
                    <ICONFONT type="icon-linghuochixu" style={{ fontSize: '24px' }}></ICONFONT>
                    <div className="font-[24px]">灵活持续</div>
                </div>
                <div>
                    <p className="text-[14px] text-[#4A4A4A]">
                        提供多样化的解决方案，并持续优化以满足您不断变化的业务需求。
                    </p>
                </div>
            </div>
        </div>
    );
};
const Question = () => {
    const text = `
    A dog is a type of domesticated animal.
    Known for its loyalty and faithfulness,
    it can be found as a welcome guest in many households across the world.
  `;
    const { token } = theme.useToken();

    const getItems: (panelStyle: CSSProperties) => CollapseProps['items'] = (panelStyle) => [
        {
            key: '1',
            label: '免费接口、会员接口、计次接口的区别是什么？',
            children: <>
                <p>免费接口：免费接口是免费的，不需要任何费用，但是需要注册账号，注册账号后可以免费使用。</p>
                <p>会员接口：会员专属接口，需要开通会员之后才能申请使用,如果会员过期后，接口将无法使用。</p>
                <p>计次接口：计次接口需要单独购买次数后才可以正常使用，一般都有免费体验次数，计次接口可以免费申请，但是次数有限，需要单独购买次数。</p>
            </>,
            style: panelStyle,
        },
        {
            key: '2',
            label: '接口怎么才是申请成功了?',
            children: <p>如果已经申请成功，接口文档页面会显示已申请，并且可以在我的接口里面查看已经申请的所有接口</p>,
            style: panelStyle,
        },
        {
            key: '3',
            label: 'Token 是什么',
            children: <>
                <p> token 是调用接口的凭证，需要在 <Link href="/dashboard/data/token">Token管理</Link> 里面创建, token 可以限制使用的接口，设置IP白名单，来源白名单等 </p>
                <p> token 在请求接口时支持通过参数和请求头两种方式传递，参数名称为：token </p>
            </>,
            style: panelStyle,
        },
        {
            key: '4',
            label: '接口的QPS是什么？',
            children: <p>QPS 是每秒查询次数，如果超过 QPS 限制，会停用 1秒 后才可以正常请求</p>,
            style: panelStyle,
        }
    ];

    const panelStyle: React.CSSProperties = {
        marginBottom: 24,
        background: token.colorFillAlter,
        borderRadius: token.borderRadiusLG,
        border: 'none',
    };
    return <>
        <div className='mx-2'>
            <div className="pt-[30px]">
                <div className="markdown-body editormd-preview-container  ">
                    <div>
                        <div>
                            <Collapse
                                bordered={false}
                                defaultActiveKey={['1']}
                                expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
                                style={{ background: token.colorBgContainer }}
                                items={getItems(panelStyle)}
                            />
                        </div>
                        <HomePage />
                    </div>
                </div>
            </div>
        </div>
    </>
}
export default Question