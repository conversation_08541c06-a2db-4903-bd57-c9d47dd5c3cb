"use client"
import React, { useState, useEffect } from 'react';
import { Card, Empty, Tag } from 'antd';
import { apiRequest } from "@/lib/axios";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { PlanColor } from '@/config/data';

export function ExploreService({ search = false, keywords = '', category_id = '' }) {
  const [selectedItem, setSelectedItem] = useState('');
  const [menuItems, setMenuItems] = useState([]);
  const [categoryApis, setCategoryApis] = useState([]);
  const [expandedCategory, setExpandedCategory] = useState(null);
  const [searchApiList, setSearchApiList] = useState([])
  const [searchRecommendApiList, setSearchRecommendApiList] = useState([])

  const loadExploreApis = () => {
    apiRequest(`/frontend/api/explore`).then((res) => {
      const { categories, apis } = res;
      const tempMenu = categories.map(category => ({
        id: category.id,
        label: `${category.name}(${category.apis_count})`
      }));
      setMenuItems(tempMenu);
      setCategoryApis(apis);
    });
  };

  const searchApis = (keywords: string) => {
    apiRequest(`/frontend/api/search`, "GET", {}, { "keywords": keywords }).then((res) => {
      const { list, recommend, categories } = res as any
      const tempMenu = categories.map((category: any) => ({
        id: category.id,
        label: `${category.name}(${category.apis_count})`
      }));
      setMenuItems(tempMenu)
      setSearchApiList(list)
      setSearchRecommendApiList(recommend)
    });
  }

  useEffect(() => {
    if (search) {
      searchApis(keywords)
    } else if (category_id) {
      setSelectedItem(Number(category_id));
      setExpandedCategory(Number(category_id));
      loadExploreApis();
    } else {
      loadExploreApis()
    }
  }, [search, keywords]);

  const handleCategoryClick = (categoryId: any) => {
    console.log(categoryId)
    setSelectedItem(categoryId);
    if (categoryId !== 'all') {
      setExpandedCategory(categoryId);
    } else {
      setExpandedCategory(null)
    }
  };

  const renderApis = (apis, categoryId, category) => {
    const isExpanded = expandedCategory == categoryId;
    const displayedApis = isExpanded ? apis : apis.slice(0, 3);


    return (
      <>
        {displayedApis.map((api) => (
          <APICard key={api.id} api={api} />
        ))}
        {!isExpanded && apis.length > 3 && (
          <div
            className="cursor-pointer underline-offset-1	underline "
            onClick={() => handleCategoryClick(categoryId)}
          >
            查看全部 {apis.length} 个 {category}API
          </div>
        )}
      </>
    );
  };

  return (
    <div className='mb-20'>
      <div className="flex flex-col md:flex-row">
        {/* 移动端菜单 */}
        <div className="md:hidden w-full overflow-x-auto mb-5">
          <div className="flex space-x-4 p-2">
            {menuItems.map((item) => (
              <div
                key={item.id}
                onClick={() => handleCategoryClick(item.id)}
                className={`px-4 py-2 whitespace-nowrap cursor-pointer ${selectedItem === item.id
                  ? 'text-blue-500 font-bold'
                  : 'text-gray-700 hover:text-blue-500'
                  }`}
              >
                {item.label}
              </div>
            ))}
          </div>
        </div>

        {/* 桌面端菜单 */}
        <div className="hidden md:block md:w-[180px] md:mr-6">
          <div className="space-y-2">
            {menuItems.map((item) => (
              <div
                key={item.id}
                onClick={() => handleCategoryClick(item.id)}
                className={`w-full px-4 py-2 cursor-pointer ${selectedItem === item.id
                  ? 'text-blue-500 font-bold'
                  : 'text-gray-700 hover:text-blue-500'
                  }`}
              >
                {item.label}
              </div>
            ))}
          </div>
        </div>

        <div className='w-full'>
          <div className='border-0 border-l-[1px] border-solid pl-[24px] lg:pr-0 pr-[24px]'>
            {/* 搜索结果 */}
            {search && <SearchResults keywords={keywords} apis={searchApiList} />}

            {/* 分类API */}
            {!search && categoryApis.map((categoryApi) => {
              if (expandedCategory) {
                return expandedCategory === categoryApi.category_id &&
                  <div key={categoryApi.category_id} className='mb-[60px]'>
                    <div className="mb-[15px] mt-0 text-[20px] font-bold">{categoryApi.category}</div>
                    <div className='grid grid-cols-1 md:grid-cols-3 gap-5'>
                      {renderApis(categoryApi.apis, categoryApi.category_id, categoryApi.category)}
                    </div>
                  </div>
              } else {
                return <div key={categoryApi.category_id} className='mb-[60px]'>
                  <div className="mb-[15px] mt-0 text-[20px] font-bold">{categoryApi.category}</div>
                  <div className='grid grid-cols-1 md:grid-cols-3 gap-5'>
                    {renderApis(categoryApi.apis, categoryApi.category_id, categoryApi.category)}
                  </div>
                </div>
              }
            })}
          </div>
        </div>
      </div>
    </div>
  );
}

function APICard({ api }) {
  const route = useRouter()
  return (
    <Card onClick={() => {
      route.push(`/api/${api.id}/introduction`)
    }} className='w-full cursor-pointer p-0 hover:border-blue-300 rounded' hoverable={true} styles={{
      body: { padding: 0 }
    }}>
      <div className='w-[320px]'>
        <div className='flex flex-row p-[20px]'>
          <div className='flex-shrink-0 w-[50px] h-[50px] bg-center bg-cover bg-no-repeat rounded' style={{
            backgroundImage: `url(${api.img})`,
          }} />

          <div className='ml-4 flex flex-col justify-center'>
            <h3 className='text-[#333] font-bold truncate' title={api.name}>{api.name}</h3>
            <div className='mb-[10px]'>
              {api.type == 1 ?
                <Tag color={PlanColor.free}>免费接口</Tag> : (api.type == 2 ? <Tag color={PlanColor.vip}>会员接口</Tag> :
                  <Tag color={PlanColor.pro}>计次接口</Tag>)}
              {api.tags && api.tags.length > 0 && api.tags.map((tag, index) => (
                <Tag key={index} className={'mr-1'} bordered={false} color={'blue-inverse'}>{tag}</Tag>))}
            </div>
            <p className='text-xs h-[30px] text-clip overflow-hidden ...' title={api.description}>
              {api.description}
            </p>
          </div>
        </div>
      </div>
    </Card>
  );
}

function SearchResults({ keywords, apis }) {
  return (
    <div className='mb-10'>
      <div className="mb-[15px] mt-0 text-[20px] font-bold">搜索结果: {keywords}</div>
      {/* 这里应该渲染搜索结果 */}
      <div className='grid gap-y-5'>
        {apis && apis.length > 0 && apis.map((api) => (
          <div key={api.id}>
            <Link href={`/api/${api.id}/api_document`}> <Card
              className='w-full cursor-pointer p-0 hover:border-blue-300 rounded' hoverable={true}>
              <div className='flex'>
                <div className='flex flex-row'>
                  <div className=' w-[50px] h-[50px] text-center bg-center bg-cover	bg-no-repeat	rounded '
                    style={{
                      backgroundImage: `url(${api.img})`,
                    }}>
                  </div>
                </div>
                <div className='api-content box-border flex flex-col justify-center pl-[12px]'>
                  <div className='flex flex-row items-center mb-[10px]'>
                    <p className='text-[#333] font-bold truncate'
                      title={api.name}>{api.name}</p>
                  </div>
                  <div className='mb-[10px]'>
                  </div>
                  <div className=''>
                    <p
                      className='text-xs  h-[30px] text-clip overflow-hidden ...'
                      title={api.description}
                    >
                      {api.description}
                    </p>
                  </div>
                </div>
              </div>
            </Card></Link>
          </div>
        ))}
        {(!apis || apis.length == 0) && <div><Empty description={'未搜索到相关接口'}></Empty></div>}
      </div>
      {/*<div>显示推荐接口</div>*/}
    </div>
  );
}