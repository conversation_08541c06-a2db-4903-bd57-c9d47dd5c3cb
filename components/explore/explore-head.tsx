export function ExploreHead() {
  return <div>
    <div className="relative bg-no-repeat  flex flex-row flex-wrap mb-[10px] ">
      <div className="max-w-6xl 	lg:pt-[136px] pt-[50px]">
        <h1 className="lg:text-[48px] text-xl mb-[24px] text-center lg:text-left">
          全部接口
          <mark className="bg-transparent text-primary lg:ml-[29px] ml-[10px]">免费试用</mark>
        </h1>
        <p className="text-[20px] mb-[35px] text-center">为数百万开发者提供专业的 API 服务，让您无忧探索广阔的 API
          世界</p>
        <div className="flex flex-row text-[16px] text-primary">
          <div className="lg:mr-[25px] mr-[10px]">
            <mark
              className="bg-transparent inline-block h-[20px] w-[20px] align-bottom bg-[url('/images/svg/checked.svg')] "></mark>
            <span className="text-sm">严选多品类 API</span>
          </div>
          <div className="lg:mr-[25px]  mr-[10px]">
            <mark
              className="bg-transparent inline-block h-[20px] w-[20px] align-bottom bg-[url('/images/svg/checked.svg')] "></mark>
            <span className="text-sm">一分钟快速接入</span>
          </div>
          <div className="lg:mr-[25px]  mr-[10px]">
            <mark
              className="bg-transparent inline-block h-[20px] w-[20px] align-bottom bg-[url('/images/svg/checked.svg')] "></mark>
            <span className="text-sm">专业服务保障</span>
          </div>
        </div>
      </div>
      <div
        className="lg:block flex-1  hidden h-[400px] bg-no-repeat bg-center bg-[url('/images/svg/explorer_bg.svg')]
           ml-20s
           mt-[20px]"></div>
    </div>
  </div>
}