"use client"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Di<PERSON>r, Drawer, Input, <PERSON>u, Row, Space } from "antd";
import Link from "next/link";
import React, { useEffect, useState } from "react";
import { MenuOutlined } from "@ant-design/icons";
import IconButton from "@/components/common/icon-button";
import { NavbarUser } from "@/components/common/navbar-user";
import { usePathname, useRouter } from "next/navigation";
import { useSiteConfig } from "@/components/provider/site-provider";
import { useAuthModal } from "@/components/provider/auth-modal-context";

const defaultMenuItems = [
  {
    key: '/',
    label: '首页',
    path: "/"
  },
  {
    key: '/explore',
    label: '探索',
    path: "/explore"
  },
  {
    key: '/vip',
    label: "VIP会员",
    path: "/vip"
  }
]

export function MarketingHeader({ user, menuList }: { user?: any, menuList?: any[] }) {
  const router = useRouter()
  const pathname = usePathname()
  const [activeKey, setActiveKey] = useState()

  const [menuOpen, setMenuOpen] = useState(false);
  const [menu, setMenu] = useState([])

  useEffect(() => {
    //@ts-ignore
    setActiveKey(pathname)
  }, [pathname])

  const siteConfig = useSiteConfig()
  const { openModal } = useAuthModal() as { openModal: () => void };

  return <>
    <Affix style={{ zIndex: 99999 }} className='bg-white'>
      <div>
        <div className={'bg-white'}>
          <Row
            justify={'space-between'}
            align="middle"
            className='leading-[64px] max-h-[64px] w-full max-w-6xl mx-auto bg-white px-0'
          >
            <Col flex="none" className={'block lg:hidden pl-4'}>
              <IconButton className={'mx-1'} size={'small'} icon={<MenuOutlined />}
                onClick={() => setMenuOpen(true)} />
              <Drawer closable={false} style={{ padding: 0 }} placement={'left'} width={200} title={null} footer={null}
                open={menuOpen}
                onClose={() => setMenuOpen(false)}>
                <Menu
                  //@ts-ignore
                  selectedKeys={[activeKey]} style={{ border: 0 }} mode={"vertical"} className={'text-lg'}
                  items={menuList || defaultMenuItems} onClick={(item) => {
                    if (item.key.includes("http")) {
                      window.open(item.key, '_blank')
                    } else {
                      router.push(item.key)
                    }
                  }}>
                </Menu>
              </Drawer>
            </Col>

            <Col flex="1" className={'block lg:hidden text-center'}>
              <Link href='/'>
                {siteConfig.site_logo ?
                  <img className={'h-[32px] max-h-[32px] inline-block'} alt={siteConfig.site_name}
                    src={siteConfig.site_logo} />
                  : <span className="font-semibold text-lg inline-block">{siteConfig.site_name}</span>
                }
              </Link>
            </Col>

            <Col flex="none" className={'block lg:hidden pr-4'}>
              <div className='text-right'>
                <Space>
                  {user ? (
                    <>
                      <NavbarUser user={user} menu={true} />
                    </>
                  ) : (<Button type={'primary'} key={'login'} size="small" onClick={() => {
                    openModal()
                  }}>登录</Button>)}
                </Space>
              </div>
            </Col>

            <Col flex="none" className="hidden lg:flex items-center">
              <Link href='/'>
                {siteConfig.site_logo ?
                  <img className={'h-[32px] max-h-[32px]'} alt={siteConfig.site_name}
                    src={siteConfig.site_logo} /> : siteConfig.site_name}
              </Link>
            </Col>

            <Col flex="auto" className='hidden lg:block'>
              <Menu
                //@ts-ignore
                selectedKeys={[activeKey]} style={{ border: 0 }} mode="horizontal" className={'ml-5'} items={menuList || defaultMenuItems}
                onClick={(item) => {
                  if (item.key.includes("http")) {
                    window.open(item.key, '_blank')
                  } else {
                    router.push(item.key)
                  }
                }}>
              </Menu>
            </Col>

            <Col flex="none" className="hidden md:block lg:flex-initial px-2">
              <Input placeholder="输入接口名称快速搜索" onPressEnter={(e) => {
                router.push(`/explore?search=Y&keyword=${(e.target as HTMLInputElement).value}`)
              }} />
            </Col>

            <Col flex="none" className="hidden lg:block lg:flex-initial">
              <div className='float-right'>
                <Space>
                  {user ? (
                    <>
                      <Link href={`/dashboard/data/api`}><Button type="primary">我的API</Button></Link>
                      <NavbarUser user={user} menu={true} />
                    </>
                  ) : (<Button type={'primary'} key={'login'} onClick={() => {
                    openModal()
                  }}>登录</Button>)}
                </Space>
              </div>
            </Col>
          </Row>
        </div>
        <Divider className='p-0 m-0' />
      </div>
    </Affix>
  </>
}