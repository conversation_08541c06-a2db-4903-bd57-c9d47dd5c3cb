import {Modal} from "antd";
import {ModalForm, ProFormItem, ProFormText} from "@ant-design/pro-components";
import Image from "next/image";
import {useEffect, useState} from "react";
import {getCaptcha} from "@/lib/api";

export interface ImageCaptchaValueProps {
  key: string
  code: string
}

const ImageCaptcha = ({open, setOpen, onChange}: {
  open: boolean
  setOpen: (v: boolean) => void
  onChange?: (v: ImageCaptchaValueProps) => void
}) => {
  const [loading, setLoading] = useState(false)
  const [captcha, setCaptcha] = useState({
    captcha: "",
    key: "",
  })
  const handleGetCaptcha = async () => {
    const res = await getCaptcha()
    // @ts-ignore
    setCaptcha(res)
  }
  useEffect(() => {
    if (open) {
      setLoading(true)
      handleGetCaptcha().then(r => setLoading(false))
    }

  }, [open])

  return <>
    <ModalForm clearOnDestroy loading={loading} modalProps={{
      destroyOnClose: true,
    }} onFinish={async (v) => {
      // @ts-ignore
      onChange && onChange({
        key: captcha.key,
        code: v.code,
      })
      setOpen(false)
    }} width={300} title={'请填写验证码'} open={open} onOpenChange={setOpen}>
      <ProFormItem>
        <Image onClick={handleGetCaptcha} width={150} height={40}
               src={captcha.captcha}
               alt={'验证码'}/>
      </ProFormItem>
      <ProFormText name={'code'} placeholder={'请输入验证码'} required={true}
                   rules={[{required: true, message: "请输入验证码"}]}>
      </ProFormText>
    </ModalForm>
  </>
}

export default ImageCaptcha