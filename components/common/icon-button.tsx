"use client"

import React, {forwardRef} from 'react';
import clsx from "clsx";
import {Button, ButtonProps} from "antd";
import '@/style/api-check-button.css'

function IconButton(props: ButtonProps, ref: React.ForwardedRef<HTMLButtonElement>) {
  const {icon, className, ...rest} = props;

  return (
    <Button
      ref={ref}
      icon={icon}
      shape="circle"
      className={clsx('icon-button', 'text-base border border-solid border-primary', className)}
      {...rest}
    />
  );
}

export default forwardRef(IconButton);
