"use client"
import {
  Capt<PERSON>ieldRef,
  <PERSON>ginForm,
  ProConfigProvider, ProForm,
  ProFormCaptcha,
  ProFormText,
  setAlpha,
} from '@ant-design/pro-components';
import {Divider, message, Modal, Space, Tabs, theme, Typography} from "antd";
import {CSSProperties, Suspense, useEffect, useRef, useState} from "react";
import {
  AlipayCircleOutlined,
  LockOutlined,
  MobileOutlined,
  TaobaoCircleOutlined,
  WeiboCircleOutlined
} from "@ant-design/icons";
import {signIn} from "next-auth/react";
import ImageCaptcha, {ImageCaptchaValueProps} from "@/components/common/image-captcha";
import {forgetPassword, sendSms} from "@/lib/api";
import {useSiteConfig} from "@/components/provider/site-provider";
import {SiteConfig} from "@/app/types";
import {useGeeTest} from "react-geetest-v4";
import {useRouter, useSearchParams} from "next/navigation";
import {useAuthModal} from "@/components/provider/auth-modal-context";
import Link from "next/link";
import <PERSON>gin<PERSON>orm<PERSON>om<PERSON> from "@/components/common/login-form";

export default function AuthLoginModal() {
  const {isOpen: visible, closeModal} = useAuthModal();


  return <>
    <Suspense fallback={<div>加载中...</div>}>
      <Modal className='lg:!w-[760px] w-full' footer={null} open={visible} onCancel={() => closeModal()}
             maskClosable={false}>
        <div className='flex h-full'>
          <div className='flex-none border-r hidden lg:block'>
            <div className="w-[300px] pl-10 pt-[35px]"><h3 className="text-xl mb-[30px]">为什么选择 ALAPI?</h3>
              <div className="reason mb-5">
                <div className="icon text-2xl mb-[5px]">🎁</div>
                <p className="blue-text leading-[16px] font-bold">提供数百个免费API接口，</p><p>让您快速、低成本接入
                API。</p></div>
              <div className="reason mb-5">
                <div className="icon text-2xl mb-[5px]">🚀</div>
                <p className="leading-[16px]"><span className="blue-text font-bold">API管理、测试、鉴权、日志</span>等功能，</p>
                <p>让您无忧探索广阔的API世界~</p></div>
              <div className="reason">
                <div className="icon text-2xl mb-[5px]">🔥</div>
                <p className="leading-[16px]"><span className="blue-text font-bold">国内外一线机构投资</span>，让我们能够专注
                </p><p>于为用户提供更优质的服务。</p></div>
            </div>
          </div>
          <div className='flex-1'>
            <LoginFormCommon handleOk={() => {
              closeModal()
            }}></LoginFormCommon>
          </div>
        </div>
      </Modal>
    </Suspense>
  </>
}