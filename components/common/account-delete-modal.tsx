"use client"

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, message } from "antd";
import { useState, useRef, useEffect } from "react";
import {
  ProForm,
  ProFormTextArea,
  ProFormCaptcha,
  CaptFieldRef
} from "@ant-design/pro-components";
import { LockOutlined, ExclamationCircleOutlined } from "@ant-design/icons";
import { sendSms } from "@/lib/api";
import ImageCaptcha from "@/components/common/image-captcha";
import { useSiteConfig } from "@/components/provider/site-provider";
import { useGeeTest } from "react-geetest-v4";

interface AccountDeleteModalProps {
  open: boolean;
  onCancel: () => void;
  onConfirm: (data: { reason: string; code: string }) => Promise<void>;
  userPhone?: string;
}

const AccountDeleteModal: React.FC<AccountDeleteModalProps> = ({
  open,
  onCancel,
  onConfirm,
  userPhone
}) => {
  const [form] = ProForm.useForm();
  const [loading, setLoading] = useState(false);
  const [imageCaptchaOpen, setImageCaptchaOpen] = useState(false);
  const captchaRef = useRef<CaptFieldRef>();
  const siteConfig = useSiteConfig();

  // 初始化极验验证码
  // @ts-ignore
  const { captcha, state } = useGeeTest(siteConfig.geetest_captcha_id || "", {
    product: 'bind',
    protocol: 'https://',
    //@ts-ignore
    containerId: 'geetest-captcha-delete',
    onError: function (error) {
      message.error(error.msg)
    },
  });

  // 处理极验验证码成功回调
  useEffect(() => {
    if (state === 'ready') {
      captcha?.onSuccess(async () => {
        const result = captcha?.getValidate()
        void handleSendSms(result)
      })
    }
  }, [state])

  // 处理图形验证码验证后发送短信
  const handleSendSms = async (captchaData: any) => {
    try {
      await sendSms({
        phone: userPhone,
        event: "deactivate",
        captcha: captchaData,
      });
      message.success("验证码发送成功");
      captchaRef.current?.startTiming();
    } catch (e: any) {
      message.error(e.message || "验证码发送失败");
    }
  };

  // 显示验证码 - 根据配置类型显示不同的验证码
  const handleShowCaptcha = async () => {
    if (siteConfig.captcha_type === 'image') {
      setImageCaptchaOpen(true);
    } else if (siteConfig.captcha_type === 'geetest') {
      captcha?.showCaptcha();
    }
    throw new Error("wait"); // 阻止ProFormCaptcha的默认行为
  };

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      await onConfirm({
        reason: values.reason,
        code: values.code
      });
      form.resetFields();
    } catch (e: any) {
      message.error(e.message || "注销失败");
    } finally {
      setLoading(false);
    }
  };

  // 处理取消
  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <>
      <Modal
        title={
          <div className="flex items-center gap-2">
            <ExclamationCircleOutlined className="text-red-500" />
            <span>账号注销确认</span>
          </div>
        }
        open={open}
        onCancel={handleCancel}
        footer={null}
        width={600}
        destroyOnClose
      >
        <div className="py-4">
          {/* 警告信息 */}
          <Alert
            message="重要提醒"
            description={
              <div className="space-y-2">
                <p>• 账号注销后账号不可找回</p>
                <p>• 账号已有资产将全部失效清空，包括但不限于VIP套餐、付费接口次数等</p>
                <p>• 请确认您已了解注销后果，并谨慎操作</p>
              </div>
            }
            type="error"
            showIcon
            className="mb-6"
          />

          {/* 注销表单 */}
          <ProForm
            form={form}
            onFinish={handleSubmit}
            submitter={{
              render: () => (
                <div className="flex justify-end gap-2 mt-6">
                  <Button onClick={handleCancel}>
                    取消
                  </Button>
                  <Button 
                    type="primary" 
                    danger 
                    loading={loading}
                    onClick={() => form.submit()}
                  >
                    确认注销
                  </Button>
                </div>
              ),
            }}
          >
            {/* 注销原因 */}
            <ProFormTextArea
              name="reason"
              label="注销原因"
              placeholder="请详细说明您注销账号的原因，以便我们改进服务"
              rules={[
                { required: true, message: "请填写注销原因" },
                { min: 10, message: "注销原因至少需要10个字符" }
              ]}
              fieldProps={{
                rows: 4,
                maxLength: 500,
                showCount: true
              }}
            />

            {/* 短信验证码 */}
            <ProFormCaptcha
              fieldRef={captchaRef}
              phoneName="phone"
              label="短信验证码"
              name="code"
              placeholder="请输入短信验证码"
              fieldProps={{
                prefix: <LockOutlined className="text-gray-400" />,
              }}
              captchaProps={{
                // size: "large",
              }}
              captchaTextRender={(timing, count) => {
                if (timing) {
                  return `${count}秒后重新获取`;
                }
                return "获取验证码";
              }}
              rules={[
                { required: true, message: "请输入短信验证码" }
              ]}
              onGetCaptcha={handleShowCaptcha}
            />
          </ProForm>

          {/* 极验验证码容器 */}
          <div id="geetest-captcha-delete" style={{ display: 'none' }}></div>
        </div>
      </Modal>

      {/* 图形验证码Modal */}
      <ImageCaptcha
        open={imageCaptchaOpen}
        setOpen={setImageCaptchaOpen}
        onChange={handleSendSms}
      />
    </>
  );
};

export default AccountDeleteModal;
