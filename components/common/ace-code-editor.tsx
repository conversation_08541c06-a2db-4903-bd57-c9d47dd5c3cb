"use client"
import AceEditor from "react-ace";
import "ace-builds/src-noconflict/mode-json";
import "ace-builds/src-noconflict/mode-html";
import "ace-builds/src-noconflict/mode-text";
import "ace-builds/src-noconflict/mode-java";
import "ace-builds/src-noconflict/mode-json";
import "ace-builds/src-noconflict/mode-javascript";
import "ace-builds/src-noconflict/mode-php";
import "ace-builds/src-noconflict/mode-golang";
import "ace-builds/src-noconflict/mode-c_cpp";
import "ace-builds/src-noconflict/mode-csharp";
import "ace-builds/src-noconflict/mode-python";
import "ace-builds/src-noconflict/mode-ruby";
import "ace-builds/src-noconflict/mode-rust";
import "ace-builds/src-noconflict/mode-sh";
import "ace-builds/src-noconflict/theme-monokai";
import "ace-builds/src-noconflict/theme-github";
import "ace-builds/src-noconflict/ext-language_tools";
import {Button, message, Space} from "antd";
import {CopyOutlined, DownloadOutlined, ExportOutlined, SearchOutlined} from "@ant-design/icons";
import "ace-builds/src-min-noconflict/ext-searchbox"
import {useEffect, useRef} from "react";
// @ts-ignore
import {CopyToClipboard} from 'react-copy-to-clipboard';

export function AceCodeEditor({code, mode = "javascript", theme = "monokai"}: {
  code: string,
  mode: string,
  theme: string
}) {
  const name = "AceCode-" + mode
  const editorRef = useRef()

  useEffect(() => {
    // @ts-ignore
    const editor = editorRef?.current.editor;

    // 绑定快捷键（例如 Ctrl-F 或 Cmd-F）以显示搜索框
    editor.commands.addCommand({
      name: 'showSearchBox',
      bindKey: {win: 'Ctrl-F', mac: 'Command-F'},
      exec: () => {
        editor.execCommand('find');
      }
    });
  }, []);
  return <>
    <div className={'border flex leading-10 h-10 justify-start items-center px-5'}>
      <Space>
        <CopyToClipboard text={code} onCopy={() => {
          message.success("复制成功")
        }}>
          <Button className={'text-black hover:text-blue-500'} size={'small'} type={'link'}
                  icon={<CopyOutlined/>} onClick={() => {
          }}>复制</Button>
        </CopyToClipboard>

        <Button className={'text-black hover:text-blue-500'} size={'small'} type={'link'}
                icon={<DownloadOutlined/>} onClick={() => {
          const blob = new Blob([code], {type: "text/plain"})
          const link = document.createElement('a');
          const url = URL.createObjectURL(blob);
          // 设置链接的href属性
          link.href = url;
          link.download = 'code.txt';
          link.click();
          URL.revokeObjectURL(url);
        }}>下载</Button>
        <Button className={'text-black hover:text-blue-500'} size={'small'} type={'link'}
                icon={<ExportOutlined/>} onClick={() => {
          const newWindow = window.open('', '_blank')
          if (newWindow) {
            newWindow.document.write(code)
          }
        }}>新开标签</Button>
        <Button className={'text-black hover:text-blue-500'} size={'small'} type={'link'}
                icon={<SearchOutlined/>} onClick={() => {
          // @ts-ignore
          editorRef?.current.editor.execCommand('find')
        }}>搜索</Button>
      </Space>
    </div>
    <AceEditor
      //@ts-ignore
      ref={editorRef}
      width='100%'
      mode={mode}
      theme={theme}
      name={name}
      editorProps={{$blockScrolling: true}}
      className='w-full'
      value={code}
      setOptions={{
        useWorker: false
      }}
    />
  </>
}