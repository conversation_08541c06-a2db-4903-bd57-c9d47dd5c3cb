"use client"

import {LogoutOutlined, SettingOutlined, UserOutlined} from '@ant-design/icons';
import {Avatar} from 'antd';
import type {MenuInfo} from 'rc-menu/lib/interface';
import React, {useCallback} from 'react';
import HeaderDropdown from '@/components/common/header-dropdown';
import Link from "next/link";
import {signOut} from "next-auth/react";
import {useRouter} from "next/navigation";
import {apiRequest} from "@/lib/axios";
import Cookies from "js-cookie";
import {AccessTokenKey} from "@/config/data";
import { ICONFONT } from '../antd/icon';

export type GlobalHeaderRightProps = {
  menu?: boolean;
  children?: React.ReactNode;
};
export const NavbarUser: React.FC<GlobalHeaderRightProps> = ({menu, user}) => {

  const router = useRouter()

  const onMenuClick = useCallback(
    async (event: MenuInfo) => {
      if (event.key === 'logout') {
        await apiRequest("/frontend/auth/logout")
        Cookies.remove(AccessTokenKey)
        await signOut({
          callbackUrl: "/"
        })
        return
      }
      router.push(event.key)
    },
    [],
  );

  const menuItems = [
    ...(menu
      ? [
        {
          key: '/dashboard',
          icon: <UserOutlined/>,
          label: '控制台',
        },
        {
          key: '/dashboard/account/index',
          icon: <SettingOutlined/>,
          label: '个人中心',
        },
        {
          key: '/dashboard/data/api',
          icon: <ICONFONT type="icon-API" style={{ fontSize: "16px" }} size={24} />,
          label: 'API管理',
        },
        {
          key: '/dashboard/data/token',
          icon: <ICONFONT type="icon-API-KEY" style={{ fontSize: "16px" }} size={24} />,
          label: 'token管理',
        },
        {
          type: 'divider' as const,
        },
      ]
      : []),
    {
      key: 'logout',
      icon: <LogoutOutlined/>,
      label: '退出登录',
    },
  ];
  return (
    <HeaderDropdown
      menu={{
        selectedKeys: [],
        onClick: onMenuClick,
        //@ts-ignore
        items: menuItems,
      }}
      trigger={["click"]}
    >
      <Avatar className={'cursor-pointer'} src={'https://gw.alipayobjects.com/zos/antfincdn/efFD%24IOql2/weixintupian_20170331104822.jpg'}>{user.name}</Avatar>
    </HeaderDropdown>
  );
};