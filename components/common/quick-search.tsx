"use client"
import {Input, Space} from "antd";
import {useRouter} from 'next/navigation'
import Link from "next/link";
import {useEffect, useState} from "react";
import {Api} from "@/app/types";
import {apiRequest} from "@/lib/axios";

const InputSearch = Input.Search

export function QuickSearch({keyword = ''}: {
  keyword?: string
}) {
  const router = useRouter()
  const [apis, setApis] = useState<Api[]>()

  const onSearch = async (e: string) => {
    let search = "N"
    if (e) {
      search = "Y"
    }
    router.push(`/explore?search=${search}&keyword=${e}`)
  }
  useEffect(() => {
    apiRequest("/frontend/api/get_search_recommend_api").then((res) => {
      //@ts-ignore
      setApis(res)
    }).catch(err => {
      console.log(err)
    })
  }, []);

  return <>
    <div className='quick-search bg-white'>
      <div>
        <InputSearch
          enterButton='搜索API'
          placeholder='输入API名称快速搜索'
          size='large'
          onSearch={onSearch}
          defaultValue={keyword ?? ''}
        />
      </div>
      <div className='mt-5'>
        <Space align={'center'} size='large' wrap>
          {apis && apis.length > 0 && apis.map((api) => (
            <Link key={api.id} href={`/api/${api.id}/api_document`} className='text-current'>{api.name}</Link>))}
        </Space>
      </div>
    </div>
  </>
}