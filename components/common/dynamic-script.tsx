"use client"

import {useEffect} from "react";
import {SiteConfig} from "@/app/types";

export default function DynamicScript({siteConfig}:{siteConfig:SiteConfig}){
  useEffect(() => {
    if(siteConfig.site_script){
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = siteConfig.site_script;

      const scripts = tempDiv.getElementsByTagName('script');
      const promises = [];

      for (let i = 0; i < scripts.length; i++) {
        const newScript = document.createElement('script');

        if (scripts[i].src) {
          // 如果有 src 属性，创建一个 src 脚本
          newScript.src = scripts[i].src;

          // 创建一个加载完成的 Promise
          promises.push(new Promise((resolve, reject) => {
            newScript.onload = resolve; // 加载成功回调
            newScript.onerror = reject; // 加载失败回调
          }));
        } else {
          // 如果没有 src 属性，执行内联脚本
          newScript.textContent = scripts[i].innerHTML; // 提取脚本内容
          document.body.appendChild(newScript); // 直接插入并执行
        }

        // 添加到文档中以触发加载
        if (newScript.src) {
          document.body.appendChild(newScript);
        }
      }

      // 等待所有引用的脚本加载完成
      Promise.all(promises)
        .then(() => {
          console.log("所有脚本加载完成");
        })
        .catch(err => {
          console.error("某些脚本加载失败", err);
        });
    }
  }, [siteConfig.site_script]);

  return <>
  </>
}