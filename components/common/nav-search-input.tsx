"use client"
import {Input} from "antd";
import React, {useState} from "react";
import {SearchOutlined, SendOutlined} from "@ant-design/icons";
import {useRouter} from "next/navigation";

export default function NavSearchInput() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [value, setValue] = useState("")
  const handleSearch = () => {
    if (value) {
      router.push(`/explore?search=Y&keyword=${value}`)
    }
  }
  return <>
    <div
      aria-hidden
      style={{
        display: 'flex',
        alignItems: 'center',
        marginInlineEnd: 24,
      }}
      onMouseDown={(e) => {
        e.stopPropagation();
        e.preventDefault();
      }}
      onKeyDown={(e) => {
        if (e.key === "Enter") {
          //回车
          if (value) {
            handleSearch()
          }
        }
      }}
    >
      <Input
        value={value}
        onChange={(e) => setValue(e.target.value)}
        placeholder="接口搜索"
        prefix={<SearchOutlined onClick={handleSearch}/>}
        suffix={<SendOutlined onClick={handleSearch}/>}
      />
    </div>
  </>
}