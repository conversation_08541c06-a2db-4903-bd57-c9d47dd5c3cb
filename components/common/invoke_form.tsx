"use client"
import {
  ProFormDependency,
  ProFormGroup,
  ProFormList,
  ProFormRadio,
  ProFormSelect,
  ProFormText, ProFormTextArea
} from "@ant-design/pro-components";
import {Divider} from "antd";
import type {Rule} from "rc-field-form/lib/interface";

export function RouteForm() {

}

export function SimpleParamsForm(props: {
  field: string[],
  title: string,
  nameExtra?: string
  valueExtra?: string
  nameLabel?: string,
  valueLabel?: string,
  extra?: string,
  rules?: Rule[],
  showType?: boolean
}) {
  return <>
    <ProFormList name={props.field} label={props.title}
      //@ts-ignore
                 rules={props.rules}>
      <ProFormGroup>
        <ProFormText label={props.nameLabel ? props.nameLabel : '参数名称'} name={'name'} rules={[{required: true}]}/>
        <ProFormText label={props.valueLabel ? props.valueLabel : '参数值'} name={'value'} rules={[{required: true}]}
                     extra={props.valueExtra ? props.valueExtra : `支持引用参数，如：name参数，则填写为： \${name\}`}/>
        {props.showType &&
          <ProFormSelect initialValue={'string'} extra={''} label={'数值类型'} name={'type'} valueEnum={{
            string: "字符串",
            integer: '数值',
            boolean: "布尔值",
          }}></ProFormSelect>}
      </ProFormGroup>
    </ProFormList>
  </>
}


export function ReflectForm() {
  return <>
    <ProFormText extra={'请输入控制器类名，如：ApiController, Api\\Controller'} label={'控制器'}
                 rules={[{required: true}]} name={['config', 'class']}/>
    <ProFormText label={'方法名'} rules={[{required: true}]} name={['config', 'method']}/>
  </>
}

export function HttpForm() {
  return <>
    <ProFormText label={'HTTP请求地址'}
                 rules={[{required: true}]} name={['config', 'url']}/>
    <ProFormSelect label={'HTTP请求方式'} valueEnum={{
      GET: "GET",
      POST: "POST",
      DELETE: "DELETE",
      PUT: "PUT",
    }} rules={[{required: true}]} name={['config', 'method']}/>
    <ProFormRadio.Group extra={<>
    </>} label={'请求内容类型'} name={['config', 'content_type']} initialValue={'json'} valueEnum={{
      json: "json",
      form_params: "form_params",
      multipart: "multipart",
    }}></ProFormRadio.Group>

    <Divider/>
    <SimpleParamsForm field={['config', 'query']} title='Query请求参数'></SimpleParamsForm>
    <Divider/>
    <SimpleParamsForm field={['config', 'header']} title='Header请求参数'></SimpleParamsForm>
    <Divider/>
    <SimpleParamsForm field={['config', 'body']} title='Body请求参数'></SimpleParamsForm>
    <Divider/>
    <ProFormRadio.Group initialValue={'CUSTOM'} valueEnum={{
      CUSTOM: "参数映射",
      NONE: "原样返回",
      FUNC: "自定义函数",

    }} name={['config', 'return_response']} label={'响应结果'}>
    </ProFormRadio.Group>
    <ProFormDependency name={["config"]}>
      {({config}) => {
        if (config.return_response === 'CUSTOM') {
          return <SimpleParamsForm rules={[{required: true, message: "请输入响应参数映射"}]}
                                   field={['config', 'response']}
                                   title='响应参数映射'
                                   valueExtra={'实际响应参数的键值，如：${name},支持多级 ${data.name}'}></SimpleParamsForm>
        }else if(config.return_response === "FUNC"){
          return <ProFormTextArea name={['config','response_func']} label={'自定义函数'} rules={[{required:true}]}/>
        }
        return <></>
      }}
    </ProFormDependency>

    <ProFormList label={'响应成功判断'} name={['config', 'success_check']}>

      <ProFormGroup>
        <ProFormSelect label={'响应位置'} initialValue={'status_code'} name={['type']}
                       valueEnum={{
                         "status_code": "状态码",
                         "header": "返回头",
                         "body": "返回数据",
                       }}>
        </ProFormSelect>
        <ProFormDependency name={['type']}>
          {({type}) => {
            if (type == 'status_code') {
              return null;
            }
            return <>
              <ProFormText width={'xs'} label={'参数名'} placeholder={'参数名称'} name={['name']}></ProFormText>
            </>
          }}
        </ProFormDependency>
        <ProFormSelect label={'条件'} initialValue={'='} placeholder={'条件判断'} name={['operator']}
                       rules={[{required: true}]} valueEnum={{
          "=": "=",
          ">": ">",
          "<": "<",
          ">=": ">=",
          "<=": "<=",
        }}></ProFormSelect>
        <ProFormText width={'xs'} label={'参数值'} extra={'用于判断响应的值是否等于输入的值'} initialValue={200}
                     name={['value']} rules={[{required: true}]}></ProFormText>
        <ProFormText label={'失败提示'} placeholder={'如果请求失败接口返回的message消息'}
                     extra={'如果请求失败接口返回的message消息'} initialValue={''} name={['help']}
                     rules={[{required: false}]}></ProFormText>
      </ProFormGroup>
    </ProFormList>

    <ProFormTextArea name={['config', 'options']} label={'请求 Options'}
                     extra={'请求options，参考 guzzle'}></ProFormTextArea>
  </>
}

export function TencentForm() {
  return <>
    <ProFormText label={'请求接口域名'} name={['config', 'host']}
                 extra={'腾讯云API Host,不同的服务host会不同，如：tag.tencentcloudapi.com'} rules={[{required: true}]}/>
    <ProFormText name={['config', 'action']} label={'动作Action'} rules={[{required: true}]}></ProFormText>
    <ProFormText name={['config', 'ak']} label={'秘钥ID'} rules={[{required: true}]}/>
    <ProFormText name={['config', 'sk']} label={'秘钥key'} rules={[{required: true}]}/>
    <ProFormText name={['config', 'version']} label={'接口版本号Version'}
                 extra={'不同的接口有不同的版本，如：2018-08-13'}/>
    <ProFormText name={['config', 'region']} label={'服务地区Region'} extra={'区域，不是必填，如：ap-beijing'}/>
    <Divider/>

    <SimpleParamsForm showType={true} field={['config', 'body']} title='输入参数'
                      extra={'排除掉公共参数的入参参数'}></SimpleParamsForm>
    <ProFormRadio.Group initialValue={'CUSTOM'} valueEnum={{
      CUSTOM: "参数映射",
      NONE: "原样返回",
      FUNC: "自定义函数",

    }} name={['config', 'return_response']} label={'响应结果'}/>
    <ProFormDependency name={["config"]}>
      {({config}) => {
        if (config.return_response === 'CUSTOM') {
          return <SimpleParamsForm rules={[{required: true, message: "请输入响应参数映射"}]}
                                   field={['config', 'response']}
                                   title='响应参数映射'
                                   valueExtra={'实际响应参数的键值，如：${name},支持多级 ${data.name}'}></SimpleParamsForm>
        }else if(config.return_response === "FUNC"){
          return <ProFormTextArea name={['config','response_func']} label={'自定义函数'} rules={[{required:true}]}/>
        }
        return <></>
      }}
    </ProFormDependency>
  </>
}


export function AliyunForm() {
  return <>
    <ProFormText label={'请求接口域名'} name={['config', 'host']}
                 extra={'阿里云 Host,不同的服务host会不同，如：ocr-api.cn-hangzhou.aliyuncs.com'}
                 rules={[{required: true}]}/>
    <ProFormText name={['config', 'ak']} label={'秘钥ID'} rules={[{required: true}]}/>
    <ProFormText name={['config', 'sk']} label={'秘钥key'} rules={[{required: true}]}/>
    <ProFormText name={['config', 'version']} label={'接口版本号'} extra={'不同的接口有不同的版本，如：2018-08-13'}/>
    <ProFormText name={['config', 'region']} label={'服务地区'} extra={'区域，不是必填，如：ap-beijing'}/>
    <Divider/>

    <SimpleParamsForm field={['config', 'body']} title='输入参数' extra={'排除掉公共参数的入参参数'}></SimpleParamsForm>

    <ProFormRadio.Group initialValue={'CUSTOM'} valueEnum={{
      CUSTOM: "参数映射",
      NONE: "原样返回",
      FUNC: "自定义函数",
    }} name={['config', 'return_response']} label={'响应结果'}/>
    <ProFormDependency name={["config"]}>
      {({config}) => {
        if (config.return_response === 'CUSTOM') {
          return <SimpleParamsForm rules={[{required: true, message: "请输入响应参数映射"}]}
                                   field={['config', 'response']}
                                   title='响应参数映射'
                                   valueExtra={'实际响应参数的键值，如：${name},支持多级 ${data.name}'}></SimpleParamsForm>
        }else if(config.return_response === "FUNC"){
          return <ProFormTextArea name={['config','response_func']} label={'自定义函数'} rules={[{required:true}]}/>
        }
        return <></>
      }}
    </ProFormDependency>
  </>
}

export function BaiduForm() {
  return <>
    <ProFormText label={'HTTP接口地址不能为空'} name={['config', 'url']} rules={[{required: true}]}/>
    <ProFormText name={['config', 'app_id']} label={'AppID'} rules={[{required: true}]}/>
    <ProFormText name={['config', 'api_key']} label={'ApiKey'} rules={[{required: true}]}/>
    <ProFormText name={['config', 'secret_key']} label={'SecretKey'} rules={[{required: true}]}/>
    <ProFormSelect initialValue={'GET'} name={['config', 'method']} label={'HTTP请求方式'} valueEnum={{
      GET: "GET",
      POST: "POST",
    }}></ProFormSelect>
    <ProFormRadio.Group extra={<>
    </>} label={'请求内容类型'} name={['config', 'content_type']} initialValue={'json'} valueEnum={{
      json: "json",
      form_params: "form_params",
      multipart: "multipart",
    }}></ProFormRadio.Group>
    <Divider/>


    <SimpleParamsForm field={['config', 'body']} title='输入参数' extra={'排除掉公共参数的入参参数'}></SimpleParamsForm>
    <ProFormRadio.Group initialValue={'CUSTOM'} valueEnum={{
      CUSTOM: "参数映射",
      NONE: "原样返回",
      FUNC: "自定义函数",
    }} name={['config', 'return_response']} label={'响应结果'}/>
    <ProFormDependency name={["config"]}>
      {({config}) => {
        if (config.return_response === 'CUSTOM') {
          return <SimpleParamsForm rules={[{required: true, message: "请输入响应参数映射"}]}
                                   field={['config', 'response']}
                                   title='响应参数映射'
                                   valueExtra={'实际响应参数的键值，如：${name},支持多级 ${data.name}'}></SimpleParamsForm>
        }else if(config.return_response === "FUNC"){
          return <ProFormTextArea name={['config','response_func']} label={'自定义函数'} rules={[{required:true}]}/>
        }
        return <></>
      }}
    </ProFormDependency>
  </>
}