"use client"

import {ProF<PERSON>List, ProFormSwitch} from "@ant-design/pro-form";
import {ProCard, ProForm, ProFormSelect, ProFormText} from "@ant-design/pro-components";

export function ParamForm(props: {
  field: string,
  showRules?:boolean
}) {
  return <>
    <ProFormList name={props.field} initialValue={[
      {
        name: "",
        data_type: "string",
        is_required: true,
        description: "",
        default: "",
        example: "",
      }
    ]}>
      <ProForm.Group>
        <ProFormText name={'name'} placeholder={'参数名称'} label={'参数名称'} rules={[{required: true}]}/>
        <ProFormSelect name={'data_type'} initialValue={'string'} label={'数据类型'} valueEnum={{
          string: "字符串",
          integer: "数值",
          boolean: "布尔值",
          array: "数组",
          object: "对象"
        }} rules={[{required: true}]}/>
        <ProFormSwitch initialValue={'false'}  name={'is_required'} label={'是否必填'}></ProFormSwitch>
        <ProFormText name={'description'} placeholder={'参数描述'} initialValue={''} label={'参数描述'}/>
        <ProFormText name={'default'} placeholder={'默认值'} initialValue={''} label={'默认值'}/>
        <ProFormText name={'example'} placeholder={'示例值'} initialValue={''} label={'示例'}/>
        {props.showRules && <ProFormText name={'rules'} initialValue={''} label={'验证规则'}/>}
      </ProForm.Group>
    </ProFormList>
  </>
}