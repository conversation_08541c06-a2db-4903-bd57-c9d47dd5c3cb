"use client"

import '@wangeditor/editor/dist/css/style.css' // 引入 css

import React, { useState, useEffect } from 'react'
import { Editor, Toolbar } from '@wangeditor/editor-for-react'
import { IDomEditor, IEditorConfig, IToolbarConfig } from '@wangeditor/editor'
import { env } from "@/env";
import Cookies from "js-cookie";
import { AccessTokenKey } from "@/config/data";

interface SimpleEditorProps {
  value?: string;
  onChange?: (v: string) => void
  placeholder?: string;
}

export default function SimpleWandEditor({ value, onChange, placeholder }: SimpleEditorProps) {

  // editor 实例
  const [editor, setEditor] = useState<IDomEditor | null>(null)   // TS 语法
  // const [editor, setEditor] = useState(null)                   // JS 语法

  // 编辑器内容
  const [html, setHtml] = useState(value)


  // 工具栏配置
  const toolbarConfig: Partial<IToolbarConfig> = {}  // TS 语法

  // 编辑器配置
  const editorConfig: Partial<IEditorConfig> = {    // TS 语法
    placeholder: '请输入内容...',
    MENU_CONF: {
      uploadImage: {
        server: env.NEXT_PUBLIC_API_URL + '/admin/common/upload',
        fieldName: "file",
        maxFileSize: 10 * 1024 * 1024,
        allowedFileTypes: ['image/*'],
        // 自定义增加 http  header
        headers: {
          "Authorization": "Bearer " + Cookies.get(AccessTokenKey)
        },
        //@ts-ignore
        customInsert: function (res: any, insertFn) {
          insertFn(res.data.url, "", "")
        }
      }
    }
  }

  // 及时销毁 editor ，重要！
  useEffect(() => {
    return () => {
      if (editor == null) return
      editor.destroy()
      setEditor(null)
    }
  }, [editor])
  return (
    <>
      <div style={{ border: '1px solid #ccc', zIndex: 100 }} className='prose w-full'>
        <Toolbar
          editor={editor}
          defaultConfig={toolbarConfig}
          mode="default"
          style={{ borderBottom: '1px solid #ccc' }}
        />
        <Editor
          defaultConfig={editorConfig}
          value={value}
          onCreated={setEditor}
          //@ts-ignore
          onChange={editor => onChange(editor.getHtml())}
          mode="default"
          style={{ height: '500px', width: "100%", overflowY: 'hidden' }}
        />
      </div>
    </>
  )
}