"use client"
import {Dropdown, DropDownProps} from 'antd';
import React from 'react';
import clsx from 'clsx';


export type HeaderDropdownProps = {
  overlayClassName?: string;
  placement?: 'bottomLeft' | 'bottomRight' | 'topLeft' | 'topCenter' | 'topRight' | 'bottomCenter';
} & Omit<DropDownProps, 'overlay'>;

const HeaderDropdown: React.FC<HeaderDropdownProps> = ({overlayClassName: cls, ...props}) => {
  return <Dropdown overlayClassName={clsx(cls)} {...props} />;
};

export default HeaderDropdown;