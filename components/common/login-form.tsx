"use client"

import { useR<PERSON><PERSON>, useSearchParams } from "next/navigation";
import { useSiteConfig } from "@/components/provider/site-provider";
import { SiteConfig } from "@/app/types";
import { CSSProperties, useEffect, useRef, useState } from "react";
import {
  CaptFieldRef, LoginForm,
  ProConfigProvider,
  ProForm,
  ProFormCaptcha,
  ProFormText,
  setAlpha
} from "@ant-design/pro-components";
import { useGeeTest } from "react-geetest-v4";
import { Card, Divider, message, Modal, Space, Tabs, theme } from "antd";
import { signIn } from "next-auth/react";
import ImageCaptcha, { ImageCaptchaValueProps } from "@/components/common/image-captcha";
import { forgetPassword, getThirdProviders, sendSms, userBindThird } from "@/lib/api";
import {
  AlipayCircleOutlined,
  LockOutlined,
  MobileOutlined, QqCircleFilled,
  TaobaoCircleOutlined,
  <PERSON><PERSON><PERSON><PERSON>cleOutlined
} from "@ant-design/icons";
import Link from "next/link";
import { ICONFONT } from "@/components/antd/icon";
import { env } from "@/env";

export interface LoginFormCommonProps {
  handleOk?: () => void
}

export default function LoginFormCommon({ handleOk }: LoginFormCommonProps) {
  const searchParams = useSearchParams()
  const router = useRouter()
  const siteConfig = useSiteConfig() as SiteConfig

  const captchaRef = useRef<CaptFieldRef | null | undefined>();
  const forgetCaptchaRef = useRef<CaptFieldRef | null | undefined>();
  const bindCaptchaRef = useRef<CaptFieldRef | null | undefined>();

  const forgetRef = useRef(false)
  const bindFormRef = useRef(false)
  const [forgetVisible, setForgetVisible] = useState(false)
  const [currentProvider, setCurrentProvider] = useState('')

  const [providers, setProviders] = useState([])

  // @ts-ignore
  const { captcha, state } = useGeeTest(siteConfig.geetest_captcha_id || "", {
    product: 'bind',
    protocol: 'https://',
    //@ts-ignore
    containerId: 'geetest-captcha',
    onError: function (error) {
      message.error(error.msg)
    },
  });

  const [loginForm] = ProForm.useForm()
  const [forgetForm] = ProForm.useForm()
  const [bindForm] = ProForm.useForm()

  const { token } = theme.useToken();
  const [imageCaptchaOpen, setImageCaptchaOpen] = useState(false)

  const [loginType, setLoginType] = useState('password');
  const iconStyles: CSSProperties = {
    marginInlineStart: '16px',
    color: setAlpha(token.colorTextBase, 0.2),
    fontSize: '24px',
    verticalAlign: 'middle',
    cursor: 'pointer',
  };
  const handleCodeLogin = async (values: any) => {
    const res = await signIn("credentials", {
      type: loginType,
      redirect: false,
      ...values
    })
    if (res === undefined) {
      message.warning('登录失败，请重新尝试')
      return
    }
    if (res.ok) {
      message.success("登录成功")
      handleOk && handleOk()
      if (searchParams.has('callbackUrl')) {
        //@ts-ignore
        router.push(searchParams.get("callbackUrl") || '/')
      } else if (window.location.pathname.includes("auth/login")) {
        router.push('/')
      } else {
        router.refresh()
      }
      return
    }
    message.warning(res?.error || '登录失败，请重新尝试')
  }

  const handleShowCaptcha = async () => {
    if (siteConfig.captcha_type === 'image') {
      setImageCaptchaOpen(true)
    } else if (siteConfig.captcha_type === 'geetest') {
      captcha?.showCaptcha()
    }
  }
  useEffect(() => {
    if (state === 'ready') {
      captcha?.onSuccess(async () => {
        const result = captcha?.getValidate()
        void handleSendSms(result)
      })
    }
  }, [state])

  useEffect(() => {
    getThirdProviders().then((data) => {
      //@ts-ignore
      setProviders(data)
    })
  }, []);

  const handleImageCaptchaSubmit = async (v: ImageCaptchaValueProps) => {
    await handleSendSms(v)
  }
  const handleSendSms = async (captcha: any) => {
    try {
      const phone = bindFormRef.current ? bindForm.getFieldValue("phone") : (forgetRef.current ? forgetForm.getFieldValue("phone") : loginForm.getFieldValue("phone"))
      const event = bindFormRef.current ? "bind_third" : (forgetRef.current ? "forget" : "login")
      const res = await sendSms({
        phone: phone,
        event: event,
        captcha: captcha,
        provider: currentProvider,
      })
      message.success("发送成功")
      bindFormRef.current ? bindCaptchaRef.current?.startTiming() : (forgetRef.current ? forgetCaptchaRef.current?.startTiming() : captchaRef.current?.startTiming())
    } catch (e) {
      //@ts-ignore
      message.error(e.message)
    }
  }

  const [thirdData, setThirdData] = useState()
  const [showBindPhoneOpen, setBindPhoneOpen] = useState(false)
  const openThirdModal = (provider: string) => {
    // 1. 添加标记防止重复处理
    let isProcessing = false;

    const openWin = window.open(env.NEXT_PUBLIC_API_URL + `/frontend/auth/oauth/redirect/${provider}`,
      `Login ${provider}`,
      'height=500,width=500,top=0,left=0,toolbar=no,menubar=no,scrollbars=no,resizable=no,location=no,status=no'
    );

    const receiveMessage = async function (event) {
      // 2. 检查消息来源
      // if(event.origin !== env.NEXT_PUBLIC_API_URL) {
      //   return;
      // }

      const data = event.data;
      // 3. 防止重复处理
      if (!isProcessing && data && 'user' in data) {
        try {
          isProcessing = true;

          if (!data.user) {
            setThirdData(data);
            setBindPhoneOpen(true);
          } else {
            const res = await signIn("third", {
              redirect: false,
              ...data.user_info
            });

            if (res?.ok) {
              message.success("登录成功");
              handleOk && handleOk();
              if (searchParams.has('callbackUrl')) {
                router.push(searchParams.get("callbackUrl") || '/');
              } else if (window.location.pathname.includes("auth/login")) {
                router.push('/');
              } else {
                router.refresh();
              }
            } else {
              message.warning(res?.error || '登录失败，请重新尝试');
            }
          }
        } finally {
          isProcessing = false;
        }
      }
    }

    // 4. 使用 AbortController 管理事件监听
    const controller = new AbortController();
    window.addEventListener('message', receiveMessage, {
      signal: controller.signal
    });

    // 5. 监听窗口关闭事件清理监听器
    const checkWindow = setInterval(() => {
      if (openWin.closed) {
        clearInterval(checkWindow);
        controller.abort();
      }
    }, 500);

    // 6. 组件卸载时清理
    return () => {
      console.log("close")
      controller.abort();
      clearInterval(checkWindow);
      if (!openWin.closed) {
        openWin.close();
      }
    }
  }

  const handleBindThird = () => {
    userBindThird({
      phone: bindForm.getFieldValue("phone"),
      code: bindForm.getFieldValue("code"),
      ...thirdData.data
    }).then(async (res) => {
      console.log(res)
      const loginRes = await signIn("third", {
        redirect: false,
        ...res
      });
      message.success("绑定成功")
      setBindPhoneOpen(false)
      setCurrentProvider('')
      if (loginRes?.ok) {
        message.success("登录成功")
        handleOk && handleOk()
        if (searchParams.has('callbackUrl')) {
          router.push(searchParams.get("callbackUrl") || '/');
        } else if (window.location.pathname.includes("auth/login")) {
          router.push('/');
        } else {
          router.refresh();
        }
      }

    }).catch((e) => {
      message.error(e.message || "绑定失败，请重试")
    })
  }
  useEffect(() => {
    bindFormRef.current = showBindPhoneOpen
  }, [showBindPhoneOpen]);
  useEffect(() => {
    forgetRef.current = forgetVisible
  }, [forgetVisible]);

  // useEffect(() => {
  //   const cleanup = openThirdModal(provider);
  //   return cleanup;
  // }, []);

  return <>
    <div>
      <ProConfigProvider hashed prefixCls={'api'} token={{}} autoClearCache>
        {!forgetVisible && <div>
          <div className={'p-0'}>
            <LoginForm form={loginForm} onFinish={async (values) => {
              await handleCodeLogin(values)
            }} containerStyle={{
              paddingInline: 0
            }} actions={<>
              <Space>
                其他登录方式
                {providers && providers.length > 0 && providers.map((e, index) => {
                  return <div title={e.name} onClick={() => {
                    setCurrentProvider(e.provider)
                    openThirdModal(e.provider)
                  }} key={index} style={iconStyles}>
                    {e.icon.includes("http") ? <img src={e.icon} className={'w-[24px] h-[24px]'} /> : <ICONFONT size={24} type={e.icon} />}
                  </div>
                })}
              </Space>
            </>}>
              <Tabs activeKey={loginType} onTabClick={setLoginType} items={[
                {
                  tabKey: "code",
                  label: "快捷登录/注册",
                  key: 'code'
                },
                {
                  tabKey: "password",
                  label: "账号密码登录",
                  key: 'password'
                }
              ]} />
              {loginType === 'code' && (<div>
                <ProFormText
                  fieldProps={{
                    size: 'large',
                    prefix: <MobileOutlined className={'prefixIcon'} />,
                  }}
                  name="phone"
                  placeholder={'手机号'}
                  rules={[
                    {
                      required: true,
                      message: '请输入手机号！',
                    },
                    {
                      pattern: /^1\d{10}$/,
                      message: '手机号格式错误！',
                    },
                  ]}
                />
                <ProFormCaptcha
                  fieldRef={captchaRef}
                  phoneName={'phone'}
                  fieldProps={{
                    size: 'large',
                    prefix: <LockOutlined className={'prefixIcon'} />,
                  }}
                  captchaProps={{
                    size: 'large',
                  }}
                  placeholder={'请输入验证码'}
                  captchaTextRender={(timing, count) => {
                    if (timing) {
                      return `${count} ${'获取验证码'}`;
                    }
                    return '获取验证码';
                  }}
                  name="code"
                  rules={[
                    {
                      required: true,
                      message: '请输入验证码！',
                    },
                  ]}
                  onGetCaptcha={async () => {
                    await handleShowCaptcha()
                    throw new Error("wait")
                    // message.success('获取验证码成功！验证码为：1234');
                  }}
                />
              </div>)}
              {loginType === 'password' && (<div>
                <ProFormText rules={[
                  {
                    required: true,
                    message: '请输入登录账号！',
                  },
                ]} placeholder={'请输入手机号'} fieldProps={{
                  size: "large"
                }} label={'账号'} name={'phone'} />
                <ProFormText.Password fieldProps={{
                  size: "large"
                }} rules={[
                  {
                    required: true,
                    message: '请输入密码！',
                  },
                ]} label={'密码'} name={'password'} />
              </div>)}
              <div
                style={{
                  marginBlockEnd: 24,
                }}
              >
                {loginType === 'password' && <a
                  className={'mb-2'}
                  style={{
                    float: 'right',
                  }}
                  onClick={() => {
                    setForgetVisible(true)
                  }}
                >
                  忘记密码
                </a>}
              </div>
            </LoginForm>
          </div>
          <div className={'mt-2'}>
            <div className='text-center !text-xs text-xs  text-[#999]'>
              <div className="">
                登录即表示已阅读并同意 <Link href={siteConfig.service_agreement || ""} target={"_blank"}
                  className='text-xs'>《用户协议》</Link>和
                <Link href={siteConfig.privacy_policy || ""} target={"_blank"}
                  className='text-xs'>《隐私政策》</Link>
              </div>
              <div>
                未注册的手机号或第三方账号验证后将自动创建新账号
              </div>
            </div>
          </div>
        </div>}
        {forgetVisible && <div>
          <div className={''}>
            <div className={'text-center text-xl'}>重置密码</div>
            <Divider className={'p-0 m-0'} />
            <LoginForm form={forgetForm} submitter={{
              submitButtonProps: {},
              searchConfig: {
                submitText: "提交"
              }
            }} onFinish={async (values) => {
              try {
                await forgetPassword(values)
                message.success("重置密码成功")
                setForgetVisible(false)
              } catch (e) {
                message.error(e.message || "更新失败")
              }
            }}>
              <ProFormText label={'手机号'} name={'phone'}
                rules={[{ required: true }]}
                fieldProps={{ style: { marginBottom: 16 } }}
              ></ProFormText>
              <ProFormCaptcha
                fieldRef={forgetCaptchaRef}
                phoneName={'phone'}
                fieldProps={{
                  prefix: <LockOutlined className={'prefixIcon'} />,
                  style: { marginBottom: 16 }
                }}
                captchaProps={{}}
                placeholder={'请输入验证码'}
                captchaTextRender={(timing, count) => {
                  if (timing) {
                    return `${count} ${'获取验证码'}`;
                  }
                  return '获取验证码';
                }}
                name="code"
                rules={[
                  {
                    required: true,
                    message: '请输入验证码！',
                  },
                ]}
                onGetCaptcha={async () => {
                  await handleShowCaptcha()
                  throw new Error("wait")
                  // message.success('获取验证码成功！验证码为：1234');
                }}
              />
              <ProFormText.Password
                label={'新密码'}
                name={'password'}
                rules={[{ required: true }]}
                fieldProps={{ style: { marginBottom: 24 } }}
              />
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <a
                  onClick={() => {
                    setForgetVisible(false)
                  }}
                >
                  返回登录
                </a>
              </div>
            </LoginForm>
          </div>
        </div>}
      </ProConfigProvider>
      <ImageCaptcha open={imageCaptchaOpen} setOpen={setImageCaptchaOpen} onChange={handleImageCaptchaSubmit} />
      <div id={'geetest-captcha'}></div>
      <div id={'geetest-captcha-forget'}></div>
      <Modal open={showBindPhoneOpen} title={'绑定手机号'} onOk={async () => {
        await bindForm.validateFields()
        //登录绑定
        handleBindThird()
      }} onCancel={() => {
        setBindPhoneOpen(false)
      }}>
        <div className={'mb-2'}>请先绑定已有账号或者新输入新手机号注册绑定</div>
        <Card className={'p-6'}>
          <ProForm form={bindForm} submitter={false}>
            <ProFormText
              fieldProps={{
                prefix: <MobileOutlined className={'prefixIcon'} />,
              }}
              name="phone"
              placeholder={'手机号'}
              rules={[
                {
                  required: true,
                  message: '请输入手机号！',
                },
                {
                  pattern: /^1\d{10}$/,
                  message: '手机号格式错误！',
                },
              ]}
            />
            <ProFormCaptcha

              fieldRef={bindCaptchaRef}
              phoneName={'phone'}
              fieldProps={{
                prefix: <LockOutlined className={'prefixIcon'} />,
              }}
              captchaProps={{}}
              placeholder={'请输入验证码'}
              captchaTextRender={(timing, count) => {
                if (timing) {
                  return `${count} ${'获取验证码'}`;
                }
                return '获取验证码';
              }}
              name="code"
              rules={[
                {
                  required: true,
                  message: '请输入验证码！',
                },
              ]}
              onGetCaptcha={async () => {
                await handleShowCaptcha()
                throw new Error("wait")
              }}
            />
          </ProForm>
        </Card>
      </Modal>
    </div>
  </>
}