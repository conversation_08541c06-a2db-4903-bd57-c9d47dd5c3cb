"use client"
import React, {useState, useEffect} from 'react';
import {DatePicker, TimePicker} from 'antd';
import dayjs, {Dayjs} from 'dayjs';

interface DateTimeRangePickerProps {
  onChange?: (value: (string | null)[]) => void;
  value?: (string | null)[];
}

const DateTimeRangePicker: React.FC<DateTimeRangePickerProps> = ({onChange, value}) => {
  const [dateValue, setDateValue] = useState<Dayjs | null>(null);
  const [startTimeValue, setStartTimeValue] = useState<Dayjs | null>(null);
  const [endTimeValue, setEndTimeValue] = useState<Dayjs | null>(null);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    if (value) {
      setDateValue(value[0] ? dayjs(value[0]) : null);
      setStartTimeValue(value[1] ? dayjs(value[1], 'HH:mm') : null);
      setEndTimeValue(value[2] ? dayjs(value[2], 'HH:mm') : null);
    }
  }, [value]);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const handleDateChange = (date: Dayjs | null) => {
    setDateValue(date);
    notifyChange(date, startTimeValue, endTimeValue);
  };

  const handleStartTimeChange = (time: Dayjs | null) => {
    setStartTimeValue(time);
    notifyChange(dateValue, time, endTimeValue);
  };

  const handleEndTimeChange = (time: Dayjs | null) => {
    setEndTimeValue(time);
    notifyChange(dateValue, startTimeValue, time);
  };

  const notifyChange = (date: Dayjs | null, start: Dayjs | null, end: Dayjs | null) => {
    const newValue = [
      date ? date.format('YYYY-MM-DD') : null,
      start?.format('HH:mm') || "00:00",
      end?.format('HH:mm') || "23:59",
    ];
    if (onChange) {
      onChange(newValue);
    }
  };

  if (isMobile) {
    // 移动端垂直布局
    return (
      <div style={{ width: '100%', display: 'flex', flexDirection: 'column', gap: '8px' }}>
        <DatePicker
          value={dateValue}
          onChange={handleDateChange}
          style={{ width: '100%' }}
        />
        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
          <TimePicker
            value={startTimeValue}
            format="HH:mm"
            onChange={handleStartTimeChange}
            style={{ width: '100%' }}
          />
          <span style={{ textAlign: 'center', margin: '4px 0' }}>至</span>
          <TimePicker
            value={endTimeValue}
            format="HH:mm"
            onChange={handleEndTimeChange}
            style={{ width: '100%' }}
          />
        </div>
      </div>
    );
  }

  // PC端水平布局
  return (
    <div style={{ width: '100%', display: 'flex', alignItems: 'center', gap: '8px', whiteSpace: 'nowrap', flexWrap: 'nowrap' }}>
      <DatePicker
        value={dateValue}
        onChange={handleDateChange}
        style={{ width: '180px', flexShrink: 0 }}
      />
      <div style={{ display: 'inline-flex', alignItems: 'center', gap: '8px', whiteSpace: 'nowrap', flexShrink: 0 }}>
        <TimePicker
          value={startTimeValue}
          format="HH:mm"
          onChange={handleStartTimeChange}
          style={{ width: '110px' }}
        />
        <span style={{ margin: '0 4px' }}>至</span>
        <TimePicker
          value={endTimeValue}
          format="HH:mm"
          onChange={handleEndTimeChange}
          style={{ width: '110px' }}
        />
      </div>
    </div>
  );
};

export default DateTimeRangePicker;
