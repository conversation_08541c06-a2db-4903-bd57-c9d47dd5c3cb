"use client"

import {
  ProLayout, ProLayoutProps,
} from '@ant-design/pro-components';
import React from "react";
import {Dropdown} from "antd";
import {LogoutOutlined} from "@ant-design/icons";
import {routes, settings} from "@/config/admin";
import NavSearchInput from "@/components/common/nav-search-input";
import Link from "next/link";
import {usePathname, useRouter} from "next/navigation";
import {useSiteConfig} from "@/components/provider/site-provider";
import {SiteConfig} from "@/app/types";
import {User} from "next-auth";


export interface LayoutProps {
  children: React.ReactNode
  user: User | null | undefined
}

// @ts-ignore
const ProLayoutC: React.FC<React.PropsWithChildren<LayoutProps>> = ({children, user}) => {
  const pathname = usePathname()
  const siteConfig = useSiteConfig() as SiteConfig

  const router = useRouter()

  //@ts-ignore
  return <ProLayout {...settings}  onMenuHeaderClick={() => {
    router.push("/")
  }}  title={siteConfig.site_name} logo={"/images/logo/icon_logo.svg"} siderWidth={216}
                    bgLayoutImgList={[
                      {
                        src: 'https://img.alicdn.com/imgextra/i2/O1CN01O4etvp1DvpFLKfuWq_!!6000000000279-2-tps-609-606.png',
                        left: 85,
                        bottom: 100,
                        height: '303px',
                      },
                      {
                        src: 'https://img.alicdn.com/imgextra/i2/O1CN01O4etvp1DvpFLKfuWq_!!6000000000279-2-tps-609-606.png',
                        bottom: -68,
                        right: -45,
                        height: '303px',
                      },
                      {
                        src: 'https://img.alicdn.com/imgextra/i3/O1CN018NxReL1shX85Yz6Cx_!!6000000005798-2-tps-884-496.png',
                        bottom: 0,
                        left: 0,
                        width: '331px',
                      },
                    ]}  appList={[]} location={{pathname: pathname}} avatarProps={{
    src: 'https://gw.alipayobjects.com/zos/antfincdn/efFD%24IOql2/weixintupian_20170331104822.jpg',
    title: user?.nickname,
    size: 'small',
    render: function (props, dom) {
      return (
        <Dropdown
          menu={{
            items: [
              {
                key: 'logout',
                icon: <LogoutOutlined/>,
                label: '退出登录',
              },
            ],
          }}
        >
          {dom}
        </Dropdown>
      )
    }
  }} route={{
    path: "/",
    children: routes
  }} actionsRender={(props) => {
    if (props.isMobile) return [];
    if (typeof window === 'undefined') return [];
    return [
      props.layout !== 'side' && document.body.clientWidth > 1024 ? (
        <NavSearchInput/>
      ) : undefined,
    ];
  }} menuItemRender={(item, dom) => (
    <Link href={item.path || '/'}>
      {dom}
    </Link>
  )} suppressHydrationWarning>
    {children}
  </ProLayout>
}


export default ProLayoutC