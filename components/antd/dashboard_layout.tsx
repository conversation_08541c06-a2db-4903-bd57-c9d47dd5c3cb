"use client"

import {
  ProLayout,
} from '@ant-design/pro-components';
import React, { useState } from "react";
import { Dropdown, message } from "antd";
import { LogoutOutlined } from "@ant-design/icons";
import { routes, settings } from "@/config/dashboard";
import NavSearchInput from "@/components/common/nav-search-input";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useSiteConfig } from "@/components/provider/site-provider";
import { SiteConfig } from "@/app/types";
import { LayoutProps } from "@/components/antd/admin_layout";
import { UserRound, ShieldCheck, LogOut } from "lucide-react";
import { apiRequest } from "@/lib/axios";
import Cookies from "js-cookie";
import { AccessTokenKey } from "@/config/data";
import { signOut } from "next-auth/react";
import GiftRedeemModal from "@/components/gift/gift-redeem-modal";
import { ICONFONT } from './icon';

const ProLayoutC: React.FC<React.PropsWithChildren<LayoutProps>> = ({ children, user }) => {
  const pathname = usePathname()
  const siteConfig = useSiteConfig() as SiteConfig
  const router = useRouter()
  const [giftModalOpen, setGiftModalOpen] = useState(false);

  //@ts-ignore
  return <ProLayout onMenuHeaderClick={() => {
    router.push("/")
  }} {...settings} title={siteConfig.site_name} logo={"/images/logo/icon_logo.svg"} siderWidth={216}
    bgLayoutImgList={[
      {
        src: 'https://img.alicdn.com/imgextra/i2/O1CN01O4etvp1DvpFLKfuWq_!!6000000000279-2-tps-609-606.png',
        left: 85,
        bottom: 100,
        height: '303px',
      },
      {
        src: 'https://img.alicdn.com/imgextra/i2/O1CN01O4etvp1DvpFLKfuWq_!!6000000000279-2-tps-609-606.png',
        bottom: -68,
        right: -45,
        height: '303px',
      },
      {
        src: 'https://img.alicdn.com/imgextra/i3/O1CN018NxReL1shX85Yz6Cx_!!6000*********-2-tps-884-496.png',
        bottom: 0,
        left: 0,
        width: '331px',
      },
    ]} appList={[]} location={{ pathname: pathname }} avatarProps={{
      src: 'https://gw.alipayobjects.com/zos/antfincdn/efFD%24IOql2/weixintupian_20170331104822.jpg',
      title: user?.nickname,
      size: 'small',
      render: function (props, dom) {
        return (
          <Dropdown
            menu={
              {
                items: [
                  {
                    key: '/dashboard/account/index',
                    icon: <UserRound size={16} />,
                    label: <Link href={`/dashboard/account/index`}>个人中心</Link>,
                  },
                  {
                    key: '/vip',
                    icon: <ICONFONT type="icon-vip" style={{ fontSize: "16px" }} size={24} />,
                    label: <Link href={'/vip'}>会员中心</Link>,
                  },
                  {
                    key: "/dashboard/data/api",
                    icon: <ICONFONT type="icon-API" style={{ fontSize: "16px" }} size={24} />,
                    label: <Link href={'/dashboard/data/api'}>API管理</Link>
                  },
                  {
                    key: "/dashboard/data/token",
                    icon: <ICONFONT type="icon-API-KEY" style={{ fontSize: "16px" }} size={24} />,
                    label: <Link href={'/dashboard/data/token'}>token管理</Link>
                  },
                  {
                    key: '/dashboard/secure/index',
                    icon: <ShieldCheck size={16} />,
                    label: <Link href={'/dashboard/secure/index'}>安全设置</Link>
                  },
                  {
                    type: 'divider' as const,
                    key: 'hr',
                  },
                  {
                    key: 'logout',
                    icon: <LogOut size={16} />,
                    label: <span onClick={async () => {
                      try {
                        await apiRequest("/frontend/auth/logout")
                        Cookies.remove(AccessTokenKey)
                        await signOut({
                          callbackUrl: "/"
                        })
                      } catch (e) {
                        await signOut({
                          callbackUrl: "/"
                        })
                      }
                      return
                    }}>退出登录</span>,
                  },
                ],
              }}
          >
            {dom}
          </Dropdown>
        )
      }
    }} route={{
      path: "/",
      children: routes,
      // hideInMenu:true,
    }} actionsRender={(props) => {
      if (props.isMobile) return [];
      if (typeof window === 'undefined') return [];
      return [
        props.layout !== 'side' && document.body.clientWidth > 1024 ? (
          <NavSearchInput />
        ) : undefined,
      ];
    }} menu={{}} menuItemRender={(item, dom) => {
      if (item.key === 'gifts') {
        return <span onClick={() => {
          setGiftModalOpen(true);
        }}>{dom}</span>
      }
      return (
        <Link href={item.path || '/'}>
          {dom}
        </Link>
      )
    }} suppressHydrationWarning>
    {children}
    <GiftRedeemModal
      open={giftModalOpen}
      onClose={() => setGiftModalOpen(false)}
    />
  </ProLayout>
}


export default ProLayoutC