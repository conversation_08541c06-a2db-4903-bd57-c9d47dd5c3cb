"use client"

import {useState} from "react";

export const Prices = ()=>{
  const [selectedPlan, setSelectedPlan] = useState('basic');

  const plans = [
    { id: 'basic', name: 'Basic', price: 49, isPopular: false },
    { id: 'popular', name: 'Popular', price: 99, isPopular: true },
    { id: 'enterprise', name: 'Enterprise', price: 149, isPopular: false },
  ];
  return (
    <div className="mt-6 space-y-8 xl:mt-12">
      {plans.map(plan => (
        <div
          key={plan.id}
          className={`flex items-center justify-between max-w-2xl px-8 py-4 mx-auto border cursor-pointer rounded-xl ${selectedPlan === plan.id ? 'border-blue-500' : 'dark:border-gray-700'}`}
          onClick={() => setSelectedPlan(plan.id)}
        >
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className={`w-5 h-5 ${selectedPlan === plan.id ? 'text-blue-600' : 'text-gray-400'} sm:h-9 sm:w-9`} viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>

            <div className="flex flex-col items-center mx-5 space-y-1">
              <h2 className="text-lg font-medium text-gray-700 sm:text-2xl dark:text-gray-200">{plan.name}</h2>
            </div>
          </div>

          <h2 className={`text-2xl font-semibold ${selectedPlan === plan.id ? 'text-blue-600' : 'text-gray-500'} sm:text-3xl dark:text-gray-300`}>
            ${plan.price} <span className="text-base font-medium">/Month</span>
          </h2>
        </div>
      ))}
    </div>
  );
}