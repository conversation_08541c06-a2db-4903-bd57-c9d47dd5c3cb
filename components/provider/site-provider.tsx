"use client"
import {createContext, useContext} from "react";
import {SiteConfig} from "@/app/types";

const SiteContext = createContext({});
export const SiteProvider = ({
                               children,
                               siteConfig
                             }: Readonly<{
  children: React.ReactNode;
  siteConfig: SiteConfig
}>) => {
  return (
    <SiteContext.Provider value={siteConfig}>
      {children}
    </SiteContext.Provider>
  );
}

export const useSiteConfig = () => {
  return useContext(SiteContext) as SiteConfig;
};