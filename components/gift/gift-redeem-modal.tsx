import { Modal, Input, message, Form, Typography } from 'antd';
import { useState } from 'react';
import axiosInstance, { apiRequest } from "@/lib/axios";
import { GiftOutlined } from '@ant-design/icons';
import { useRouter } from 'next/router';

const { Text, Paragraph } = Typography;

interface GiftRedeemModalProps {
    open: boolean;
    onClose: () => void;
}

const GiftRedeemModal: React.FC<GiftRedeemModalProps> = ({ open, onClose }) => {
    const [loading, setLoading] = useState(false);
    const [form] = Form.useForm();

    const handleRedeem = async (values: { code: string }) => {
        try {
            setLoading(true);
            const res = await axiosInstance.post('/frontend/user/redeem', {
                code: values.code
            });
            message.success('礼品兑换成功');
            form.resetFields();
            onClose();
        } catch (error) {
            message.error(error?.message || '兑换失败，请检查礼品码是否正确');
        } finally {
            setLoading(false);
        }
    };

    return (
        <Modal
            title={
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <GiftOutlined style={{ color: '#1890ff', fontSize: '20px' }} />
                    <span>礼品兑换</span>
                </div>
            }
            open={open}
            onCancel={onClose}
            onOk={() => form.submit()}
            confirmLoading={loading}
            okText="兑换"
            cancelText="取消"
            width={480}
            destroyOnClose
        >
            <div style={{ padding: '12px 0' }}>
                <Paragraph>
                    <Text strong>欢迎使用礼品兑换功能！</Text>
                </Paragraph>
                <Paragraph type="secondary" style={{ marginBottom: '16px' }}>
                    请输入您收到的礼品兑换码，兑换后礼品将直接发放到您的账户中。
                </Paragraph>
                <Form
                    form={form}
                    onFinish={handleRedeem}
                    layout="vertical"
                >
                    <Form.Item
                        name="code"
                        rules={[
                            { required: true, message: '请输入礼品码' },
                            {
                                len: 15,
                                message: '礼品码必须为15位'
                            },
                            {
                                pattern: /^[A-Za-z0-9]{15}$/,
                                message: '礼品码只能包含字母和数字'
                            }
                        ]}
                        help="礼品码为15位字母和数字组合，请注意区分大小写"
                    >
                        <Input
                            placeholder="请输入礼品码，例如：GIFT202401234567"
                            size="large"
                            style={{ letterSpacing: '1px' }}
                            maxLength={15}
                            showCount
                        />
                    </Form.Item>
                </Form>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                    如遇到问题，请联系客服获取帮助
                </Text>
            </div>
        </Modal>
    );
};

export default GiftRedeemModal; 