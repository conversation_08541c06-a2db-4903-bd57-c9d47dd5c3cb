import type {Config} from "tailwindcss";

const config: Config = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic":
          "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
        enterprisePC: 'url(/images/enterprise-pc.jpg)',
        enterpriseMobile: 'url(/images/enterprise-mobile.png)'
      },
      backgroundColor: {
        primary: "#165DFF",
        data1: "#4080ff",
        blue5: "#4096ff",
        blue6: "#1677ff",
      },
      backgroundSize: {
        autoFull: 'auto 100%'
      },
      boxShadow: {
        xs: "0 5px 15px rgba(30,101,235,.1)",
      }
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
};
export default config;
