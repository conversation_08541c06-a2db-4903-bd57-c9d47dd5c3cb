{"name": "nextjs-alapi-antdesign", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest --config jest.config.js"}, "dependencies": {"@ant-design/nextjs-registry": "^1.0.0", "@ant-design/pro-components": "^2.7.9", "@formkit/auto-animate": "^0.8.2", "@t3-oss/env-nextjs": "^0.10.1", "@uiw/react-json-view": "^2.0.0-alpha.24", "@visactor/react-vchart": "^1.12.8", "@visactor/vchart": "^1.12.8", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-react": "^1.0.6", "ace-builds": "^1.34.2", "antd": "^5.21.0", "antd-style": "^3.6.2", "axios": "^1.7.2", "clsx": "^2.1.1", "copy-to-clipboard": "^3.3.3", "httpsnippet": "^3.0.4", "js-cookie": "^3.0.5", "lucide-react": "^0.454.0", "next": "^14.2.25", "next-auth": "^4.24.7", "rc-resize-observer": "^1.4.0", "react": "^18", "react-ace": "^11.0.1", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18", "react-geetest-v4": "^1.1.3", "zod": "^3.23.8"}, "devDependencies": {"@babel/preset-env": "^7.26.0", "@babel/preset-typescript": "^7.26.0", "@tailwindcss/typography": "^0.5.15", "@types/jest": "^29.5.14", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.3", "jest": "^29.7.0", "postcss": "^8", "tailwindcss": "^3.4.1", "ts-jest": "^29.2.5", "typescript": "^5"}}