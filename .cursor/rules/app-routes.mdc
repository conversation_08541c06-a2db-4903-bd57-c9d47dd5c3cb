---
description: 
globs: 
alwaysApply: false
---
# Next.js 应用路由结构

这个项目使用Next.js的App Router路由系统，其中每个文件夹代表一个路由，特殊文件(如page.tsx, layout.tsx)定义路由行为。括号中的文件夹名表示路由分组，不影响URL路径。

## 主要应用路由

- **app/(auth)/** - 认证相关路由
- **app/(dashboard)/** - 仪表板相关路由（需登录权限）
- **app/(admin)/** - 管理员相关路由（需管理员权限）
- **app/(marketing)/** - 面向用户的营销页面
- **app/(service)/** - 服务相关路由

## 关键文件

- [app/layout.tsx](mdc:app/layout.tsx) - 根布局组件，适用于所有页面
- [app/actions.ts](mdc:app/actions.ts) - 服务器操作(Server Actions)
- [app/types.d.ts](mdc:app/types.d.ts) - 应用类型定义
- [middleware.ts](mdc:middleware.ts) - 处理路由中间件，用于认证和授权控制

## API路由

API路由位于`app/api`目录，遵循Next.js 14的路由约定。

### 重要概念

- 每个`route.ts`文件导出HTTP方法处理程序(GET, POST, PUT, DELETE)
- API响应使用Next.js的Response API返回
- 认证和授权在API路由层或中间件层处理
