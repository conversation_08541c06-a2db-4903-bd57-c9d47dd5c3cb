---
description: 
globs: 
alwaysApply: false
---
# 认证与鉴权

本项目使用Next-Auth进行认证，并通过中间件实现路由级别的权限控制。

## 认证系统

- 基于[next-auth](mdc:https:/next-auth.js.org)库实现
- 支持多种认证方式：凭证(用户名/密码)、OAuth等
- 用户会话通过JWT或数据库会话管理

## 认证流程

1. 用户在`/auth/signin`页面登录
2. 成功后重定向到仪表板或之前尝试访问的页面
3. 认证状态通过Provider在全应用范围内共享

## 路由保护

通过[middleware.ts](mdc:middleware.ts)中间件实现：

1. 公开路由：任何人可访问
2. 受保护路由：需要登录才能访问，未登录重定向到登录页
3. 管理员路由：需要管理员权限才能访问

## 实现文件

- **app/(auth)** - 包含登录、注册等认证相关页面
- **components/provider/auth-provider.tsx** - 认证提供者组件（如果存在）
- **middleware.ts** - 路由保护中间件

## API权限控制

API端点通过以下方式保护：

1. 直接在路由处理程序中验证会话
2. 使用高阶函数包装路由处理程序
3. 通过中间件拦截未认证请求
