---
description:
globs:
alwaysApply: false
---
# 组件结构

本项目使用模块化组件结构，组件按功能分类并存放在相应目录中。

## 组件类别

- **antd/** - Ant Design组件自定义与封装
- **common/** - 通用UI组件，可在整个应用中复用
- **layout/** - 页面布局组件，如页头、页脚、侧边栏等
- **provider/** - React Context提供者组件
- **api/** - API相关组件，如请求表单、响应展示等
- **gift/** - 礼品/奖励相关组件
- **explore/** - 探索/发现页面组件
- **index/** - 首页相关组件
- **purchase/** - 购买流程相关组件

## 组件编写规范

1. 组件使用TypeScript类型安全
2. 尽可能使用函数式组件和React Hooks
3. 组件样式优先使用TailwindCSS
4. 复杂UI组件基于Ant Design扩展
5. 通用逻辑抽象到自定义hooks中
6. 组件文件名使用PascalCase命名法

## 关键组件

探索components目录以了解更多：
- **components/layout/** - 包含主要布局结构
- **components/provider/** - 包含全局状态提供者
- **components/common/** - 包含通用UI组件
