---
description: 
globs: 
alwaysApply: false
---
# 项目结构概述

这个项目是一个使用Next.js、Ant Design和API集成的应用程序。

## 主要目录结构

- **app/** - Next.js应用程序主目录
  - **(auth)/** - 认证相关页面
  - **(dashboard)/** - 仪表板相关页面
  - **(admin)/** - 管理员相关页面
  - **(marketing)/** - 营销相关页面
  - **(service)/** - 服务相关页面
  - **api/** - API路由

- **components/** - 组件目录
  - **antd/** - Ant Design组件定制
  - **common/** - 通用组件
  - **layout/** - 布局组件
  - **provider/** - 提供者组件

- **utils/** - 工具函数
- **lib/** - 核心库代码
- **public/** - 静态资源
- **style/** - 样式文件

## 主要技术栈

- [Next.js](mdc:https:/nextjs.org) - React框架
- [Ant Design](mdc:https:/ant.design) - UI组件库
- [TailwindCSS](mdc:https:/tailwindcss.com) - 实用优先的CSS框架
- [TypeScript](mdc:https:/www.typescriptlang.org) - 类型安全的JavaScript超集

## 关键文件

- [package.json](mdc:package.json) - 项目依赖和脚本
- [next.config.mjs](mdc:next.config.mjs) - Next.js配置
- [tailwind.config.ts](mdc:tailwind.config.ts) - TailwindCSS配置
- [app/layout.tsx](mdc:app/layout.tsx) - 根布局组件
- [middleware.ts](mdc:middleware.ts) - Next.js中间件
