---
description: 
globs: 
alwaysApply: false
---
# 开发指南

本项目是基于Next.js 14、TypeScript、Ant Design和TailwindCSS的全栈应用。

## 开发环境设置

1. 安装依赖：
   ```bash
   npm install
   ```

2. 本地开发：
   ```bash
   npm run dev
   ```

3. 构建生产版本：
   ```bash
   npm run build
   ```

4. 运行生产版本：
   ```bash
   npm run start
   ```

5. 运行测试：
   ```bash
   npm run test
   ```

## 技术栈

- [Next.js](mdc:https:/nextjs.org) - React全栈框架
- [TypeScript](mdc:https:/www.typescriptlang.org) - 类型安全
- [Ant Design](mdc:https:/ant.design) - UI组件库
- [TailwindCSS](mdc:https:/tailwindcss.com) - 原子化CSS框架
- [Jest](mdc:https:/jestjs.io) - 测试框架

## 代码规范

1. **文件命名**：
   - 组件使用PascalCase: `Button.tsx`
   - 工具函数使用camelCase: `formatDate.ts`

2. **目录结构**：
   - 页面在`app`目录下按路由组织
   - 组件在`components`目录下按功能分类
   - 工具函数在`utils`目录

3. **样式**：
   - 优先使用TailwindCSS
   - 复杂样式使用CSS Modules或Ant Design的样式方案

4. **状态管理**：
   - 本地状态使用React useState/useReducer
   - 跨组件状态使用React Context
   - 服务器状态使用React Server Components和Server Actions

5. **API调用**：
   - 使用axios进行API调用
   - API端点定义在app/api目录下

## Docker部署

项目包含[Dockerfile](mdc:Dockerfile)用于容器化部署。
