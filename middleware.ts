import {withAuth} from "next-auth/middleware";
import {AccessTokenKey} from "@/config/data";
import Cookies from "js-cookie";

export default withAuth(
  {
    callbacks: {
      async authorized({token,req}) {
        //权限判断
        if(!token){
          return  false
        }

        const pathname =  req.nextUrl.pathname
        //@ts-ignore
        return !(pathname.includes("/admin") && token?.is_admin !== true);

      },

    },
    pages: {
      error: "/",
      signIn: "/"
    },
  },
)

export const config = {
  matcher: ['/admin/:path*', '/dashboard/:path*','/purchase/:path*','/test/:path*']
}