import {HTTPSnippet, addTargetClient} from 'httpsnippet';
import {wxapp} from '@/utils/httpsnippet/target/wxapp';
import {uniapp} from '@/utils/httpsnippet/target/uniapp';

// @ts-expect-error
addTargetClient("javascript", wxapp)
// @ts-expect-error
addTargetClient("javascript", uniapp)

// @ts-expect-error
export function codeConvert(targetId: string, input, clientId = '') {

  const snippet = new HTTPSnippet(input);

  // @ts-expect-error
  return snippet.convert(targetId, clientId).toString()
}
