export const uniapp = {
  info: {
    key: 'uniapp',
    title: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    extname: '.js',
    default: 'request',
    description: "wxapp ",
    link: "https://developers.weixin.qq.com/miniprogram/dev/api/network/request/wx.request.html"
  },
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-expect-error
  convert: ({allHeaders, method, url, queryObj, postData}) => {

    const headerStr = JSON.stringify(allHeaders)
    let dataStr = '{}'
    switch (postData.mimeType) {
      case "application/json":
        dataStr = JSON.stringify(postData.jsonObj)
        break;
      case "application/x-www-form-urlencoded":
        dataStr = JSON.stringify(postData.paramsObj)
        break;
      default:
        dataStr = '{}'
    }
    let fullUrl = url
    if (queryObj) {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
      const searchParams = new URLSearchParams(queryObj)
      const queryString = searchParams.toString();
      fullUrl += '?' + queryString
    }

    return `uni.request({
      url: '${fullUrl}',
      method: '${method}',
      header: ${headerStr},
      data: ${dataStr},
      success(res) {
        console.log(res.data);
      },
      fail(error) {
        console.error(error);
      }
    });`;
  }
};