import { convertResponseParams } from '../helper'

describe('convertResponseParams', () => {
  
  test('应该正确处理没有嵌套的参数', () => {
    const input = [
      {
        id: 1,
        name: 'code',
        data_type: 'string',
        is_required: true,
        description: '状态码'
      },
      {
        id: 2,
        name: 'message',
        data_type: 'string',
        is_required: true,
        description: '提示信息'
      }
    ]
    
    const result = convertResponseParams(input)
    expect(result).toEqual(input)
  })

  test('应该正确处理一级嵌套参数', () => {
    const input = [
      {
        id: 1,
        name: 'data',
        data_type: 'object',
        is_required: true,
        description: '数据对象'
      },
      {
        id: 2,
        name: 'data.id',
        data_type: 'integer',
        is_required: true,
        description: '用户ID'
      },
      {
        id: 3,
        name: 'data.name',
        data_type: 'string',
        is_required: true,
        description: '用户名称'
      }
    ]
    
    const expected = [
      {
        id: 1,
        name: 'data',
        data_type: 'object',
        is_required: true,
        description: '数据对象',
        children: [
          {
            id: 2,
            name: 'id',
            data_type: 'integer',
            is_required: true,
            description: '用户ID'
          },
          {
            id: 3,
            name: 'name',
            data_type: 'string',
            is_required: true,
            description: '用户名称'
          }
        ]
      }
    ]
    
    const result = convertResponseParams(input)
    expect(result).toEqual(expected)
  })

  test('应该正确处理多级嵌套参数', () => {
    const input = [
      {
        id: 1,
        name: 'data',
        data_type: 'object',
        is_required: true,
        description: '数据对象'
      },
      {
        id: 2,
        name: 'data.user.id',
        data_type: 'integer',
        is_required: true,
        description: '用户ID'
      },
      {
        id: 3,
        name: 'data.user.profile.name',
        data_type: 'string',
        is_required: true,
        description: '用户名称'
      }
    ]
    
    const expected = [
      {
        id: 1,
        name: 'data',
        data_type: 'object',
        is_required: true,
        description: '数据对象',
        children: [
          {
            name: 'user',
            data_type: 'object',
            is_required: true,
            description: 'user对象',
            children: [
              {
                id: 2,
                name: 'id',
                data_type: 'integer',
                is_required: true,
                description: '用户ID'
              },
              {
                name: 'profile',
                data_type: 'object',
                is_required: true,
                description: 'profile对象',
                children: [
                  {
                    id: 3,
                    name: 'name',
                    data_type: 'string',
                    is_required: true,
                    description: '用户名称'
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
    
    const result = convertResponseParams(input)
    expect(result).toEqual(expected)
  })

  test('应该正确处理数组类型的参数', () => {
    const input = [
      {
        id: 1,
        name: 'items',
        data_type: 'array',
        is_required: true,
        description: '数据列表'
      },
      {
        id: 2,
        name: 'items.id',
        data_type: 'integer',
        is_required: true,
        description: '项目ID'
      },
      {
        id: 3,
        name: 'items.name',
        data_type: 'string',
        is_required: true,
        description: '项目名称'
      }
    ]
    
    const expected = [
      {
        id: 1,
        name: 'items',
        data_type: 'array',
        is_required: true,
        description: '数据列表',
        children: [
          {
            id: 2,
            name: 'id',
            data_type: 'integer',
            is_required: true,
            description: '项目ID'
          },
          {
            id: 3,
            name: 'name',
            data_type: 'string',
            is_required: true,
            description: '项目名称'
          }
        ]
      }
    ]
    
    const result = convertResponseParams(input)
    expect(result).toEqual(expected)
  })

  test('应该正确处理异常情况并返回空数组', () => {
    // 测试 null 输入
    expect(convertResponseParams(null)).toEqual([]);
    
    
    // 测试非数组输入
    //@ts-ignore
    expect(convertResponseParams('not an array')).toEqual([]);
    
    // 测试空数组
    expect(convertResponseParams([])).toEqual([]);
  })
}) 