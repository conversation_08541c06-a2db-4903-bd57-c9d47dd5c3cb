import {ApiNode} from "@/app/types";

export function jsonToQueryString(json: Record<string, string>) {
  return Object.keys(json).map(function (key) {
    return encodeURIComponent(key) + '=' + encodeURIComponent(json[key] ?? '');
  }).join('&');
}

//@ts-ignore
export function debounce<T extends (...args: any[]) => Promise<void>>(func: T, wait: number): (...args) => Promise<void> {
  let timeout: ReturnType<typeof setTimeout> | null;

  //@ts-ignore
  return async function (
    ...args
  ): Promise<void> {
    if (timeout !== null) {
      clearTimeout(timeout);
    }

    return new Promise((resolve) => {
      timeout = setTimeout(async () => {
        //@ts-ignore
        await func.apply(this, args);
        resolve();
      }, wait);
    });
  };
}

// 节流函数
//@ts-ignore
export function throttle<T extends (...args: any[]) => Promise<void>>(func: T, wait: number): (...args) => Promise<void> {
  let timeout: ReturnType<typeof setTimeout> | null = null;
  let lastArgs: null = null;

  return async function (...args): Promise<void> {
    if (timeout === null) {
      //@ts-ignore
      await func.apply(this, args);
      timeout = setTimeout(async () => {
        if (lastArgs) {
          //@ts-ignore
          await func.apply(this, lastArgs);
          lastArgs = null;
        }
        timeout = null;
      }, wait);
    } else {
      //@ts-ignore
      lastArgs = args;
    }
  };
}


export function getApiUrl(domain: string, path: string) {
  return `https://${domain}/api${path}`
}

export function getHttpMethod(method: string) {
  //默认都为POST
  return method === 'GET' ? "GET" : "POST"
}


export function GenerateSnippetOptions(node: ApiNode, url: string, method?: string, request_params?: object) {
  // const method = getHttpMethod(node.request_method)
  if (!method) {
    method = getHttpMethod(node.request_method)
  }
  let params = []
  if (request_params) {
    for (const param in request_params) {
      params.push({
        "name": param,
        //@ts-ignore
        "value": request_params[param],
      })
    }
  } else {
    params.push({
      "name": "token",
      "value": "请在token管理生成token",
    })
    for (let requestParam of node.request_params) {
      params.push({
        "name": requestParam.name,
        "value": requestParam.default,
      })
    }
  }
  //判断params是否有token ,没有则加上


  const options = {
    method: method,
    url: url,
    httpVersion: 'HTTP/1.1',
    headers: [
      {"name": "Content-Type", "value": "application/json"},
    ]
  }
  if (method === 'GET') {
    //@ts-ignore
    options["queryString"] = params
  } else {
    //@ts-ignore
    let temp_param = {}
    for (const param of params) {
      //@ts-ignore
      temp_param[param.name] = param.value
    }
    //@ts-ignore
    options["postData"] = {
      "mimeType": "application/json",
      "text": JSON.stringify(temp_param),
    }
  }
  return options
}

export function JsonParse(value:any): object {
  try {
    return JSON.parse(value)
  } catch {
    return {}
  }
}

export function convertToMenuItems(data:any) {
  //@ts-ignore
  return data.map(item => {
    // 创建一个基本的菜单项对象
    const menuItem = {
      key: item.link,
      label: item.name,
      title: item.name,
      path: item.link || '',
    };

    // 如果存在子菜单项，则递归处理它们
    if (item.children && Array.isArray(item.children) && item.children.length > 0) {
      //@ts-ignore
      menuItem.children = convertToMenuItems(item.children);
    }

    return menuItem;
  });
}

/**
 * 将扁平的响应参数转换为树形结构
 * 支持多级嵌套，如 a.b.c.d 
 */
export function convertResponseParams(params: any[] | null): any[] {
  if (!params || !Array.isArray(params)) {
    return [];  // 返回空数组而不是 null
  }
  
  try {
    // 创建结果数组
    const result: any[] = [];
    // 创建临时Map存储所有节点
    const nodeMap = new Map();
    
    // 第一次遍历,找出所有顶级节点
    params.forEach(param => {
      if (!param.name.includes('.')) {
        const node = {...param};
        if (param.data_type === 'array' || param.data_type === 'object') {
          node.children = [];
        }
        result.push(node);
        nodeMap.set(param.name, node);
      }
    });

    // 第二次遍历,处理所有带点号的参数
    params.forEach(param => {
      if (param.name.includes('.')) {
        const parts = param.name.split('.');
        let currentNode = null;
        let parentPath = '';
        
        // 遍历路径的每一部分
        for (let i = 0; i < parts.length - 1; i++) {
          const currentPath = parentPath ? `${parentPath}.${parts[i]}` : parts[i];
          parentPath = currentPath;
          
          // 如果当前路径节点不存在,则创建一个新节点
          if (!nodeMap.has(currentPath)) {
            const newNode = {
              name: parts[i],
              data_type: 'object',
              children: [],
              is_required: true,
              description: `${parts[i]}对象`,
            };
            nodeMap.set(currentPath, newNode);
            
            // 将新节点添加到父节点的children中
            if (i === 0) {
              result.push(newNode);
            } else {
              const parentNode = nodeMap.get(parentPath.substring(0, parentPath.lastIndexOf('.')));
              if (parentNode && parentNode.children) {
                parentNode.children.push(newNode);
              }
            }
          }
          
          currentNode = nodeMap.get(currentPath);
          if (!currentNode.children) {
            currentNode.children = [];
          }
        }
        
        // 添加最终的叶子节点
        if (currentNode && currentNode.children) {
          const leafNode = {
            ...param,
            name: parts[parts.length - 1]
          };
          currentNode.children.push(leafNode);
        }
      }
    });

    // 如果没有发生任何转换,返回原数组
    if (result.length === 0) {
      return params;
    }

    // 清理空的children数组
    //@ts-ignore
    const cleanEmptyChildren = (nodes) => {
      //@ts-ignore
      return nodes.map(node => {
        if (node.children && node.children.length === 0) {
          const {children, ...rest} = node;
          return rest;
        }
        if (node.children) {
          return {
            ...node,
            children: cleanEmptyChildren(node.children)
          };
        }
        return node;
      });
    };

    return cleanEmptyChildren(result);
  } catch (e) {
    console.error('Convert response params error:', e);
    return params;  // 如果是数组就返回原数组
  }
}